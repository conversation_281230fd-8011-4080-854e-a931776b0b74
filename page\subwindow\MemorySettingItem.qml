import QtQuick
import "../framework"

Row {
    property string titleStr: title.text
    property int memoryType: 0
    property int memoryId: 0
    property int selectedMemoryType: dataMap["uiDataMap"]["selectedMemoryType"]
    property int selectedMemoryId: dataMap["uiDataMap"]["selectedMemoryId"]

    property bool uiMemoryEnable: memoryEnable
    property alias uiMemoryComment: comment.text

    property bool memoryEnable: dataMap["memorySettingType" + memoryType + "Index" + memoryId]["enable"]
    property string memoryComment: dataMap["memorySettingType" + memoryType + "Index" + memoryId]["name"]

    onUiMemoryEnableChanged: {
        if(uiMemoryEnable !== memoryEnable)
        {
            enabledBtn.checked = uiMemoryEnable
            disabledBtn.checked = !uiMemoryEnable
            commonCtrl.setMemoryEnable(memoryId, memoryType, uiMemoryEnable)
        }
    }

    onUiMemoryCommentChanged: {
        if(uiMemoryComment !== memoryComment)
        {
            commonCtrl.setMemoryName(memoryId, memoryType, uiMemoryComment)
        }
    }

    onMemoryEnableChanged: {
        if(uiMemoryEnable !== memoryEnable)
        {
            uiMemoryEnable = memoryEnable
        }
    }

    onMemoryCommentChanged: {
        if(uiMemoryComment !== memoryComment)
        {
            uiMemoryComment = memoryComment
        }
    }

    Text {
        id: title
        anchors.verticalCenter: parent.verticalCenter
        width: 36
        height: 8
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: titleStr
    }

    MyRadioButton {
        id: enabledBtn
        anchors.verticalCenter: parent.verticalCenter
        width: 80
        text: qsTr("Valid")
        checked: uiMemoryEnable
        enabled: (((selectedMemoryType !== memoryType) || (selectedMemoryId !== memoryId)) && (0 !== memoryId))

        onClicked: uiMemoryEnable = true
    }

    MyRadioButton {
        id: disabledBtn
        anchors.verticalCenter: parent.verticalCenter
        width: 80
        text: qsTr("Invalid")
        checked: !uiMemoryEnable
        enabled: enabledBtn.enabled

        onClicked: uiMemoryEnable = false
    }

    Rectangle {
        width: 208
        height: 24
        color: "#282828"
        border.width: 1
        border.color: "#646870"

        TextInput {
            id: comment
            anchors.fill: parent
            color: "#E8E8E8"
            leftPadding: 3
            rightPadding: leftPadding
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Qt.AlignVCenter
            text: memoryComment
            clip: true
            maximumLength: (/[^\u0000-\u00FF]/.test(text)) ? 8 : 32

            // onEditingFinished: uiMemoryComment = text
        }
    }

    Text {
        id: usingTxt
        anchors.verticalCenter: parent.verticalCenter
        width: 92
        height: 8
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        leftPadding: 8
        verticalAlignment: Text.AlignVCenter
        text: qsTr("(Selected)")
        visible: ((selectedMemoryType === memoryType) && (selectedMemoryId === memoryId))
    }
}
