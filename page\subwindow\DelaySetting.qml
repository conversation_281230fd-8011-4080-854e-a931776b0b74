import QtQuick
import QtQuick.Controls
import "../framework"

SubWindow {
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 1162
    contentHeight: 496
    titleStr: qsTr("Time Alignment")
    confirmVisible: false
    cancelStr: qsTr("Close")

    property int uiUnitId: unitId
    property int unitId: dataMap["delayUnit"]

    onClickCancel: clickClose()

    onUiUnitIdChanged: {
        if(uiUnitId !== unitId)
        {
            commonCtrl.setDelayUnit(uiUnitId)
        }
    }

    onUnitIdChanged: {
        uiUnitId = unitId
    }

    contentItem: Rectangle {
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"

        DelaySpeakersItem {
            anchors.left: parent.left
            anchors.leftMargin: 28
            anchors.top: parent.top
            anchors.topMargin: 68
        }

        Grid{
            anchors.right: parent.right
            anchors.rightMargin: 16
            anchors.top: parent.top
            anchors.topMargin: 44
            rows: 2
            rowSpacing: 6
            columns: 5
            columnSpacing: 6

            Repeater {
                model: 10

                DelayItem {
                    channel: modelData
                    delayUnit: uiUnitId
                }
            }
        }

        Row {
            anchors.right: parent.right
            anchors.rightMargin: 16
            anchors.top: parent.top
            anchors.topMargin: 16
            spacing: 18

            Text {
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                bottomPadding: 3
                text: qsTr("Unit")
            }

            MyRadioButton {
                text: "cm"
                checked: (0 === uiUnitId)

                onClicked: uiUnitId = 0
            }

            MyRadioButton {
                text: "inch"
                checked: (1 === uiUnitId)

                onClicked: uiUnitId = 1
            }

            MyRadioButton {
                text: "msec"
                checked: (2 === uiUnitId)

                onClicked: uiUnitId = 2
            }
        }
    }
}
