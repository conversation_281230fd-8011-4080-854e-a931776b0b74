#ifndef MID01HANDLER_H
#define MID01HANDLER_H

#include "DataHandlerAbstract.h"

#include "communication/aesapi.h"

class Mid01Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid01Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    QByteArray send01Data();
    QByteArray send03Data();
    QByteArray send08Data(const DeviceOperation0809 &data);
    QByteArray send09Data();

    const DeviceOperation01& getDeviceOperation01();
    const DeviceOperation03& getDeviceOperation03();
    const DeviceOperation0809& getDeviceOperation0809();

signals:
    void deviceTypeChanged(QString type);
    void mainVersionChanged(uint8_t version);
    void subVersionChanged(uint8_t version);

    void mainGainChanged(uint16_t gain);
    void mainMuteChanged(uint8_t isMute);
    void usbConnectedChanged(uint8_t isConnected);
    // void musicSourceChanged(uint8_t source);
    void btConnectedChanged(uint8_t isConnected);
    void aptxConnectedChanged(uint8_t isConnected);
    void uacConnectedChanged(uint8_t isConnected);

    void mainUnitSupportedChanged(uint8_t isSupported);
    void auxSupportedChanged(uint8_t isSupported);
    void btSupportedChanged(uint8_t isSupported);
    void spdifSupportedChanged(uint8_t isSupported);
    void usbAudioSupportedChanged(uint8_t isSupported);

private:
    void parse01Data(const QByteArray &data);
    void parse03Data(const QByteArray &data);
    void parse09Data(const QByteArray &data);

private:
    AesApi mAesApi;

    DeviceOperation01 mDeviceOperation01;
    DeviceOperation03 mDeviceOperation03;
    DeviceOperation0809 mDeviceOperation0809;
};

#endif // MID01HANDLER_H
