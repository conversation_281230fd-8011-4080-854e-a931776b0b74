#ifndef DATACENTERCONTROLLER_H
#define DATACENTERCONTROLLER_H

#include <QObject>
#include <QTimer>
#include <QMap>

#include "communication/USBManager.h"

#include "DataHandlerDef.h"
#include "Mid01Handler.h"
#include "Mid02Handler.h"
#include "Mid03Handler.h"
#include "Mid04Handler.h"
#include "Mid05Handler.h"
#include "Mid06Handler.h"

class DataCenterController : public QObject
{
    Q_OBJECT
private:
    explicit DataCenterController(QObject *parent = nullptr);

public:
    static DataCenterController& getInstance()
    {
        static DataCenterController instance;
        return instance;
    }

    // 创建操作码的辅助函数
    static OptCode MakeOptCode(bool needResponse, bool isResponse, ErrorCode errCode = ErrorCode::SUCCESS) {
        OptCode opt;
        opt.bits.syn = needResponse ? 1 : 0;
        opt.bits.ack = isResponse ? 1 : 0;
        opt.bits.reserve = 0;
        opt.bits.errcode = static_cast<uint8_t>(errCode);
        return opt;
    }

    void initData(int deviceLevel);

    static uint8_t calCrc8(const QByteArray &data);

    void updateDataByMemoryChange(int deviceLevel);
    void updateDataByEqTypeChange(int deviceLevel);
    void updateDataByEqResetChange(int deviceLevel);

    // interface for hid
    void connectRequest0101();
    void heartBeat0103();
    void setMusicSourceList0108(const DeviceOperation0809 &data);
    void getMusicSourceList0109();

    void setOutputChannel0201(const ChannelOperation0102 &data);
    void getOutputChannel0202(uint8_t channel);
    void setCrossOver0207(const ChannelOperation0708 &data);
    void getCrossOver0208(uint8_t channel);

    void setMainGain0301(const AudioOperation0102 &data);
    void getMainGain0302();
    void setMusicSource0303(const AudioOperation0304 &data);
    void getMusicSource0304();
    void setChannelMix0305(const AudioOperation0506 &data);
    void getChannelMix0306(uint8_t source, uint8_t channel, uint8_t num);
    void setEQ0307(const AudioOperation0708 &data);
    void getEQ0308(uint8_t channel, uint8_t band = 0x00, uint8_t num = EQ_BAND_MAX);
    void setEqLink0309(const AudioOperation09 &data);
    void setMemory030a(const AudioOperation0A0B &data);
    void getMemory030b(uint8_t type, uint8_t memory = 0x00, uint8_t num = MEMORY_MAX);
    void setMemoryCopy030c(const AudioOperation0C &data);
    void setDelayGroup030d(const AudioOperation0D &data);
    void setPreset030e(const AudioOperation0E0F &data);
    void getPreset030f();
    void setDisconnectBt0310();

    void setSystemConfig0403(const SystemOperation0304 &data);
    void getSystemConfig0404();
    void setSystemConfig0407(const SystemOperation0708 &data);
    void getInputGain0408();
    void setInitialization0409();
    void querySaveData(uint16_t index);
    int sendLoadData(const QByteArray &data, int deviceLevel);

    void getVersion0501();
    void upgradeDspRemote0502(uint8_t type, const QByteArray &data);    //type: 0:MCU; 2:line
    void getUpgradeDspRemoteRet0503();

    void setRemoteBrightness0601(const RemoteOperation0102 &data);
    void getRemoteBrightness0602();
    void setRemoteModel0603(const RemoteOperation0304 &data);
    void getRemoteModel0604();
    void setRemoteShortCut0605(const RemoteOperation0506 &data);
    void getRemoteShortCut0606();

    // interface for ui
    const DeviceOperation01& getDeviceOperation01();
    const DeviceOperation03& getDeviceOperation03();
    const DeviceOperation0809& getDeviceOperation0809();

    const QVector<ChannelOperation0102>& getChannelOperation0102Vector();
    const QVector<ChannelOperation0708>& getChannelOperation0708Vector();

    const AudioOperation0102& getAudioOperation0102();
    const AudioOperation0304& getAudioOperation0304();
    const QMap<uint8_t, QMap<uint8_t, QVector<Mix>>>& getAudioOperation0506Map();
    const QMap<uint8_t, QVector<EqParm>>& getAudioOperation0708Map();
    const QMap<uint8_t, QVector<MemorySetting>>& getAudioOperation0A0B();
    const AudioOperation0E0F& getAudioOperation0E0F();

    const SystemOperation0304& getSystemOperation0304();
    const SystemOperation0708& getSystemOperation0708();

public slots:
    void onReceiveMessage(const QByteArray &data);

signals:
    void receivedErrorSig(ErrorCode errorcode);

    // mid 01
    void deviceTypeChanged(QString type);
    void mainVersionChanged(uint8_t version);
    void subVersionChanged(uint8_t version);

    void mainGainChanged(uint16_t gain);
    void mainMuteChanged(uint8_t isMute);
    void usbConnectedChanged(uint8_t isConnected);
    // void musicSourceChanged(uint8_t source);
    void btConnectedChanged(uint8_t isConnected);
    void aptxConnectedChanged(uint8_t isConnected);
    void uacConnectedChanged(uint8_t isConnected);

    void mainUnitSupportedChanged(uint8_t isSupported);
    void auxSupportedChanged(uint8_t isSupported);
    void btSupportedChanged(uint8_t isSupported);
    void spdifSupportedChanged(uint8_t isSupported);
    void usbAudioSupportedChanged(uint8_t isSupported);

    // mid 02
    void outputChannelTypeChanged(uint8_t channel, uint16_t type);
    void outputChannelGainChanged(uint8_t channel, uint16_t gain);
    void outputChannelPositiveNegativeChanged(uint8_t channel, uint8_t positiveNegative);
    void outputChannelMuteChanged(uint8_t channel, uint8_t mute);
    void outputChannelDelayChanged(uint8_t channel, uint16_t delay);
    void outputChannelDelayGroupChanged(uint8_t channel, uint8_t group);
    void outputChannelEqSetChanged(uint8_t channel, uint8_t eqSet);
    void outputChannelEqTypeChanged(uint8_t channel, uint8_t eqType);
    void outputChannelLinkTypeChanged(uint8_t channel, uint8_t linkType);
    void outputChannelLinkChannelChanged(uint8_t channel, uint8_t linkChannel);

    void outputChannelHpFreqChanged(uint8_t channel, uint16_t hpFreq);
    void outputChannelHpSlopeChanged(uint8_t channel, uint8_t hpSlope);
    void outputChannelHpTypeChanged(uint8_t channel, uint8_t hpType);
    void outputChannelHpEnableChanged(uint8_t channel, uint8_t hpEnable);
    void outputChannelLpFreqChanged(uint8_t channel, uint16_t lpFreq);
    void outputChannelLpSlopeChanged(uint8_t channel, uint8_t lpSlope);
    void outputChannelLpTypeChanged(uint8_t channel, uint8_t lpType);
    void outputChannelLpEnableChanged(uint8_t channel, uint8_t lpEnable);

    // mid 03
    void dspGainChanged(uint8_t gain);
    void dspMuteChanged(uint8_t isMute);
    void bassLevelChanged(uint8_t level);
    void bassMuteChanged(uint8_t isMute);

    void currentSourceChanged(uint8_t source);
    void currentMemoryChanged(uint8_t memory);

    void mixGainChanged(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t gain);
    void mixEnableChanged(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t enable);

    void eqFreqChanged(uint8_t channel, uint8_t band, uint16_t freq);
    void eqQValueChanged(uint8_t channel, uint8_t band, uint16_t qValue);
    void eqGainChanged(uint8_t channel, uint8_t band, uint16_t gain);
    void eqTypeChanged(uint8_t channel, uint8_t band, uint8_t type);
    void eqEnableChanged(uint8_t channel, uint8_t band, uint8_t enable);

    void memoryEnableChanged(uint8_t type, uint8_t memoryId, uint8_t enable);
    void memoryNameChanged(uint8_t type, uint8_t memoryId, QString name);

    void presetModeChanged(uint8_t mode);
    void presetLevelChanged(uint8_t level);

    // mid 04
    void naviMixEnableChanged(uint8_t isEnabled);
    void naviSensitivityChanged(uint8_t sensitivity);
    void naviAttenuationChanged(uint8_t attenuation);
    void naviDurationChanged(uint8_t duration);
    void externalAttenuationChanged(uint8_t attenuation);
    void externalPolarityChanged(uint8_t polarity);
    void externalDspAttenuationChanged(uint8_t attenuation);
    void delayUnitChanged(uint8_t unit);

    void highGainChanged(uint8_t gain);
    void rcaGainChanged(uint8_t gain);
    void auxGainChanged(uint8_t gain);
    void btGainChanged(uint8_t gain);
    void spdifGainChanged(uint8_t gain);
    void usbGainChanged(uint8_t gain);

    void savePackageCountChanged(int count, const QByteArray data);
    void savePackageDataChanged(uint16_t index, const QByteArray data);
    void loadPackageFinished();

    // mid 05
    void dspMainVersionChanged(uint8_t version);
    void dspSubVersionChanged(uint8_t version);
    void remoteMainVersionChanged(uint8_t version);
    void remoteSubVersionChanged(uint8_t version);
    void upgradeError(uint8_t code);
    void upgradeProgress(uint8_t progress);
    void upgradeStatus(uint8_t status);


    // mid 06
    void remoteBrightnessChanged(uint8_t value);
    void remoteDimmerChanged(uint8_t value);
    void remotePolarityChanged(uint8_t value);
    void remoteDimmerBrightnessChanged(uint8_t value);
    void remoteModelChanged(uint8_t value);
    void remoteShortCutChanged(uint8_t id, uint8_t type, uint8_t memory);

private:
    void parseData(const FrameHead &header, const QByteArray &data);
    QByteArray packFrame(uint8_t mid, uint8_t sid, OptCode opt, QByteArray data);
    int unpackFrame(const QByteArray &data, FrameHead &header, QByteArray &detailData);

private:
    QTimer* mHeartBeatTimer;
    QMap<uint8_t, DataHandlerAbstract*> mDataHandlers;
    int mLoadPackageCount;
};

#endif // DATACENTERCONTROLLER_H
