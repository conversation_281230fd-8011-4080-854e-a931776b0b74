import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    id: root
    width: 238
    height: 150
    color: "#3C4048"
    border.width: 1
    border.color: "#5C6068"
    radius: 4

    property var sbassImgs: ["qrc:/Image/img_preset_sbass_1.png", "qrc:/Image/img_preset_sbass_2.png", "qrc:/Image/img_preset_sbass_3.png",
    "qrc:/Image/img_preset_sbass_4.png", "qrc:/Image/img_preset_sbass_5.png"]
    property var powerfulImgs: ["qrc:/Image/img_preset_power_1.png", "qrc:/Image/img_preset_power_2.png", "qrc:/Image/img_preset_power_3.png",
    "qrc:/Image/img_preset_power_4.png", "qrc:/Image/img_preset_power_5.png"]
    property var vocalImgs: ["qrc:/Image/img_preset_vocal_1.png", "qrc:/Image/img_preset_vocal_2.png", "qrc:/Image/img_preset_vocal_3.png",
    "qrc:/Image/img_preset_vocal_4.png", "qrc:/Image/img_preset_vocal_5.png"]
    property var naturalImgs: ["qrc:/Image/img_preset_natural_1.png", "qrc:/Image/img_preset_natural_2.png", "qrc:/Image/img_preset_natural_3.png",
    "qrc:/Image/img_preset_natural_4.png", "qrc:/Image/img_preset_natural_5.png"]
    property var flatImgs: ["qrc:/Image/img_preset_flat_1.png", "qrc:/Image/img_preset_flat_1.png", "qrc:/Image/img_preset_flat_1.png",
    "qrc:/Image/img_preset_flat_1.png", "qrc:/Image/img_preset_flat_1.png"]

    readonly property int fromValue: decimalToInt(-60, gainInput.decimalFactor)
    readonly property int toValue: decimalToInt(6, gainInput.decimalFactor)

    property int uiGain: gain
    property alias uiMute: muteBtn.checked
    property int uiPresetType: presetType
    property alias uiPresetLevel: presetLevelInput.value

    property int gain: dataMap["masterGain"] - 600
    property bool mute: dataMap["masterMute"]
    property int presetType: dataMap["presetMode"]
    property int presetLevel: dataMap["presetLevel"]

    function decimalToInt(realValue, factor) {
        return realValue * factor
    }

    onUiGainChanged: {
        gainInput.value = uiGain
        gainSlider.value = uiGain

        if(uiGain !== gain)
        {
            commonCtrl.setMasterGain(uiGain + 600)
        }
    }

    onUiMuteChanged: {
        if(uiMute !== mute)
        {
            commonCtrl.setMasterMute(uiMute)
        }
    }

    onUiPresetTypeChanged: {
        presetTypeCombobox.currentIndex = ((0 === uiPresetType) ? 4 : (uiPresetType - 1))

        if(uiPresetType !== presetType)
        {
            commonCtrl.setPresetMode(uiPresetType)
        }
    }

    onUiPresetLevelChanged: {
        if((uiPresetLevel - 1) !== presetLevel)
        {
            commonCtrl.setPresetLevel(uiPresetLevel - 1)
        }
    }

    onGainChanged: {
        uiGain = gain
    }

    onMuteChanged: {
        uiMute = mute
    }

    onPresetTypeChanged: {
        uiPresetType = presetType
    }

    onPresetLevelChanged: {
        uiPresetLevel = presetLevel + 1
    }

    Text {
        x: 10
        y: 9
        height: 16
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "Master Gain"
    }

    SpinBoxB {
        id: gainInput
        anchors.right: parent.right
        anchors.rightMargin: 10
        anchors.top: parent.top
        anchors.topMargin: 6
        width: 64
        height: 24

        from: fromValue
        to: toValue
        value: uiGain
        // stepSize: decimalFactor

        property int decimals: 1
        property real realValue: value / decimalFactor
        readonly property int decimalFactor: Math.pow(10, decimals)

        // validator: RegularExpressionValidator { regularExpression: /(-?\d{1,2}(\.\d?)?)(dB)?/ }
        validator: RegularExpressionValidator { regularExpression: /-?(\d{0,2}(\.\d?)?)?/ }
        unit: "dB"

        textFromValue: function(value, locale) {
            // return Number(value / decimalFactor).toLocaleString(locale, 'f', decimals) + "dB"
            // return (value / decimalFactor).toFixed(decimals).toString() + "dB"
            return (value / decimalFactor).toFixed(decimals).toString()
        }

        valueFromText: function(text, locale) {
            // let re = /(-?\d{1,2}(\.\d?)?)(dB)?/
            // return Number.fromLocaleString(locale, re.exec(text)[1]) * decimalFactor
            // return Number(re.exec(text)[1]) * decimalFactor
            return Number(text) * decimalFactor
        }

        onValueChanged: {
            uiGain = value
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                // case Qt.Key_Right:
                                {
                                    gainInput.increase()
                                    event.accepted = true
                                    break
                                }
                                case Qt.Key_Down:
                                // case Qt.Key_Left:
                                {
                                    gainInput.decrease()
                                    event.accepted = true
                                    break
                                }
                            }
                        }
    }

    ButtonB {
        id: muteBtn
        anchors.right: gainInput.left
        anchors.rightMargin: 5
        anchors.top: gainInput.top
        width: 24
        height: 24
        checkable: true
        checked: mute

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: muteBtn.checked ? "qrc:/Image/mute.png" : "qrc:/Image/unmute.png"
            }
        }
    }

    SliderB {
        id: gainSlider
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: gainInput.bottom
        anchors.topMargin: 6
        width: 132
        height: 30

        from: fromValue
        to: toValue
        value: uiGain
        stepSize: 1
        snapMode: Slider.SnapAlways

        onValueChanged: {
            uiGain = Math.round(value)
        }
    }

    ButtonB {
        id: gainMinusBtn
        anchors.right: gainSlider.left
        anchors.rightMargin: 8
        anchors.top: gainSlider.top
        anchors.topMargin: 3
        width: 24
        height: 24
        autoRepeat: true

        onClicked: {
            gainSlider.decrease()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_minus_bold.png"
            }
        }
    }

    ButtonB {
        id: gainPlusBtn
        anchors.left: gainSlider.right
        anchors.leftMargin: 8
        anchors.top: gainSlider.top
        anchors.topMargin: 3
        width: 24
        height: 24
        autoRepeat: true

        onClicked: {
            gainSlider.increase()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_plus_bold.png"
            }
        }
    }

    Rectangle {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: gainSlider.bottom
        anchors.topMargin: 5
        width: 226
        height:1
        color: "#5C6068"
    }

    Text {
        x: 10
        y: 72
        height: 16
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "Preset EQ"
    }

    ComboBoxB {
        id: presetTypeCombobox
        x: 10
        y: 92
        width: 120
        height: 24
        model: ["S.Bass", "Powerful", "Vocal", "Natural", "FLAT"]
        currentIndex: 4

        onCurrentIndexChanged: {
            if(-1 < currentIndex)
            {
                uiPresetType = (4 === currentIndex) ? 0 : (currentIndex + 1)
            }
        }
    }

    Text {
        x: 10
        y: 126
        height: 16
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "Level"
    }

    SpinBoxB {
        id: presetLevelInput
        anchors.right: presetTypeCombobox.right
        anchors.rightMargin: 28
        anchors.top: presetTypeCombobox.bottom
        anchors.topMargin: 6
        width: 28
        height: 24
        inputMethodHints: Qt.ImhDigitsOnly

        from: 1
        to: 5
        value: presetLevel + 1

        validator: RegularExpressionValidator { regularExpression: /\d?/ }

        textFromValue: function(value, locale) {
            return value.toString()
        }

        valueFromText: function(text, locale) {
            return Number(text)
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                // case Qt.Key_Right:
                                {
                                    event.accepted = true
                                    break
                                }
                                case Qt.Key_Down:
                                // case Qt.Key_Left:
                                {
                                    event.accepted = true
                                    break
                                }
                            }
                        }
    }

    ButtonB {
        id: presetLevelMinusBtn
        anchors.right: presetLevelInput.left
        anchors.verticalCenter: presetLevelInput.verticalCenter
        width: 28
        height: 24

        onClicked: {
            presetLevelInput.decrease()
        }

        Image {
            anchors.centerIn: parent
            source: "qrc:/Image/icn_btn_minus_bold.png"
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                case Qt.Key_Right:
                                {
                                    presetLevelInput.increase()
                                    break
                                }
                                case Qt.Key_Down:
                                case Qt.Key_Left:
                                {
                                    presetLevelInput.decrease()
                                    break
                                }
                            }
                        }
    }

    ButtonB {
        id: presetLevelPlusBtn
        anchors.left: presetLevelInput.right
        anchors.verticalCenter: presetLevelInput.verticalCenter
        width: 28
        height: 24

        onClicked: {
            presetLevelInput.increase()
        }

        Image {
            anchors.centerIn: parent
            source: "qrc:/Image/icn_btn_plus_bold.png"
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                case Qt.Key_Right:
                                {
                                    presetLevelInput.increase()
                                    break
                                }
                                case Qt.Key_Down:
                                case Qt.Key_Left:
                                {
                                    presetLevelInput.decrease()
                                    break
                                }
                            }
                        }
    }

    Image {
        anchors.left: presetTypeCombobox.right
        anchors.leftMargin: 6
        anchors.top: presetTypeCombobox.top

        source: {
            switch(uiPresetType) {
            case 0:
            {
                return flatImgs[uiPresetLevel - 1]
            }
            case 1:
            {
                return sbassImgs[uiPresetLevel - 1]
            }
            case 2:
            {
                return powerfulImgs[uiPresetLevel - 1]
            }
            case 3:
            {
                return vocalImgs[uiPresetLevel - 1]
            }
            case 4:
            {
                return naturalImgs[uiPresetLevel - 1]
            }
            }
        }
    }
}
