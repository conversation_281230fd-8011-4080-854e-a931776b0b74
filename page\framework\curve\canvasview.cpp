#include "canvasview.h"
#include <QPainter>
#include <QMouseEvent>
#include <QPainterPath>
#include <cmath>
#include <QDebug>
#include "common/common.h"
#ifdef __cplusplus
#include <complex>
using std::abs;
#endif
#include <vector>
#include <QTimer>
#include <QQmlEngine>

[[maybe_unused]] static std::vector<double> interpolateManualY(const QVector<double>& manualY, size_t n);

/**
 * @brief 构造函数
 * 初始化画布大小并连接信号槽
 */
CanvasView::CanvasView(QQuickItem* parent)
    : QQuickPaintedItem(parent)
    , m_model(nullptr)
    , m_needFullRedraw(true)  // 初始化时需要完全重绘
    , m_lastUpdatedCurve(-1)
    , m_lastUpdatedPoint(-1)
{
    // 设计稿长宽 1282, 254
    // setWidth(1030);
    // setHeight(204);
    setWidth(1058);
    setHeight(224);
    setFlag(QQuickItem::ItemHasContents, true);
    setAcceptedMouseButtons(Qt::AllButtons);

    // 默认使用DataModel单例
    setDataModel(DataModel::getInstance());

    // 初始化数据矩形范围
    // x轴范围为0-30（对应31个频点）
    // y轴范围为-20到+20（对应增益范围）
    m_dataRect = QRectF(0, -20, 30, 40);

    // 设置绘图区域边距
    // m_marginLeft = 10;   // 左边距
    // m_marginRight = 12;  // 右边距
    // m_marginTop = 8;     // 上边距
    // m_marginBottom = 2;  // 下边距
    m_marginLeft = 35;   // 左边距
    m_marginRight = 12;  // 右边距
    m_marginTop = 5;     // 上边距
    m_marginBottom = 24;  // 下边距

    // 初始化节流重绘相关变量
    m_updateTimer.start();
    m_throttleTimer.setSingleShot(true);
    connect(&m_throttleTimer, &QTimer::timeout, this, &CanvasView::performUpdate);
    m_refreshRate = 60; // 默认60Hz
    m_refreshInterval = 1000 / m_refreshRate; // 计算刷新间隔(ms)

    // 连接自身的信号到对应的槽函数
    connect(this, &CanvasView::adjustablePointChanged, this, &CanvasView::onAdjustablePointChanged);
    connect(this, &CanvasView::highpassPointChanged, this, &CanvasView::onHighpassPointChanged);
    connect(this, &CanvasView::lowpassPointChanged, this, &CanvasView::onLowpassPointChanged);


}

/**
 * @brief 设置DataModel并连接信号
 * @param model DataModel指针
 */
void CanvasView::setDataModel(DataModel* model)
{
    // 如果传入的model为空，使用单例
    if (model == nullptr) {
        model = DataModel::getInstance();
    }

    if (m_model == model) {
        return; // 如果是同一个model，不需要重新设置
    }

    // 断开旧model的信号连接
    if (m_model) {
        disconnect(m_model, nullptr, this, nullptr);
    }

    m_model = model;

    // 连接新model的信号
    if (m_model) {
        // 连接数据变化和可见性变化信号到重绘槽
        connect(m_model, &DataModel::dataChanged, this, &CanvasView::onDataChanged);
        connect(m_model, &DataModel::visibilityChanged, this, &CanvasView::onVisibilityChanged);
        connect(m_model, &DataModel::pointPositionChanged, this, &CanvasView::onPointPositionChanged);
        connect(m_model, &DataModel::selectedPointChanged, this, &CanvasView::onSelectedPointChanged);
        connect(m_model, &DataModel::filterPointsVisibilityChanged, this, &CanvasView::onFilterPointsVisibilityChanged);
        connect(m_model, &DataModel::highpassPointVisibilityChanged, this, &CanvasView::onHighpassPointVisibilityChanged);
        connect(m_model, &DataModel::lowpassPointVisibilityChanged, this, &CanvasView::onLowpassPointVisibilityChanged);
        // 注释掉DataModel高低通信号到自身信号的连接，避免外部调用时触发信号
        // 只有用户主动拖拽时才发送信号，在mouseMoveEvent和mouseReleaseEvent中处理
        /*
        connect(m_model, &DataModel::highpassPointChanged,
                this, [this](int curveIndex, double frequency) {
                    if (curveIndex == m_model->getSelectedCurveIndex()) {
                        emit highpassPointChanged(frequency);
                    }
                });
        connect(m_model, &DataModel::lowpassPointChanged,
                this, [this](int curveIndex, double frequency) {
                    if (curveIndex == m_model->getSelectedCurveIndex()) {
                        emit lowpassPointChanged(frequency);
                    }
                });
        */

        // 连接高低通滤波点参数变化信号
        connect(m_model, &DataModel::highpassPointParamsChanged,
                [this](int curveIndex, double frequency, FilterTypeUniversal type, int slope) {
            if (curveIndex == m_model->getSelectedCurveIndex()) {
                qDebug() << "CanvasView get signal DataModel::highpassPointParamsChanged";
                throttledUpdate();
            }
        });

        connect(m_model, &DataModel::lowpassPointParamsChanged,
                [this](int curveIndex, double frequency, FilterTypeUniversal type, int slope) {
            if (curveIndex == m_model->getSelectedCurveIndex()) {
                qDebug() << "CanvasView get signal DataModel::lowpassPointParamsChanged";
                throttledUpdate();
            }
        });

        // 注释掉DataModel信号到自身信号的连接，避免外部调用时触发信号
        // 只有用户主动拖拽时才发送信号，在mouseMoveEvent和mouseReleaseEvent中处理
        /*
        connect(m_model, &DataModel::adjustablePointChanged,
                [this](int curveIndex, int pointIndex, double frequency, double qValue, double gain) {
            if (curveIndex == m_model->getSelectedCurveIndex()) {
                emit adjustablePointChanged(pointIndex + 1, frequency, qValue, gain); // 点编号从1开始
            }
        });
        */

        // 连接预设变化信号到专门的处理槽
        connect(m_model, &DataModel::curvePresetChanged,
                [this](int curveIndex, DataModel::PresetType type, DataModel::PresetLevel level) {
            onCurvePresetChanged(curveIndex, static_cast<int>(type), static_cast<int>(level));
        });

        // 检查可调节点是否已初始化
        int activeCurve = 0; // 默认第一条曲线
        const QVector<QPointF>& points = m_model->getAdjustablePoints(activeCurve);
        if (!points.isEmpty()) {
            // 确保初始化后立即更新显示
            QTimer::singleShot(100, this, [this]() {
                throttledUpdate();
            });
        }
    }

    emit dataModelChanged();
}

/**
 * @brief 设置重绘频率
 * @param hz 每秒重绘次数
 */
void CanvasView::setRefreshRate(int hz)
{
    if (hz < 1) hz = 1;
    if (hz > 240) hz = 240; // 设置上限，避免过高刷新率

    m_refreshRate = hz;
    m_refreshInterval = 1000 / hz;
}

/**
 * @brief 节流更新方法
 * 控制重绘频率，避免过于频繁的重绘
 * @param rect 需要更新的矩形区域，默认为整个控件
 */
void CanvasView::throttledUpdate(const QRect& rect)
{
    // 记录有更新请求
    m_updatePending = true;

    // 检查距离上次更新的时间
    qint64 elapsed = m_updateTimer.elapsed();

    // 如果时间间隔足够长，直接更新
    if (elapsed >= m_refreshInterval) {
        // 如果定时器正在运行，停止它
        if (m_throttleTimer.isActive()) {
            m_throttleTimer.stop();
        }

        // 重置计时器
        m_updateTimer.restart();

        // 执行更新
        update();

        // 清除更新标志
        m_updatePending = false;
    }
    // 如果时间间隔不够，且定时器未启动，则启动定时器
    else if (!m_throttleTimer.isActive()) {
        // 计算剩余等待时间
        int remainingTime = m_refreshInterval - static_cast<int>(elapsed);

        // 启动定时器，延迟执行更新
        m_throttleTimer.start(remainingTime);
    }
    // 如果定时器已经在运行，什么都不做，等待定时器触发
}

/**
 * @brief 执行实际重绘
 * 定时器触发时调用此槽函数
 */
void CanvasView::performUpdate()
{
    if (m_updatePending) {
        // 重置计时器
        m_updateTimer.restart();

        // 执行更新
        update();

        // 清除更新标志
        m_updatePending = false;
    }
}

/**
 * @brief 数据变化时的处理函数
 * 根据变化类型决定重绘策略
 */
void CanvasView::onDataChanged()
{
    // 默认情况下只需要局部更新
    if (!m_needFullRedraw) {
        // 如果有最近更新的点，只更新该点附近区域
        if (m_lastUpdatedCurve >= 0 && m_lastUpdatedPoint >= 0) {
            // 使用更大的更新区域
            QPointF pt;
            if (m_lastUpdatedCurve < m_model->curveCount() &&
                m_lastUpdatedPoint < m_model->getAdjustablePoints(m_lastUpdatedCurve).size()) {
                pt = m_model->getAdjustablePoints(m_lastUpdatedCurve)[m_lastUpdatedPoint];
                QPointF wpt = dataToWidget(pt);

                // 创建一个更大的矩形区域，确保覆盖足够的显示区域
                QRect updateRect(wpt.x() - 100, wpt.y() - 100, 200, 200);
                throttledUpdate(updateRect);
            } else {
                // 如果找不到点，更新整个区域
                throttledUpdate();
            }
        } else {
            // 没有具体点的信息，更新整个曲线区域
            throttledUpdate();
        }
    } else {
        throttledUpdate(); // 完全重绘
        m_needFullRedraw = false; // 重置标志
    }
}

/**
 * @brief 可见性变化时的处理函数
 */
void CanvasView::onVisibilityChanged()
{
    m_needFullRedraw = true; // 可见性变化需要完全重绘
    throttledUpdate();
}

/**
 * @brief 点位置变化时的处理函数
 */
void CanvasView::onPointPositionChanged(int curveIndex, int pointIndex, const QPointF& position)
{
    m_lastUpdatedCurve = curveIndex;
    m_lastUpdatedPoint = pointIndex;

    // 只更新该点附近区域
    updatePointArea(curveIndex, pointIndex);
}

/**
 * @brief 选中点变化时的处理函数
 */
void CanvasView::onSelectedPointChanged(int curveIndex, int pointIndex)
{
    // 保存当前活动曲线索引
    int originalActiveCurve = m_model->getSelectedCurveIndex();

    // 如果之前有选中点，需要更新原选中点区域
    int oldSelectedCurve = m_model->getSelectedCurveIndex();
    int oldSelectedPoint = m_model->getSelectedPointIndex();

    if (oldSelectedCurve >= 0 && oldSelectedPoint >= 0) {
        QPointF pt;
        if (oldSelectedCurve < m_model->curveCount() &&
            oldSelectedPoint < m_model->getAdjustablePoints(oldSelectedCurve).size()) {
            pt = m_model->getAdjustablePoints(oldSelectedCurve)[oldSelectedPoint];
            QPointF wpt = dataToWidget(pt);

            // 创建一个更大的矩形区域，确保覆盖足够的显示区域
            QRect updateRect(wpt.x() - 80, wpt.y() - 80, 160, 160);
            throttledUpdate(updateRect);
        }
    }

    // 更新新选中点区域
    if (curveIndex >= 0 && pointIndex >= 0) {
        QPointF pt;
        if (curveIndex < m_model->curveCount() &&
            pointIndex < m_model->getAdjustablePoints(curveIndex).size()) {
            pt = m_model->getAdjustablePoints(curveIndex)[pointIndex];
            QPointF wpt = dataToWidget(pt);

            // 创建一个更大的矩形区域，确保覆盖足够的显示区域
            QRect updateRect(wpt.x() - 80, wpt.y() - 80, 160, 160);
            throttledUpdate(updateRect);

            // 如果选中点所在曲线与当前活动曲线不同，确保选中点所在曲线可见
            if (curveIndex != m_model->getSelectedCurveIndex() && !m_model->isCurveVisible(curveIndex)) {
                m_model->setCurveVisible(curveIndex, true);
            }
        }
    }

    // 检查活动曲线是否被意外修改
    if (m_model->getSelectedCurveIndex() != originalActiveCurve) {
        // 恢复活动曲线
        m_model->setSelectedPoint(m_model->getSelectedCurveIndex(), m_model->getSelectedPointIndex());
    }
}

/**
 * @brief 高通滤波点变化时的处理函数
 */
void CanvasView::onHighpassPointChanged(double frequency)
{
    // 更新高通点区域
    if (m_model->getSelectedCurveIndex() >= 0) {
        QPointF highpassPt = m_model->getHighpassPoint(m_model->getSelectedCurveIndex());
        QPointF wpt = dataToWidget(highpassPt);
        // 原来的大小：QRect updateRect(wpt.x() - 20, wpt.y() - 20, 40, 40);
        // 放大100%后的大小：
        QRect updateRect(wpt.x() - 40, wpt.y() - 40, 80, 80);
        throttledUpdate(updateRect);
    }
}

/**
 * @brief 低通滤波点变化时的处理函数
 */
void CanvasView::onLowpassPointChanged(double frequency)
{
    // 更新低通点区域
    if (m_model->getSelectedCurveIndex() >= 0) {
        QPointF lowpassPt = m_model->getLowpassPoint(m_model->getSelectedCurveIndex());
        QPointF wpt = dataToWidget(lowpassPt);
        // 原来的大小：QRect updateRect(wpt.x() - 20, wpt.y() - 20, 40, 40);
        // 放大100%后的大小：
        QRect updateRect(wpt.x() - 40, wpt.y() - 40, 80, 80);
        throttledUpdate(updateRect);
    }
}

/**
 * @brief 更新指定点附近的区域
 */
void CanvasView::updatePointArea(int curveIndex, int pointIndex)
{
    if (curveIndex < 0 || pointIndex < 0) return;

    // 获取点位置
    QPointF pt;
    if (curveIndex < m_model->curveCount() && pointIndex < m_model->getAdjustablePoints(curveIndex).size()) {
        pt = m_model->getAdjustablePoints(curveIndex)[pointIndex];
    } else {
        return;
    }

    // 转换为窗口坐标
    QPointF wpt = dataToWidget(pt);

    // 创建一个足够大的矩形区域，包含点及其周围区域
    // 原来的大小：QRect updateRect(wpt.x() - 30, wpt.y() - 30, 60, 60);
    // 放大100%后的大小：
    QRect updateRect(wpt.x() - 60, wpt.y() - 60, 120, 120);

    // 更新该区域
    throttledUpdate(updateRect);
}

/**
 * @brief 绘制函数
 * 负责绘制坐标轴、曲线、控制点等
 */
void CanvasView::paint(QPainter* painter)
{
    if (!painter || !m_model) {
        return;
    }

    painter->setRenderHint(QPainter::Antialiasing);
    painter->setRenderHint(QPainter::TextAntialiasing);
    painter->setRenderHint(QPainter::SmoothPixmapTransform);

    // 如果是完全重绘，绘制坐标轴
    if (m_needFullRedraw) {
        painter->drawLine(m_marginLeft, height() - m_marginBottom, width() - m_marginRight, height() - m_marginBottom);  // X轴
        painter->drawLine(m_marginLeft, m_marginTop, m_marginLeft, height() - m_marginBottom);  // Y轴
    }

    // 确保绘制顺序正确，使用save/restore保持绘制状态
    painter->save();

    // 1. 先绘制所有曲线
    const auto& colors = m_model->colors();
    const auto& visibility = m_model->visibility();
    for (int curveIndex = 0; curveIndex < m_model->curveCount(); ++curveIndex) {
        if (!visibility[curveIndex]) continue;
        const QVector<QPointF>& points = m_model->getPoints(curveIndex);
        if (points.isEmpty()) continue;
        if (points.size() > 1) {
            // 曲线渲染时，若y值超出-20~20dB范围则不渲染。遍历每个点，若y值超出范围则断开path，只有在y值回到范围内时才重新moveTo。
            int j = 0;
            while (j < points.size()) {
                // 跳过非法区间，找到第一个合法点
                while (j < points.size() && (points[j].y() <= -20.0 || points[j].y() >= 20.0)) {
                    ++j;
                }
                if (j >= points.size()) break;
                // 新建一段path
                QPainterPath path;
                path.moveTo(dataToWidget(points[j]));
                int k = j + 1;
                while (k < points.size() && points[k].y() > -20.0 && points[k].y() < 20.0) {
                    path.lineTo(dataToWidget(points[k]));
                    ++k;
                }
                painter->setPen(QPen(colors[curveIndex], 2));
                painter->drawPath(path);
                // 画完一段，j跳到下一个点，继续找下一段
                j = k;
            }
        }
    }

    painter->restore();
    painter->save();

    // 4. 绘制高低通滤波点（每条曲线各两个点）
    drawFilterPoints(*painter);

    painter->restore();
    painter->save();

    // 5. 绘制31个与曲线无关的可选点（最上层）
    drawAdjustablePoints(*painter);

    // 6. 绘制选中点的F、Q、G信息（最上层）
    drawSelectedPointInfo(*painter);

    painter->restore();
}

/**
 * @brief 是否正在拖动可调节点
 * 只有在鼠标按下并命中可调节点后，拖动才会生效，防止误操作
 */
bool m_isDraggingAdjustablePoint = false;

/**
 * @brief 鼠标按下事件处理函数
 * 处理点选择和拖拽状态初始化
 */
void CanvasView::mousePressEvent(QMouseEvent* event)
{
    QPointF clickPos = event->pos();

    // 1. 先检查是否点击了可调节点
    if (handleAdjustablePointClick(clickPos)) {
        m_isDraggingAdjustablePoint = true; // 只有点击到可调节点才允许拖拽
        throttledUpdate();
        return;
    }

    // 2. 再检查是否点击了高低通滤波点
    if (handleFilterPointClick(clickPos)) {
        m_isDraggingAdjustablePoint = false;
        throttledUpdate();
        return;
    }

    // 3. 修改：点击空白区域时不再清除选中状态
    // 只重置拖拽状态，不清除选中点
    m_isDraggingHighpass = false;
    m_isDraggingLowpass = false;
    m_draggingFilterCurve = -1;
    m_isDraggingAdjustablePoint = false;

    throttledUpdate();
}

/**
 * @brief 鼠标移动事件处理函数
 * 仅在拖拽状态下允许移动可调节点
 */
void CanvasView::mouseMoveEvent(QMouseEvent* event)
{
    QPointF newPos = widgetToData(event->pos());

    // 处理31个可选点的拖拽
    int selectedCurve = m_model->getSelectedCurveIndex();
    int selectedPoint = m_model->getSelectedPointIndex();
    if (m_isDraggingAdjustablePoint && selectedCurve >= 0 && selectedPoint >= 0 && selectedCurve == m_model->getSelectedCurveIndex()) {
        // 获取当前选中点的位置
        const QVector<QPointF>& points = m_model->getAdjustablePoints(selectedCurve);
        if (selectedPoint < points.size()) {
            // 获取当前点的数据以检查类型
            const DataModel::AdjustablePointData& pointData = m_model->getAdjustablePointData(selectedCurve, selectedPoint);

            // 水平拖拽范围限制
            double maxX = log10(20000.0 / 20.0) / log10(40000.0 / 20.0) * 30.0;
            double newX = qBound(0.0, newPos.x(), maxX);

            // 新增：检查是否允许横向拖拽
            if (!m_model->adjustablePointsLateralChangeState()) {
                // 如果禁用横向拖拽，保持原来的 X 坐标
                newX = points[selectedPoint].x();
                qDebug() << "[CanvasView::mouseMoveEvent] 可调节点" << selectedPoint + 1 << "禁用横向拖拽，保持X坐标:" << newX;
            }

            // 根据全局配置和点类型决定是否允许纵向拖拽
            double newY;
            if (!m_model->adjustablePointsVerticalChangeState()) {
                // 如果全局禁用纵向拖拽，保持原来的 Y 坐标
                newY = points[selectedPoint].y();
                qDebug() << "[CanvasView::mouseMoveEvent] 可调节点" << selectedPoint + 1 << "全局禁用纵向拖拽，保持Y坐标:" << newY;
            } else if (pointData.type == DataModel::EQ_TYPE_AP) {
                // EQ_TYPE_AP 类型的点不允许纵向拖拽，保持原来的 Y 坐标
                newY = points[selectedPoint].y();
                qDebug() << "[CanvasView::mouseMoveEvent] EQ_TYPE_AP类型点" << selectedPoint + 1 << "禁用纵向拖拽";
            } else {
                // 其他类型的点允许纵向拖拽
                newY = qBound(-18.0, newPos.y(), 18.0);
            }

            // 创建新的点位置
            QPointF adjustedPos(newX, newY);

            // 更新点位置
            m_model->updateAdjustablePoint(selectedCurve, selectedPoint, adjustedPos);

            // 获取点的频率、Q值和增益
            double frequency = m_model->getPointFrequency(selectedCurve, selectedPoint);
            double qValue = m_model->getPointQValue(selectedCurve, selectedPoint);
            double gain = m_model->getPointGain(selectedCurve, selectedPoint);

            // 发送可调节点变化信号
            emit adjustablePointChanged(selectedPoint + 1, frequency, qValue, gain); // 点编号从1开始

            // 修改：不调用updateLineDisplayStatus，直接设置曲线可见性
            // 确保当前选中曲线可见
            if (!m_model->isCurveVisible(selectedCurve)) {
                m_model->setCurveVisible(selectedCurve, true);
            }

            // 重新计算曲线的显示坐标
            // 获取当前曲线的所有可调节点
            const QVector<QPointF>& adjustablePoints = m_model->getAdjustablePoints(selectedCurve);
            // 更新曲线数据
            m_model->updateLineData(selectedCurve, adjustablePoints);

            throttledUpdate();
            return;
        }
    }

    // 处理高通滤波点拖拽
    if (m_isDraggingHighpass && m_draggingFilterCurve >= 0) {
        // 只能水平拖动
        // 限制x值范围
        double newX = qBound(0.0, newPos.x(), 30.0);

        // 移除碰撞检测：允许高通和低通滤波器重叠
        // 原来的限制：newX = qMin(newX, lowpassX - 1.0);
        // 现在允许高通点超过低通点

        // 更新高通点
        m_model->updateHighpassPoint(m_draggingFilterCurve, newX);

        // 获取高通频率
        double frequency = m_model->getCurveHighpassFc(m_draggingFilterCurve);

        // 发送高通点变化信号
        emit highpassPointChanged(frequency);

        // 重新计算曲线的显示坐标
        // 获取当前曲线的所有可调节点
        const QVector<QPointF>& adjustablePoints = m_model->getAdjustablePoints(m_draggingFilterCurve);
        // 更新曲线数据
        m_model->updateLineData(m_draggingFilterCurve, adjustablePoints);

        throttledUpdate();
        return;
    }

    // 处理低通滤波点拖拽
    if (m_isDraggingLowpass && m_draggingFilterCurve >= 0) {
        // 只能水平拖动
        // 限制x值范围
        double newX = qBound(0.0, newPos.x(), 30.0);

        // 移除碰撞检测：允许高通和低通滤波器重叠
        // 原来的限制：newX = qMax(newX, highpassX + 1.0);
        // 现在允许低通点低于高通点

        // 更新低通点
        m_model->updateLowpassPoint(m_draggingFilterCurve, newX);

        // 获取低通频率
        double frequency = m_model->getCurveLowpassFc(m_draggingFilterCurve);

        // 发送低通点变化信号
        emit lowpassPointChanged(frequency);

        // 修改：不调用updateLineDisplayStatus，直接设置曲线可见性
        // 确保当前滤波曲线可见
        if (!m_model->isCurveVisible(m_draggingFilterCurve)) {
            m_model->setCurveVisible(m_draggingFilterCurve, true);
        }

        // 重新计算曲线的显示坐标
        // 获取当前曲线的所有可调节点
        const QVector<QPointF>& adjustablePoints = m_model->getAdjustablePoints(m_draggingFilterCurve);
        // 更新曲线数据
        m_model->updateLineData(m_draggingFilterCurve, adjustablePoints);

        throttledUpdate();
        return;
    }
}

/**
 * @brief 鼠标释放事件处理函数
 * 释放所有拖拽状态，并在用户松开时发送最终信号
 */
void CanvasView::mouseReleaseEvent(QMouseEvent*)
{
    int curveToUpdate = -1;

    // 结束高通滤波点拖拽
    if (m_isDraggingHighpass && m_draggingFilterCurve >= 0) {
        curveToUpdate = m_draggingFilterCurve;

        // 用户松开高通滤波点时发送信号
        double frequency = m_model->getCurveHighpassFc(m_draggingFilterCurve);
        emit highpassPointChanged(frequency);

        m_isDraggingHighpass = false;
    }

    // 结束低通滤波点拖拽
    if (m_isDraggingLowpass && m_draggingFilterCurve >= 0) {
        curveToUpdate = m_draggingFilterCurve;

        // 用户松开低通滤波点时发送信号
        double frequency = m_model->getCurveLowpassFc(m_draggingFilterCurve);
        emit lowpassPointChanged(frequency);

        m_isDraggingLowpass = false;
    }

    // 结束可调节点拖拽
    if (m_isDraggingAdjustablePoint) {
        // 用户松开可调节点时发送信号
        int selectedCurve = m_model->getSelectedCurveIndex();
        int selectedPoint = m_model->getSelectedPointIndex();
        if (selectedCurve >= 0 && selectedPoint >= 0) {
            double frequency = m_model->getPointFrequency(selectedCurve, selectedPoint);
            double qValue = m_model->getPointQValue(selectedCurve, selectedPoint);
            double gain = m_model->getPointGain(selectedCurve, selectedPoint);
            emit adjustablePointChanged(selectedPoint + 1, frequency, qValue, gain); // 点编号从1开始
        }

        m_isDraggingAdjustablePoint = false;
    }

    // 如果有选中点，也需要更新对应曲线
    int selectedCurve = m_model->getSelectedCurveIndex();
    if (selectedCurve >= 0) {
        curveToUpdate = selectedCurve;
    }

    // 重新计算曲线的显示坐标
    if (curveToUpdate >= 0) {
        // 获取当前曲线的所有可调节点
        const QVector<QPointF>& adjustablePoints = m_model->getAdjustablePoints(curveToUpdate);
        // 更新曲线数据
        m_model->updateLineData(curveToUpdate, adjustablePoints);
    }

    m_draggingFilterCurve = -1;
    throttledUpdate();
}

/**
 * @brief 数据坐标转换为窗口坐标
 */
QPointF CanvasView::dataToWidget(const QPointF& point) const
{
    double x = m_marginLeft + (point.x() - m_dataRect.left()) * (width() - m_marginLeft - m_marginRight) / m_dataRect.width();
    double y = height() - m_marginBottom - (point.y() - m_dataRect.top()) * (height() - m_marginTop - m_marginBottom) / m_dataRect.height();
    return QPointF(x, y);
}

/**
 * @brief 窗口坐标转换为数据坐标
 */
QPointF CanvasView::widgetToData(const QPointF& point) const
{
    double x = m_dataRect.left() + (point.x() - m_marginLeft) * m_dataRect.width() / (width() - m_marginLeft - m_marginRight);
    double y = m_dataRect.top() + (height() - point.y() - m_marginBottom) * m_dataRect.height() / (height() - m_marginTop - m_marginBottom);
    return QPointF(x, y);
}

/**
 * @brief 查找最近的点
 * @return 返回距离目标点最近的点的索引
 */
int CanvasView::findNearestPoint(int curveIndex, const QPointF& targetPoint) const
{
    const QVector<QPointF>& points = m_model->getPoints(curveIndex);
    if (points.isEmpty()) return -1;
    int nearestIndex = 0;
    double minDistance = std::abs(points[0].x() - targetPoint.x());
    for (int i = 1; i < points.size(); ++i) {
        double distance = std::abs(points[i].x() - targetPoint.x());
        if (distance < minDistance) {
            minDistance = distance;
            nearestIndex = i;
        }
    }
    return nearestIndex;
}

/**
 * @brief 计算影响力度
 */
double CanvasView::calculateInfluence(double distance) const
{
    if (distance <= 0) return 1.0;
    if (distance >= 5.0) return 0.0;
    return 1.0 - (distance / 5.0);
}

// manualY插值函数
[[maybe_unused]] static std::vector<double> interpolateManualY(const QVector<double>& manualY, size_t n) {
    std::vector<double> interp(n, 0.0);
    int m = manualY.size();
    for (size_t i = 0; i < n; ++i) {
        double x = i * (m - 1.0) / (n - 1.0);
        int x0 = static_cast<int>(floor(x));
        int x1 = static_cast<int>(ceil(x));
        double t = x - x0;
        double y0 = manualY[qBound(0, x0, m - 1)];
        double y1 = manualY[qBound(0, x1, m - 1)];
        interp[i] = y0 * (1 - t) + y1 * t;
    }
    return interp;
}

// 绘制31个与曲线无关的独立可选点
void CanvasView::drawAdjustablePoints(QPainter& painter) {
    // 只为当前活动曲线绘制可调节点
    if (m_model->getSelectedCurveIndex() < 0) {
        return;
    }

    // 检查曲线可见性，但即使不可见也要显示可调节点
    bool isCurveVisible = m_model->isCurveVisible(m_model->getSelectedCurveIndex());
    if (!isCurveVisible) {
        // 确保活动曲线可见
        m_model->setCurveVisible(m_model->getSelectedCurveIndex(), true);
    }

    const QVector<QPointF>& points = m_model->getAdjustablePoints(m_model->getSelectedCurveIndex());
    if (points.isEmpty()) {
        return;
    }

    // 获取当前选中的点
    int selectedCurve = m_model->getSelectedCurveIndex();
    int selectedPoint = m_model->getSelectedPointIndex();

    // 保存原始字体
    QFont originalFont = painter.font();
    QFont numFont = painter.font();
    numFont.setPointSize(10); // 更大的字体
    numFont.setBold(true);
    painter.setFont(numFont);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::TextAntialiasing, true);
    QColor selectedColor = m_model->colors()[selectedCurve];

    // 绘制所有可调节点
    for (int i = 0; i < 31 && i < points.size(); ++i) {
        const QPointF& pt = points[i];
        QPointF wpt = dataToWidget(pt);

        // 判断是否是选中的点
        bool isSelected = (selectedCurve == m_model->getSelectedCurveIndex() && selectedPoint == i && selectedPoint >= 0);

        // 获取点的数据，判断是否被选中过
        bool hasBeenSelected = false;
        try {
            const DataModel::AdjustablePointData& pointData = m_model->getAdjustablePointData(selectedCurve, i);
            hasBeenSelected = pointData.hasBeenSelected;
        } catch (...) {
            // 如果获取数据失败，默认为未被选中过
            hasBeenSelected = false;
        }

        QString numText = QString::number(i + 1); // 编号从1开始
        QFontMetrics fm(numFont);
        QRect textRect = fm.boundingRect(numText);

        // 可调点显示修改为偏上一个像素，偏右两个像素
        QPoint adjustedCenter = wpt.toPoint();
        adjustedCenter.setX(adjustedCenter.x() + 2); // 偏右两个像素
        adjustedCenter.setY(adjustedCenter.y() - 1); // 偏上一个像素
        textRect.moveCenter(adjustedCenter);
        QPoint textCenter = textRect.center();

        if (isSelected) {
            // 当前选中的点：显示曲线颜色的背景圆
            int pointRadius = 11; // 圆直径扩大两像素，半径从10增加到11
            // 对应的外部的圈，向左上角调整一个像素
            QPointF circlePos(textCenter.x() + 1 - 1, textCenter.y() + 2 - 1); // 原来是+1,+2，现在是+0,+1
            painter.setPen(Qt::NoPen);
            painter.setBrush(selectedColor);
            painter.drawEllipse(circlePos, pointRadius, pointRadius);

            // 字相对于圆向左调整一像素
            QRect adjustedTextRect = textRect;
            adjustedTextRect.translate(-1, 0); // 向左移动1像素
            painter.setPen(Qt::white);
            painter.drawText(adjustedTextRect, Qt::AlignCenter, numText);
        } else {
            // 未选中的点：根据是否被选中过显示不同的背景圆
            int pointRadius = 11;
            QPointF circlePos(textCenter.x() + 1 - 1, textCenter.y() + 2 - 1);

            if (hasBeenSelected) {
                // 曾经选中过但当前未选中：显示 #c8c8c8，不透明度70%
                painter.setPen(Qt::NoPen);
                QColor prevSelectedColor(0xc8, 0xc8, 0xc8, int(255 * 0.7)); // 70%不透明度
                painter.setBrush(prevSelectedColor);
                painter.drawEllipse(circlePos, pointRadius, pointRadius);
            } else {
                // 从未选中过：显示 #606060，不透明度70%
                painter.setPen(Qt::NoPen);
                QColor neverSelectedColor(0x60, 0x60, 0x60, int(255 * 0.7)); // 70%不透明度
                painter.setBrush(neverSelectedColor);
                painter.drawEllipse(circlePos, pointRadius, pointRadius);
            }

            // 绘制文字描边和文字
            painter.setPen(Qt::black);
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = -1; dy <= 1; dy++) {
                    if (dx == 0 && dy == 0) continue;
                    painter.drawText(textRect.translated(dx, dy), Qt::AlignCenter, numText);
                }
            }
            painter.setPen(Qt::white);
            painter.drawText(textRect, Qt::AlignCenter, numText);
        }
    }
    painter.setFont(originalFont);
}

// 新增：绘制选中点的F、Q、G信息
void CanvasView::drawSelectedPointInfo(QPainter& painter) {
    // 获取当前选中点信息
    int selectedCurve = m_model->getSelectedCurveIndex();
    int selectedPoint = m_model->getSelectedPointIndex();

    // 只要有选中点就显示信息
    if (selectedPoint < 0) {
        return;
    }

    // 获取选中点的位置
    const QVector<QPointF>& points = m_model->getAdjustablePoints(selectedCurve);
    if (selectedPoint >= points.size()) {
        return;
    }

    // 检查曲线可见性，但即使不可见也要显示选中点信息
    bool isCurveVisible = m_model->isCurveVisible(selectedCurve);
    if (!isCurveVisible) {
        // 确保选中曲线可见
        m_model->setCurveVisible(selectedCurve, true);
    }

    QPointF pt = points[selectedPoint];
    QPointF wpt = dataToWidget(pt);

    // 获取选中点的F、Q、G值
    double frequency = m_model->getPointFrequency(selectedCurve, selectedPoint);
    double qValue = m_model->getPointQValue(selectedCurve, selectedPoint);
    double gain = m_model->getPointGain(selectedCurve, selectedPoint);

    // 格式化显示信息为两行
    // 第一行：Freq xx
    QString line1 = QString("Freq %1").arg(frequency, 0, 'f', 0);
    // 第二行：Gxx.x/Qx.xxx（Q显示到小数点后三位）
    QString line2 = QString("G%1/Q%2").arg(gain, 0, 'f', 1)
                                      .arg(qValue, 0, 'f', 3);

    // 设置文本样式
    QFont font1 = painter.font();
    font1.setBold(true);
    font1.setPixelSize(14);
    QFont font2 = font1;
    font2.setPixelSize(12);

    // 计算两行文本的尺寸
    QFontMetrics fm1(font1);
    QFontMetrics fm2(font2);
    QRect line1Rect = fm1.boundingRect(line1);
    QRect line2Rect = fm2.boundingRect(line2);

    // 计算总的文本区域（取较宽的一行作为宽度）
    int maxWidth = qMax(line1Rect.width(), line2Rect.width());
    int totalHeight = line1Rect.height() + line2Rect.height() + 2; // 2像素行间距

    QRect totalRect(0, 0, maxWidth + 8, totalHeight + 4); // 添加边距

    // 智能位置调整，避免边界切割
    int infoBoxX = wpt.x();
    int infoBoxY = wpt.y() + 18; // 默认在点的下方，距离更近

    // 获取画布的有效绘制区域
    int canvasLeft = m_marginLeft;
    int canvasRight = width() - m_marginRight;
    int canvasTop = m_marginTop;
    int canvasBottom = height() - m_marginBottom;

    // 水平位置调整：避免左右边界切割
    int halfWidth = totalRect.width() / 2;
    if (infoBoxX - halfWidth < canvasLeft) {
        // 临近左边界时，不再向左移动，左对齐到边界
        infoBoxX = canvasLeft + halfWidth;
    } else if (infoBoxX + halfWidth > canvasRight) {
        // 临近右边界时，右对齐到边界
        infoBoxX = canvasRight - halfWidth;
    }

    // 垂直位置调整：优先显示在下方，空间不足时显示在上方
    if (infoBoxY + totalRect.height() > canvasBottom) {
        // 下方空间不足，显示在点的上方
        infoBoxY = wpt.y() - 18 - totalRect.height();

        // 如果上方也不够，则强制显示在上边界内
        if (infoBoxY < canvasTop) {
            infoBoxY = canvasTop;
        }
    }

    // 设置最终位置
    totalRect.moveCenter(QPoint(infoBoxX, infoBoxY + totalRect.height() / 2));

    // 绘制背景
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor(0, 0, 0, 102));
    painter.drawRoundedRect(totalRect, 4, 4);

    // 绘制第一行文本
    painter.setPen(Qt::white);
    painter.setFont(font1);
    QRect line1DrawRect(totalRect.left(), totalRect.top() + 2, totalRect.width(), line1Rect.height());
    painter.drawText(line1DrawRect, Qt::AlignCenter, line1);

    // 绘制第二行文本
    painter.setFont(font2);
    QRect line2DrawRect(totalRect.left(), totalRect.top() + line1Rect.height() + 4, totalRect.width(), line2Rect.height());
    painter.drawText(line2DrawRect, Qt::AlignCenter, line2);
}

// 新增：绘制高低通滤波点
void CanvasView::drawFilterPoints(QPainter& painter) {
    // 检查是否有任何滤波点需要显示
    bool hasHighpass = m_model->isHighpassPointVisible();
    bool hasLowpass = m_model->isLowpassPointVisible();

    if (!hasHighpass && !hasLowpass) {
        return;
    }

    // 只为当前活动曲线绘制滤波点
    if (m_model->getSelectedCurveIndex() < 0) {
        return;
    }

    // 检查曲线可见性，但即使不可见也要显示滤波点
    bool isCurveVisible = m_model->isCurveVisible(m_model->getSelectedCurveIndex());
    if (!isCurveVisible) {
        // 确保活动曲线可见
        m_model->setCurveVisible(m_model->getSelectedCurveIndex(), true);
    }

    // 获取高通和低通点
    QPointF highpassPt = m_model->getHighpassPoint(m_model->getSelectedCurveIndex());
    QPointF lowpassPt = m_model->getLowpassPoint(m_model->getSelectedCurveIndex());

    QPointF highpassWpt = dataToWidget(highpassPt);
    QPointF lowpassWpt = dataToWidget(lowpassPt);

    // 高通点是否被选中的标志
    bool highpassSelected = m_isDraggingHighpass && m_draggingFilterCurve == m_model->getSelectedCurveIndex();

    // 低通点是否被选中的标志
    bool lowpassSelected = m_isDraggingLowpass && m_draggingFilterCurve == m_model->getSelectedCurveIndex();

    // 保存原始字体
    QFont originalFont = painter.font();

    // 设置字体用于H/L标记
    QFont markerFont = painter.font();
    markerFont.setBold(true);
    markerFont.setPointSize(10);

    // 设置字体用于频率显示
    QFont freqFont = painter.font();
    freqFont.setPointSize(8);

    // 绘制高通点
    if (hasHighpass) {
        if (highpassSelected) {
            // 选中状态绘制红色圈和填充
            painter.setPen(Qt::NoPen);
            painter.setBrush(Qt::red);
            QPointF circlePos = highpassWpt;
            circlePos.setX(highpassWpt.x() - 0);  // 向右偏移3像素
            circlePos.setY(highpassWpt.y() + 0);  // 向下偏移3像素
            painter.drawEllipse(circlePos, 12, 12);
        }

        // 绘制"H"标记
        painter.setFont(markerFont);
        painter.setPen(Qt::white);
        QRect textRect(highpassWpt.x() - 5, highpassWpt.y() - 7, 10, 14);
        painter.drawText(textRect, Qt::AlignCenter, "H");
    }

    // 绘制低通点
    if (hasLowpass) {
        if (lowpassSelected) {
            // 选中状态绘制红色圈和填充
            painter.setPen(Qt::NoPen);
            painter.setBrush(Qt::red);
            QPointF circlePos = lowpassWpt;
            circlePos.setX(lowpassWpt.x() + 0);  // 向右偏移3像素
            circlePos.setY(lowpassWpt.y() + 0);  // 向下偏移3像素
            painter.drawEllipse(circlePos, 12, 12);
        }

        // 绘制"L"标记
        painter.setPen(Qt::white);
        QRect lowTextRect(lowpassWpt.x() - 5, lowpassWpt.y() - 7, 10, 14);
        painter.drawText(lowTextRect, Qt::AlignCenter, "L");
    }

    // 绘制高低通频率值
    painter.setFont(freqFont);

    // 只在高通点可见且选中时显示频率值
    if (hasHighpass && highpassSelected) {
        // 高通频率
        double highpassFreq = m_model->getCurveHighpassFc(m_model->getSelectedCurveIndex());
        QString highpassText = QString("F: %1Hz").arg(highpassFreq, 0, 'f', 0);

        // 计算文本宽度以确保不被截断
        QFontMetrics fm(freqFont);
        QRect highpassFreqRect = fm.boundingRect(highpassText);
        highpassFreqRect.setWidth(highpassFreqRect.width() + 10); // 添加额外的空间
        highpassFreqRect.moveCenter(QPoint(highpassWpt.x(), highpassWpt.y() + 25));

        // 绘制高通频率文本背景
        painter.setPen(Qt::NoPen);
        painter.setBrush(QColor(0, 0, 0, 150));
        painter.drawRoundedRect(highpassFreqRect, 3, 3);

        // 绘制高通频率文本
        painter.setPen(Qt::white);
        painter.drawText(highpassFreqRect, Qt::AlignCenter, highpassText);
    }

    // 只在低通点可见且选中时显示频率值
    if (hasLowpass && lowpassSelected) {
        // 低通频率
        double lowpassFreq = m_model->getCurveLowpassFc(m_model->getSelectedCurveIndex());
        QString lowpassText = QString("F: %1Hz").arg(lowpassFreq, 0, 'f', 0);

        // 计算文本宽度以确保不被截断
        QFontMetrics fm(freqFont);
        QRect lowpassFreqRect = fm.boundingRect(lowpassText);
        lowpassFreqRect.setWidth(lowpassFreqRect.width() + 10); // 添加额外的空间
        lowpassFreqRect.moveCenter(QPoint(lowpassWpt.x(), lowpassWpt.y() + 25));

        // 绘制低通频率文本背景
        painter.setPen(Qt::NoPen);
        painter.setBrush(QColor(0, 0, 0, 150));
        painter.drawRoundedRect(lowpassFreqRect, 3, 3);

        // 绘制低通频率文本
        painter.setPen(Qt::white);
        painter.drawText(lowpassFreqRect, Qt::AlignCenter, lowpassText);
    }

    // 恢复原始字体
    painter.setFont(originalFont);
}

// 新增：处理可选点的点击
bool CanvasView::handleAdjustablePointClick(const QPointF& clickPos) {
    // 只处理当前活动曲线的点击
    if (m_model->getSelectedCurveIndex() < 0) {
        return false;
    }

    // 检查曲线可见性，但即使不可见也要处理点击事件
    bool isCurveVisible = m_model->isCurveVisible(m_model->getSelectedCurveIndex());
    if (!isCurveVisible) {
        // 确保活动曲线可见
        m_model->setCurveVisible(m_model->getSelectedCurveIndex(), true);
    }

    const QVector<QPointF>& points = m_model->getAdjustablePoints(m_model->getSelectedCurveIndex());
    if (points.isEmpty()) {
        return false;
    }

    // 检查是否点击了可调节点
    for (int i = 0; i < points.size(); ++i) {
        QPointF wpt = dataToWidget(points[i]);
        if (QLineF(clickPos, wpt).length() < 10) {  // 10像素的点击范围
            // 保存当前通道索引
            int currentActiveCurve = m_model->getSelectedCurveIndex();

            // 选中点，会自动清除之前的选中状态
            m_model->setSelectedPoint(m_model->getSelectedCurveIndex(), i);

            // 检查选中曲线是否被意外修改
            int newSelectedCurve = m_model->getSelectedCurveIndex();
            if (newSelectedCurve != m_model->getSelectedCurveIndex()) {
                // 强制恢复为活动曲线
                m_model->setSelectedPoint(m_model->getSelectedCurveIndex(), i);
            }

            // 检查活动曲线是否被意外修改
            if (m_model->getSelectedCurveIndex() != currentActiveCurve) {
                // 恢复活动曲线
                m_model->setSelectedPoint(m_model->getSelectedCurveIndex(), i);
            }

            return true;
        }
    }

    return false;
}

// 新增：处理高低通滤波点的点击
bool CanvasView::handleFilterPointClick(const QPointF& clickPos) {
    // 检查是否有任何滤波点可见
    bool hasHighpass = m_model->isHighpassPointVisible();
    bool hasLowpass = m_model->isLowpassPointVisible();

    if (!hasHighpass && !hasLowpass) {
        return false;
    }

    // 只处理当前活动曲线的点击
    if (m_model->getSelectedCurveIndex() < 0) {
        return false;
    }

    // 检查曲线可见性，但即使不可见也要处理点击事件
    bool isCurveVisible = m_model->isCurveVisible(m_model->getSelectedCurveIndex());
    if (!isCurveVisible) {
        // 确保活动曲线可见
        m_model->setCurveVisible(m_model->getSelectedCurveIndex(), true);
    }

    // 获取高通和低通点
    QPointF highpassPt = m_model->getHighpassPoint(m_model->getSelectedCurveIndex());
    QPointF lowpassPt = m_model->getLowpassPoint(m_model->getSelectedCurveIndex());

    QPointF highpassWpt = dataToWidget(highpassPt);
    QPointF lowpassWpt = dataToWidget(lowpassPt);

    // 检查是否点击了高通点（只在高通点可见时）
    if (hasHighpass && QLineF(clickPos, highpassWpt).length() < 10) {  // 10像素的点击范围
        m_isDraggingHighpass = true;
        m_isDraggingLowpass = false;
        m_draggingFilterCurve = m_model->getSelectedCurveIndex();

        // 清除其他选中状态
        m_model->clearSelectedPoint();

        return true;
    }

    // 检查是否点击了低通点（只在低通点可见时）
    if (hasLowpass && QLineF(clickPos, lowpassWpt).length() < 10) {  // 10像素的点击范围
        m_isDraggingHighpass = false;
        m_isDraggingLowpass = true;
        m_draggingFilterCurve = m_model->getSelectedCurveIndex();

        // 清除其他选中状态
        m_model->clearSelectedPoint();

        return true;
    }

    return false;
}

// 处理可调节点位置变化的槽函数
void CanvasView::onAdjustablePointChanged(int pointIndex, double frequency, double qValue, double gain)
{
    // 打印日志
    qDebug() << "点击选中可调节点 - 曲线:" << m_model->getSelectedCurveIndex() << "点索引:" << pointIndex
             << "曲线可见性:" << m_model->isCurveVisible(m_model->getSelectedCurveIndex());
}

/**
 * @brief 高低通滤波点显示状态变化处理函数（兼容接口）
 * @param visible 是否显示高低通滤波点
 */
void CanvasView::onFilterPointsVisibilityChanged(bool visible)
{
    qDebug() << "[CanvasView::onFilterPointsVisibilityChanged] 高低通滤波点显示状态变更为:" << visible;

    // 如果滤波点被隐藏且当前正在拖拽滤波点，则停止拖拽
    if (!visible) {
        if (m_isDraggingHighpass || m_isDraggingLowpass) {
            m_isDraggingHighpass = false;
            m_isDraggingLowpass = false;
            m_draggingFilterCurve = -1;
            qDebug() << "[CanvasView::onFilterPointsVisibilityChanged] 停止滤波点拖拽";
        }
    }

    // 触发重绘
    throttledUpdate();
}

/**
 * @brief 高通滤波点显示状态变化处理函数
 * @param visible 是否显示高通滤波点
 */
void CanvasView::onHighpassPointVisibilityChanged(bool visible)
{
    qDebug() << "[CanvasView::onHighpassPointVisibilityChanged] 高通滤波点显示状态变更为:" << visible;

    // 如果高通滤波点被隐藏且当前正在拖拽高通滤波点，则停止拖拽
    if (!visible && m_isDraggingHighpass) {
        m_isDraggingHighpass = false;
        m_draggingFilterCurve = -1;
        qDebug() << "[CanvasView::onHighpassPointVisibilityChanged] 停止高通滤波点拖拽";
    }

    // 触发重绘
    throttledUpdate();
}

/**
 * @brief 低通滤波点显示状态变化处理函数
 * @param visible 是否显示低通滤波点
 */
void CanvasView::onLowpassPointVisibilityChanged(bool visible)
{
    qDebug() << "[CanvasView::onLowpassPointVisibilityChanged] 低通滤波点显示状态变更为:" << visible;

    // 如果低通滤波点被隐藏且当前正在拖拽低通滤波点，则停止拖拽
    if (!visible && m_isDraggingLowpass) {
        m_isDraggingLowpass = false;
        m_draggingFilterCurve = -1;
        qDebug() << "[CanvasView::onLowpassPointVisibilityChanged] 停止低通滤波点拖拽";
    }

    // 触发重绘
    throttledUpdate();
}

/**
 * @brief 预设变化时的处理函数
 * @param curveIndex 曲线索引
 * @param presetType 预设类型
 * @param presetLevel 预设等级
 */
void CanvasView::onCurvePresetChanged(int curveIndex, int presetType, int presetLevel)
{
    qDebug() << "CanvasView::onCurvePresetChanged 通道" << curveIndex
             << "预设变化为类型" << presetType << "等级" << presetLevel;

    // 如果是当前选中的曲线或者该曲线可见，需要重绘
    if (curveIndex == m_model->getSelectedCurveIndex() || m_model->isCurveVisible(curveIndex)) {
        // 预设变化影响32~36号不可见点，会影响整个曲线，所以需要完全重绘
        m_needFullRedraw = true;
        throttledUpdate();

        qDebug() << "CanvasView: 触发canvas重绘，因为通道" << curveIndex << "的32~36号点已更新";
    }
}

// 注意：onCurvePresetCleared函数已移除，因为清除操作现在使用Flat预设
// 所有预设变化（包括设置为Flat）都通过onCurvePresetChanged处理
