import QtQuick
import "../framework"

Item {
    id: control

    Rectangle {
        id: memorySettingFrame
        anchors.left: parent.left
        anchors.leftMargin: 24
        anchors.top: parent.top
        anchors.topMargin: 24
        width: 1056
        height: 260
        color: "transparent"
        border.width: 1
        border.color: "#747880"
        enabled: (0 === dataMap["remoteModel"])

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: memorySettingTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: memorySettingTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Memory Setting")
            }
        }

        Text {
            id: mainUnitTitle
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("Main Unit Memory")
        }

        Row {
            id: mainUnitTitles
            anchors.left: mainUnitTitle.left
            anchors.top: mainUnitTitle.bottom
            anchors.topMargin: 12

            Text {
                anchors.verticalCenter: parent.verticalCenter
                width: 36
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: "No."
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                width: 160
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Valid / Invalid")
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Comment")
            }
        }

        Column {
            anchors.left: mainUnitTitles.left
            anchors.top: mainUnitTitles.bottom
            anchors.topMargin: 12 - (24 / 2 - 14 / 2)
            spacing: 16 - (24 / 2 - 14 / 2) * 2

            Repeater {
                model: 6

                MemorySettingItem {
                    titleStr: "M" + (modelData + 1)
                    memoryType: 0
                    memoryId: modelData
                }
            }
        }

        Rectangle {
            id: separator
            anchors.left: parent.left
            anchors.leftMargin: 520
            anchors.top: parent.top
            anchors.topMargin: 24
            width: 1
            height: 209
            color: "#747880"
        }

        Text {
            id: dspTitle
            anchors.left: separator.right
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("DSP Source Memory")
        }

        Row {
            id: dspTitles
            anchors.left: dspTitle.left
            anchors.top: dspTitle.bottom
            anchors.topMargin: 12

            Text {
                anchors.verticalCenter: parent.verticalCenter
                width: 36
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: "No."
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                width: 160
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Valid / Invalid")
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Comment")
            }
        }

        Column {
            anchors.left: dspTitles.left
            anchors.top: dspTitles.bottom
            anchors.topMargin: 12 - (24 / 2 - 14 / 2)
            spacing: 16 - (24 / 2 - 14 / 2) * 2

            Repeater {
                model: 6

                MemorySettingItem {
                    titleStr: "S" + (modelData + 1)
                    memoryType: 1
                    memoryId: modelData
                }
            }
        }
    }
}
