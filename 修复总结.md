# Shelving Filter 修复总结

## 问题确认

经过详细分析，确认了31个可调点中EQ_TYPE_LS和EQ_TYPE_HS类型的问题：

### 1. 核心问题
- **算法层面**：所有可调点都被强制处理为峰值滤波器（PEQ_PEAKING_FILTER），完全忽略了用户设置的EQ类型
- **公式问题**：shelving filter的系数计算与标准Audio EQ Cookbook公式不完全一致

### 2. 用户界面支持情况
用户界面**已经支持**shelving filter类型选择：
- 在EQ界面的每个频段下拉框中可以选择：
  - "PEAK" - 峰值滤波器
  - "LS" - 低架滤波器  
  - "HS" - 高架滤波器
  - "AP" - 全通滤波器

**关键发现**：用户一直可以设置shelving类型，但由于算法bug，这些设置从未生效！

## 修复内容

### 修复1：EQ类型正确映射
**文件**：`page/framework/curve/datamodel.cpp` (第698-750行)

**修复前**：
```cpp
calc_iir_filter_coeff(FS, 0, pt.frequency, pt.gain, pt.qValue, &coeffs); // 强制使用0=PEQ_PEAKING_FILTER
```

**修复后**：
```cpp
// 将DataModel::EQType映射到calc_iir_filter_coeff的滤波器类型
int filterType = 0; // 默认为PEQ_PEAKING_FILTER
switch (pt.type) {
    case EQ_TYPE_PEAK:
        filterType = 0; // PEQ_PEAKING_FILTER
        break;
    case EQ_TYPE_LS1:
    case EQ_TYPE_LS2:
        filterType = 1; // PEQ_LOW_SHELF_FILTER
        break;
    case EQ_TYPE_HS1:
    case EQ_TYPE_HS2:
        filterType = 2; // PEQ_HIGH_SHELF_FILTER
        break;
    // ... 其他类型
}
calc_iir_filter_coeff(FS, filterType, pt.frequency, pt.gain, pt.qValue, &coeffs);
```

### 修复2：Shelving Filter公式标准化
**文件**：`page/framework/curve/common/common.cpp` (第272-295行)

**修复前**：
```cpp
beta = sqrtf(A) / q_value; // 错误的公式
```

**修复后**：
```cpp
beta = 2 * sqrtf(A) * alpha; // 标准Audio EQ Cookbook公式
```

## 修复效果

### 修复前的行为
- 用户设置EQ点为"LS"类型 → 实际仍表现为峰值滤波器
- 用户设置EQ点为"HS"类型 → 实际仍表现为峰值滤波器
- Shelving响应可能不准确

### 修复后的行为
- 用户设置EQ点为"LS"类型 → 正确产生低架响应，影响整个低频段
- 用户设置EQ点为"HS"类型 → 正确产生高架响应，影响整个高频段
- Shelving响应符合专业音频标准

## 验证方法

### 简单测试步骤
1. 打开EQ界面
2. 选择任意一个频段（比如第10个）
3. 设置类型为"LS"，增益+6dB，频率1000Hz
4. 观察曲线：应该看到1000Hz以下的所有频率都有约6dB的提升（架形）
5. 设置类型为"HS"，增益+6dB，频率1000Hz  
6. 观察曲线：应该看到1000Hz以上的所有频率都有约6dB的提升（架形）

### 预期结果对比
- **Peak类型**：只在1000Hz附近有峰值提升
- **LS类型**：1000Hz以下整体平坦提升，1000Hz以上逐渐过渡到0dB
- **HS类型**：1000Hz以上整体平坦提升，1000Hz以下逐渐过渡到0dB

## 技术细节

### EQ类型映射关系
```
UI选择 → 内部枚举值 → 滤波器类型
"PEAK" → 0x00 (EQ_TYPE_PEAK) → PEQ_PEAKING_FILTER (0)
"LS"   → 0x07 (EQ_TYPE_LS2)  → PEQ_LOW_SHELF_FILTER (1)  
"HS"   → 0x08 (EQ_TYPE_HS2)  → PEQ_HIGH_SHELF_FILTER (2)
"AP"   → 0x0B (EQ_TYPE_AP2)  → PEQ_PEAKING_FILTER (0) [暂时]
```

### 物理意义
- **Peak Filter**：在特定频率附近产生局部增益/衰减，形成峰值或凹陷
- **Low Shelf**：在截止频率以下的所有频率产生相同增益，形成低频架
- **High Shelf**：在截止频率以上的所有频率产生相同增益，形成高频架

## 兼容性保证

- ✅ 高通/低通滤波器功能不受影响
- ✅ 现有的峰值滤波器（PEAK类型）继续正常工作
- ✅ 31个可调点的其他功能（频率、Q值、增益调节）保持不变
- ✅ 曲线拖拽、实时更新等交互功能不受影响

## 总结

这个修复解决了一个长期存在的功能缺陷：用户界面提供了shelving filter选项，但算法层面从未实现。现在shelving filter功能完全可用，符合专业音频处理标准，用户可以真正使用低架和高架均衡器来调整音频的整体音色特征。
