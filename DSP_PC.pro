TEMPLATE = app
QT += core gui widgets quickwidgets network

CONFIG += c++17
CONFIG -= no_keywords

# Generate version.h before compilation
version_header.target = page/version.h
win32 {
    version_header.commands = powershell -ExecutionPolicy Bypass -File $$PWD/scripts/generate_version.ps1 -OutputDir $$PWD/page
} else {
    version_header.commands = bash $$PWD/scripts/generate_version.sh $$PWD/page
}
version_header.depends = FORCE
PRE_TARGETDEPS += page/version.h
QMAKE_EXTRA_TARGETS += version_header

# 编译缓存目录配置
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/build/bin/debug
    OBJECTS_DIR = $$PWD/build/obj/debug
    MOC_DIR = $$PWD/build/obj/debug/moc
    RCC_DIR = $$PWD/build/obj/debug/rcc
    UI_DIR = $$PWD/build/obj/debug/ui
} else {
    DESTDIR = $$PWD/build/bin/release
    OBJECTS_DIR = $$PWD/build/obj/release
    MOC_DIR = $$PWD/build/obj/release/moc
    RCC_DIR = $$PWD/build/obj/release/rcc
    UI_DIR = $$PWD/build/obj/release/ui
    DEFINES += QT_NO_DEBUG_OUTPUT
}

QMAKE_CXXFLAGS += -fno-stack-protector
QMAKE_LFLAGS += -fno-stack-protector

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    communication/HidSender.cpp \
    communication/USBManager.cpp \
    communication/aes.c \
    communication/aesapi.cpp \
    datacenter/DataCenterController.cpp \
    datacenter/DataHandlerAbstract.cpp \
    datacenter/Mid01Handler.cpp \
    datacenter/Mid02Handler.cpp \
    datacenter/Mid03Handler.cpp \
    datacenter/Mid04Handler.cpp \
    datacenter/Mid05Handler.cpp \
    datacenter/Mid06Handler.cpp \
    main.cpp \
    page/CommonController.cpp \
    page/MainWindow.cpp \
    page/framework/ChannelItem.cpp \
    page/framework/GeneralSettingsPopup.cpp \
    page/framework/PageTitle.cpp \
    page/framework/PageToolBar.cpp \
    page/framework/curve/canvasview.cpp \
    page/framework/curve/common/common.cpp \
    page/framework/curve/datamodel.cpp \
    page/framework/curve/preset_manager.cpp \
    page/framework/curve/common/butterworth_filter.cpp \
    page/framework/curve/common/bessel_filter.cpp \
    page/framework/curve/common/linkwitz_riley_filter.cpp

HEADERS += \
    communication/HidDef.h \
    communication/HidSender.h \
    communication/LibUsb/include/hidapi.h \
    communication/LibUsb/include/hidapi_winapi.h \
    communication/USBManager.h \
    communication/aes.h \
    communication/aesapi.h \
    datacenter/DataCenterController.h \
    datacenter/DataHandlerAbstract.h \
    datacenter/DataHandlerDef.h \
    datacenter/Mid01Handler.h \
    datacenter/Mid02Handler.h \
    datacenter/Mid03Handler.h \
    datacenter/Mid04Handler.h \
    datacenter/Mid05Handler.h \
    datacenter/Mid06Handler.h \
    page/CommonController.h \
    page/MainWindow.h \
    page/version.h \
    page/framework/ChannelItem.h \
    page/framework/GeneralSettingsPopup.h \
    page/framework/PageTitle.h \
    page/framework/PageToolBar.h \
    page/framework/curve/canvasview.h \
    page/framework/curve/common/common.h \
    page/framework/curve/datamodel.h \
    page/framework/curve/preset_manager.h

FORMS += \
    page/MainWindow.ui \
    page/framework/ChannelItem.ui \
    page/framework/GeneralSettingsPopup.ui \
    page/framework/PageTitle.ui \
    page/framework/PageToolBar.ui

VERSION = 0.1.1.7
RC_ICONS = logo.ico
QMAKE_TARGET_PRODUCT = "DSP Controller"
QMAKE_TARGET_COMPANY = "pioneer"
TARGET = "DSP Controller"

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

INCLUDEPATH += \
    $(PRJ_PATH)/communication \
    $(PRJ_PATH)/datacenter \
    $(PRJ_PATH)/page/framework \
    $(PRJ_PATH)/page/framework/curve

RESOURCES += \
    Image.qrc \
    language.qrc \
    qml.qrc

TRANSLATIONS += \
    en_US.ts \
    jp_JP.ts \

# MinGW specific settings
win32-g++ {
    # Disable optimization for debug
    CONFIG(debug, debug|release) {
        QMAKE_CXXFLAGS_DEBUG += -O0
    }

    # Basic optimization for release
    CONFIG(release, debug|release) {
        QMAKE_CXXFLAGS_RELEASE += -O1
    }

    QMAKE_CFLAGS += -fstack-protector
    QMAKE_CXXFLAGS += -fstack-protector
    QMAKE_LFLAGS += -fstack-protector
}

win32: LIBS += -L$$PWD/communication/LibUsb -lhidapi

INCLUDEPATH += $$PWD/communication/LibUsb
DEPENDPATH += $$PWD/communication/LibUsb
