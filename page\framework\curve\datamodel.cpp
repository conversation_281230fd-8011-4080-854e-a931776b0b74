#include "datamodel.h"
#include "qdebug.h"
#include <QtCore>
#include <cmath>
#include "preset_manager.h"

// 静态成员变量定义
DataModel* DataModel::m_instance = nullptr;

/**
 * @brief 获取单例实例
 */
DataModel* DataModel::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new DataModel();
    }
    return m_instance;
}

/**
 * @brief 销毁单例实例
 */
void DataModel::destroyInstance()
{
    if (m_instance != nullptr) {
        delete m_instance;
        m_instance = nullptr;
    }
}

/**
 * @brief 私有构造函数
 * 初始化曲线颜色和可见性状态，并生成初始数据
 */
DataModel::DataModel(QObject* parent)
    : QObject(parent), m_presetManager(nullptr)
{
    qDebug() << "[DataModel] 构造函数开始";

    // 创建预设管理器实例
    m_presetManager = new PresetManager(this);

    // 初始化10种不同的颜色
    m_colors = {
        QColor(0x00C8E8),
        QColor(0x00F090),
        QColor(0xD744D6),
        QColor(0xA870FF),
        QColor(0xE0A000),
        QColor(0xD0D000),
        QColor(0x0088FF),
        QColor(0xFF80C0),
        QColor(0x00B0A0),
        QColor(0xF05878)
    };

    // 初始化显示状态，默认只显示第一条
    m_visibility.fill(false, MAX_CURVES);
    m_visibility[0] = true;  // 只显示第一条
    m_currentLine = 0;       // 设置第一条为当前曲线

    qDebug() << "[DataModel] 开始初始化滤波器参数";
    initCurveFilterParams();
    qDebug() << "[DataModel] 滤波器参数初始化完成";

    qDebug() << "[DataModel] 开始初始化预设数据";
    initPresetData();
    qDebug() << "[DataModel] 预设数据初始化完成";

    qDebug() << "[DataModel] 开始初始化31个可调节点";
    // 初始化31个可调节点
    initAdjustablePoints();
    qDebug() << "[DataModel] 31个可调节点初始化完成，第一条曲线点数:" << m_adjustablePoints[0].size();

    // 打印第一条曲线的所有可调节点位置
    if (!m_adjustablePoints.isEmpty() && !m_adjustablePoints[0].isEmpty()) {
        qDebug() << "[DataModel] 第一条曲线的可调节点位置:";
        for (int i = 0; i < m_adjustablePoints[0].size(); ++i) {
            qDebug() << "  点" << i << ":" << m_adjustablePoints[0][i];
        }
    }

    qDebug() << "[DataModel] 开始生成曲线数据";
    generateAllPoints();
    qDebug() << "[DataModel] 曲线数据生成完成";

    // 默认选中0号曲线但不选中点
    m_selectedCurveIndex = 0;
    m_selectedPointIndex = -1;
    emit selectedPointChanged(0, -1);
    emit dataChanged();

    qDebug() << "[DataModel] 构造函数完成";

    m_lineType = LINE_TYPE_STEP; // 默认可调点+滤波器，保证可调点调整后曲线跟随变化
}

/**
 * @brief 析构函数
 */
DataModel::~DataModel()
{
    qDebug() << "[DataModel] 析构函数";
}

// 新增：初始化31个可调节点
void DataModel::initAdjustablePoints() {
    m_adjustablePoints.clear();
    m_adjustablePointsData.clear();
    m_adjustablePoints.resize(MAX_CURVES);
    m_adjustablePointsData.resize(MAX_CURVES);

    qDebug() << "[DataModel] 开始初始化" << MAX_CURVES << "条曲线的" << TOTAL_POINTS_COUNT << "个点（31个可见+5个不可见），默认y值为0";

    for (int i = 0; i < MAX_CURVES; ++i) {
        QVector<QPointF> points;
        QVector<AdjustablePointData> pointsData;

        // 创建31个可见的均匀分布点
        for (int j = 0; j < ADJUSTABLE_POINTS_COUNT; ++j) {
            double freqHz = 20.0 * pow(20000.0 / 20.0, j / 30.0); // 20Hz~20kHz对数分布
            double xPos = log10(freqHz / 20.0) / log10(40000.0 / 20.0) * 30.0;

            // 将所有点的y值设为0，与曲线默认y值一致
            double yPos = 0.0;

            // 创建位置点
            QPointF point(xPos, yPos);
            points.append(point);

            // 创建详细数据
            AdjustablePointData pointData;
            pointData.position = point;
            pointData.frequency = freqHz;
            pointData.qValue = 4.32; // 默认Q值
            pointData.gain = yPos;   // 增益与y坐标相同
            pointData.type = EQ_TYPE_PEAK;   // 默认类型为PEAK
            pointsData.append(pointData);
        }

        // 创建5个不可见点（32~36，索引31~35）
        for (int j = 0; j < INVISIBLE_POINTS_COUNT; ++j) {
            int pointIndex = ADJUSTABLE_POINTS_COUNT + j; // 31, 32, 33, 34, 35

            // 不可见点的默认频率分布在20kHz-40kHz之间
            double freqHz = 20000.0 + (20000.0 * j / (INVISIBLE_POINTS_COUNT - 1)); // 20kHz到40kHz均匀分布
            double xPos = log10(freqHz / 20.0) / log10(40000.0 / 20.0) * 30.0;
            double yPos = 0.0;

            // 创建位置点
            QPointF point(xPos, yPos);
            points.append(point);

            // 创建详细数据
            AdjustablePointData pointData;
            pointData.position = point;
            pointData.frequency = freqHz;
            pointData.qValue = 4.32; // 默认Q值
            pointData.gain = yPos;   // 增益与y坐标相同
            pointData.type = EQ_TYPE_PEAK;   // 默认类型为PEAK
            pointsData.append(pointData);

            qDebug() << "[DataModel] 曲线" << i << "不可见点" << (pointIndex + 1) << "初始化: 频率=" << freqHz << "Hz";
        }

        m_adjustablePoints[i] = points;
        m_adjustablePointsData[i] = pointsData;

        qDebug() << "[DataModel] 曲线" << i << "的所有点初始化完成，总点数:" << points.size()
                 << "（可见:" << ADJUSTABLE_POINTS_COUNT << "，不可见:" << INVISIBLE_POINTS_COUNT << "）";
    }

    qDebug() << "[DataModel] 所有曲线的" << TOTAL_POINTS_COUNT << "个点初始化完成";
    qDebug() << "[DataModel] 第一条曲线第一个点频率:" << getPointFrequency(0, 0) << "Hz";
    qDebug() << "[DataModel] 第一条曲线最后一个点频率:" << getPointFrequency(0, 30) << "Hz";

    // 打印所有曲线的可调节点数量
    for (int i = 0; i < m_adjustablePoints.size(); ++i) {
        qDebug() << "[DataModel] 曲线" << i << "的可调节点数量:" << m_adjustablePoints[i].size();
    }
}

// 新增：获取曲线的可调节点（只返回前31个可见点）
const QVector<QPointF>& DataModel::getAdjustablePoints(int curveIndex) const {
    static QVector<QPointF> visiblePoints;
    if (curveIndex >= 0 && curveIndex < m_adjustablePoints.size()) {
        visiblePoints.clear();
        // 只返回前31个可见点
        for (int i = 0; i < ADJUSTABLE_POINTS_COUNT && i < m_adjustablePoints[curveIndex].size(); ++i) {
            visiblePoints.append(m_adjustablePoints[curveIndex][i]);
        }
        return visiblePoints;
    }
    static QVector<QPointF> empty;
    return empty;
}

// 新增：更新可调节点（只允许操作前31个可见点）
void DataModel::updateAdjustablePoint(int curveIndex, int pointIndex, const QPointF& point)
{
    if (curveIndex >= 0 && curveIndex < m_adjustablePoints.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {

        // 保存当前通道索引，确保不会被修改
        int currentLine = m_currentLine;

        // 记录是否已经调用过generatePoints
        static int lastGeneratedCurve = -1;
        static QElapsedTimer lastGenerateTime;

        // 如果是首次调用，启动计时器
        if (!lastGenerateTime.isValid()) {
            lastGenerateTime.start();
        }

        // 更新点的位置
        m_adjustablePoints[curveIndex][pointIndex] = point;

        // 更新点的详细数据
        if (curveIndex < m_adjustablePointsData.size() &&
            pointIndex < m_adjustablePointsData[curveIndex].size()) {

            // 获取原始数据
            AdjustablePointData& data = m_adjustablePointsData[curveIndex][pointIndex];

            // 更新位置
            data.position = point;

            // 更新频率（根据x坐标计算）
            double xNormalized = point.x() / 30.0; // 归一化到0-1范围
            double freq = 20.0 * pow(2000.0, xNormalized); // 20Hz到40kHz的对数分布
            data.frequency = std::round(freq);

            // 更新增益（直接使用y坐标）
            data.gain = point.y();

            // 保持Q值和类型不变
        }

        // 发送点位置变化信号
        emit pointPositionChanged(curveIndex, pointIndex, point);

        // 获取点的频率、Q值和增益
        double frequency = getPointFrequency(curveIndex, pointIndex);
        double qValue = getPointQValue(curveIndex, pointIndex);
        double gain = getPointGain(curveIndex, pointIndex);
        EQType type = getPointType(curveIndex, pointIndex);

        // 发送可调节点变化信号
        emit adjustablePointChanged(curveIndex, pointIndex, frequency, qValue, gain, type);

        // 使用新的generatePoints函数重新生成该通道的曲线数据
        // 但避免短时间内对同一通道重复生成
        qint64 elapsed = lastGenerateTime.elapsed();
        if (curveIndex != lastGeneratedCurve || elapsed > 100) {
            qDebug() << "[DataModel::updateAdjustablePoint] 重新生成曲线" << curveIndex
                     << "的数据，距上次生成时间:" << elapsed << "ms";

            generatePoints(curveIndex);
            lastGeneratedCurve = curveIndex;
            lastGenerateTime.restart();
        } else {
            qDebug() << "[DataModel::updateAdjustablePoint] 跳过重复生成曲线" << curveIndex
                     << "的数据，距上次生成时间仅" << elapsed << "ms";
        }

        // 确保当前通道不变
        if (m_currentLine != currentLine) {
            qDebug() << "[警告] 当前通道在updateAdjustablePoint中被意外修改，恢复为原值:" << currentLine;
            m_currentLine = currentLine;
        }

        // 发送数据变化信号
        emit dataChanged();
    }
}

/**
 * @brief 设置指定曲线的点数据
 * @param curveIndex 曲线索引
 * @param points 新的点数据
 */
void DataModel::setPoints(int curveIndex, const QVector<QPointF>& points)
{
    Q_UNUSED(curveIndex);
    Q_UNUSED(points);
            emit dataChanged();
}

/**
 * @brief 更新指定曲线的指定点位置
 * @param curveIndex 曲线索引
 * @param pointIndex 点索引
 * @param point 新的点位置
 */
void DataModel::updatePoint(int curveIndex, int pointIndex, const QPointF& point)
{
    Q_UNUSED(curveIndex);
    Q_UNUSED(pointIndex);
    Q_UNUSED(point);
        emit dataChanged();
}

/**
 * @brief 设置点的密度倍数
 * @param multiplier 新的密度倍数
 */
void DataModel::setMultiplier(int multiplier)
{
    if (m_multiplier != multiplier && multiplier > 0) {
        m_multiplier = multiplier;
        generateAllPoints();
    }
}

/**
 * @brief 重新生成所有曲线的点数据
 * 根据当前的密度倍数生成曲线
 */
void DataModel::generateAllPoints()
{
    qDebug() << "[DataModel] generateAllPoints called";
    m_allPoints.clear();
    m_allPoints.resize(curveCount());

    // 生成所有通道的曲线数据
    for (int i = 0; i < curveCount(); ++i) {
        generatePoints(i);
    }

    qDebug() << "[DataModel] generateAllPoints finished, m_allPoints.size=" << m_allPoints.size();
    emit dataChanged();
    qDebug() << "[DataModel] emit dataChanged";
}

/**
 * @brief 重新生成指定通道曲线的点数据
 * @param chIndex 通道索引
 */
void DataModel::generatePoints(int chIndex)
{
    qDebug() << "[DataModel] generatePoints called for channel" << chIndex;

    if (chIndex < 0 || chIndex >= curveCount()) {
        qDebug() << "[DataModel] Invalid channel index:" << chIndex;
        return;
    }

    // 确保m_allPoints有足够的空间
    if (m_allPoints.size() <= chIndex) {
        m_allPoints.resize(chIndex + 1);
    }

    // 生成高密度频率点，确保范围为20Hz到40000Hz
    DoubleArray freq_list = get_oct_freq_list(20.0, 40000.0);
    QVector<QPointF> points;
    for (size_t j = 0; j < freq_list.size; ++j) {
        double x = (double)j * 30.0 / (freq_list.size - 1);
        points.append(QPointF(x, 0.0));
    }
    // 直接用物理叠加法生成曲线
    processLinePointsWithFilter(chIndex, points);
    free(freq_list.data);
    m_allPoints[chIndex] = points;

    // 单行打印所有点坐标
    QString line = QString("[DataModel] ch %1 points:").arg(chIndex);
    for (const auto& pt : points) {
        line += QString(" (%1,%2)").arg(pt.x(), 0, 'f', 2).arg(pt.y(), 0, 'f', 2);
    }
    qDebug().noquote() << line;

    qDebug() << "[DataModel] generatePoints finished for channel" << chIndex << ", points.size=" << points.size();
    // 注意：不在这里发送dataChanged信号，由调用者决定是否需要发送

    // 打印当前ch的可调点信息
    if (chIndex < m_adjustablePointsData.size()) {
        const auto& pointsData = m_adjustablePointsData[chIndex];
        QString info = QString("[DataModel] ch %1 AdjustablePoints:").arg(chIndex);
        for (int i = 0; i < pointsData.size(); ++i) {
            const auto& pt = pointsData[i];
            info += QString(" [%1: f=%2Hz, g=%3dB, Q=%4, type=%5]")
                .arg(i)
                .arg(pt.frequency, 0, 'f', 2)
                .arg(pt.gain, 0, 'f', 2)
                .arg(pt.qValue, 0, 'f', 2)
                .arg(int(pt.type));
        }
        qDebug().noquote() << info;
    }
    // 打印高低通滤波器参数
    if (chIndex < m_curveHighpassParams.size() && chIndex < m_curveLowpassParams.size()) {
        const auto& hp = m_curveHighpassParams[chIndex];
        const auto& lp = m_curveLowpassParams[chIndex];
        QString hpInfo = QString("[DataModel] ch %1 Highpass: type=%2, fc=%3Hz, slope=%4dB/oct")
            .arg(chIndex).arg(int(hp.type)).arg(hp.fc, 0, 'f', 2).arg(hp.slope);
        QString lpInfo = QString("[DataModel] ch %1 Lowpass: type=%2, fc=%3Hz, slope=%4dB/oct")
            .arg(chIndex).arg(int(lp.type)).arg(lp.fc, 0, 'f', 2).arg(lp.slope);
        qDebug().noquote() << hpInfo;
        qDebug().noquote() << lpInfo;
    }
}

const QVector<QPointF>& DataModel::getPoints(int curveIndex) const {
    // qDebug() << "[DataModel] getPoints called" << curveIndex << m_allPoints.size();
    static QVector<QPointF> empty;
    if (curveIndex >= 0 && curveIndex < m_allPoints.size()) return m_allPoints[curveIndex];
    return empty;
}

/**
 * @brief 设置曲线的可见性
 * @param curveIndex 曲线索引
 * @param visible 是否可见
 */
void DataModel::setCurveVisible(int curveIndex, bool visible)
{
    if (curveIndex >= 0 && curveIndex < m_visibility.size()) {
        if (m_visibility[curveIndex] != visible) {
            m_visibility[curveIndex] = visible;
            emit visibilityChanged();
        }
    }
}

/**
 * @brief 获取曲线的可见性状态
 * @param curveIndex 曲线索引
 * @return 是否可见
 */
bool DataModel::isCurveVisible(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_visibility.size()) {
        return m_visibility[curveIndex];
    }
    return false;
}

void DataModel::updateLineType(int lineType)
{
    if (lineType >= LINE_TYPE_NORMAL && lineType <= LINE_TYPE_STEP) {
        m_lineType = static_cast<LineType>(lineType);
        emit dataChanged();
    }
}

void DataModel::updateRedPoint(QPointF point)
{
    // 限制x坐标范围
    if (!m_redPointLateralChange) {
        point.setX(m_redPoint.x());
    } else {
        // 当允许横向移动时，确保x坐标对应到最近的可调点
        int nearestX = qRound(point.x());
        point.setX(nearestX);
    }

    // 限制坐标范围
    point.setX(qBound(0.0, point.x(), 30.0));
    point.setY(point.y());

    if (m_redPoint != point) {
        m_redPoint = point;

        // 发送百分比信号
        float xPercentage = point.x() / 30.0f;
        float yPercentage = (point.y() + 20.0f) / 40.0f;
        emit redpointUpdated(m_currentLine, static_cast<int>(point.x()), xPercentage, yPercentage);

        // 发送实际参数信号
        int frequency = static_cast<int>(20 + point.x() * (40000 - 20) / 30.0);
        float gain = point.y();
        emit redpointUpdated(m_currentLine, static_cast<int>(point.x()), frequency, gain);

        emit dataChanged();
    }
}

void DataModel::updateLineDisplayStatus(int currentLine, QMap<int, bool> lineDisplayState)
{
    bool changed = false;
    if (m_currentLine != currentLine) {
        // 保存旧的当前线索引
        int oldCurrentLine = m_currentLine;

        // 更新当前线索引
        m_currentLine = currentLine;
        changed = true;

        qDebug() << "[DataModel] 当前选中线变更为:" << currentLine << "，原选中线:" << oldCurrentLine;

        // 切换通道时，清除选中点，使其失去焦点
        if (m_selectedPointIndex >= 0) {
            qDebug() << "[DataModel] 切换通道时清除选中点 - 原选中曲线:" << m_selectedCurveIndex
                     << "原选中点:" << m_selectedPointIndex;

            // 只清除选中点索引，但保持选中曲线索引为当前通道
            int oldPoint = m_selectedPointIndex;
            m_selectedPointIndex = -1;

            // 更新选中曲线索引为当前通道
            m_selectedCurveIndex = currentLine;

            // 发送选中点变化信号
            emit selectedPointChanged(m_selectedCurveIndex, -1);

            // 打印日志
            qDebug() << "[DataModel] 清除选中点，但保持选中曲线为当前通道:" << m_selectedCurveIndex
                     << "，原选中点:" << oldPoint;
        } else {
            // 即使没有选中点，也更新选中曲线索引为当前通道
            m_selectedCurveIndex = currentLine;
            qDebug() << "[DataModel] 更新选中曲线为当前通道:" << m_selectedCurveIndex;
        }

        // 标记需要更新可调点位置，但暂不执行
        bool needUpdatePoints = false;

        // 确保当前通道的可调点位置与频率和增益一致
        if (m_adjustablePoints.size() > currentLine &&
            m_adjustablePointsData.size() > currentLine) {

            qDebug() << "[DataModel] 重新计算当前通道" << currentLine << "的可调点位置";

            // 暂存所有需要更新的点
            QVector<QPair<int, QPointF>> updatedPoints;

            for (int i = 0; i < m_adjustablePointsData[currentLine].size(); ++i) {
                AdjustablePointData& data = m_adjustablePointsData[currentLine][i];

                // 计算并更新位置
                // 将频率映射到x坐标
                double x = 0.0;
                if (data.frequency >= 20.0 && data.frequency <= 40000.0) {
                    x = 30.0 * log10(data.frequency / 20.0) / log10(40000.0 / 20.0);
                }

                // 将增益直接作为y坐标
                double y = data.gain;

                // 限制坐标范围
                x = qBound(0.0, x, 30.0);
                y = qBound(-18.0, y, 18.0);

                // 检查位置是否需要更新
                if (data.position.x() != x || data.position.y() != y) {
                    qDebug() << "[DataModel] 更新通道" << currentLine << "点" << i
                             << "位置: (" << data.position.x() << "," << data.position.y() << ") -> ("
                             << x << "," << y << ")";

                    // 更新位置
                    data.position = QPointF(x, y);
                    m_adjustablePoints[currentLine][i] = data.position;

                    // 添加到更新列表，稍后一次性发送信号
                    updatedPoints.append(qMakePair(i, data.position));
                    needUpdatePoints = true;
                }
            }

            // 一次性发送所有点位置变化信号
            for (const auto& pair : updatedPoints) {
                emit pointPositionChanged(currentLine, pair.first, pair.second);
            }

            // 如果有点位置更新，需要重新生成曲线数据，但只生成一次
            if (needUpdatePoints) {
                generatePoints(currentLine);
            }
        }
    }

    // 确保当前选中的曲线可见
    if (!lineDisplayState.value(currentLine, false)) {
        lineDisplayState[currentLine] = true;
        qDebug() << "[DataModel] 强制设置当前选中线" << currentLine << "为可见";
        changed = true;
    }

    // 检查当前选中线在m_visibility中的可见性
    if (currentLine >= 0 && currentLine < m_visibility.size() && !m_visibility[currentLine]) {
        m_visibility[currentLine] = true;
        qDebug() << "[DataModel] 直接设置m_visibility中当前选中线" << currentLine << "为可见";
        changed = true;
    }

    for (auto it = lineDisplayState.begin(); it != lineDisplayState.end(); ++it) {
        int lineIndex = it.key();
        bool visible = it.value();
        if (lineIndex >= 0 && lineIndex < m_visibility.size() && m_visibility[lineIndex] != visible) {
            m_visibility[lineIndex] = visible;
            qDebug() << "[DataModel] 更新线" << lineIndex << "的可见性为" << visible;
            changed = true;
        }
    }

    if (changed) {
        qDebug() << "[DataModel] 线显示状态已更新，当前选中线:" << m_currentLine
                 << "，选中曲线索引:" << m_selectedCurveIndex
                 << "，选中点索引:" << m_selectedPointIndex;

        // 打印所有线的可见性状态
        for (int i = 0; i < m_visibility.size(); ++i) {
            qDebug() << "[DataModel] 线" << i << "的可见性:" << m_visibility[i];
        }

        // 发送可见性变化信号
        emit visibilityChanged();

        // 发送数据变化信号
        emit dataChanged();

        // 如果当前线变化，发送当前线的高通和低通滤波器参数变化信号
        if (m_currentLine >= 0 && m_currentLine < m_curveHighpassParams.size() && m_currentLine < m_curveLowpassParams.size()) {
            // 发送高通滤波器参数变化信号
            emit highpassPointParamsChanged(m_currentLine,
                                          m_curveHighpassParams[m_currentLine].fc,
                                          m_curveHighpassParams[m_currentLine].type,
                                          m_curveHighpassParams[m_currentLine].slope);
            emit highpassPointChanged(m_currentLine, m_curveHighpassParams[m_currentLine].fc);

            // 发送低通滤波器参数变化信号
            emit lowpassPointParamsChanged(m_currentLine,
                                         m_curveLowpassParams[m_currentLine].fc,
                                         m_curveLowpassParams[m_currentLine].type,
                                         m_curveLowpassParams[m_currentLine].slope);
            emit lowpassPointChanged(m_currentLine, m_curveLowpassParams[m_currentLine].fc);

            qDebug() << "[DataModel] 发送当前线" << m_currentLine << "的滤波器参数变化信号"
                     << "高通频率:" << m_curveHighpassParams[m_currentLine].fc
                     << "低通频率:" << m_curveLowpassParams[m_currentLine].fc;
        }

        // 如果当前线变化，发送当前线的31个可调节点数据变化信号
        if (m_currentLine >= 0 && m_currentLine < m_adjustablePoints.size()) {
            const QVector<QPointF>& points = m_adjustablePoints[m_currentLine];
            if (!points.isEmpty()) {
                qDebug() << "[DataModel] 发送当前线" << m_currentLine << "的31个可调节点数据变化信号，点数量:" << points.size();

                // 更新每个点的数据
                for (int i = 0; i < points.size(); ++i) {
                    // 获取点的频率、Q值和增益
                    double frequency = getPointFrequency(m_currentLine, i);
                    double qValue = getPointQValue(m_currentLine, i);
                    double gain = getPointGain(m_currentLine, i);
                    EQType type = getPointType(m_currentLine, i);

                    // 发送可调节点变化信号
                    emit adjustablePointChanged(m_currentLine, i, frequency, qValue, gain, type);
                }
            }
        }
    }
}

void DataModel::updateRedPointLateralChangeState(bool enable)
{
    setAdjustablePointsLateralChangeState(enable);
}

// 新增：设置可调节点横向拖拽状态
void DataModel::setAdjustablePointsLateralChangeState(bool enable)
{
    if (m_adjustablePointsLateralChange != enable) {
        m_adjustablePointsLateralChange = enable;
        qDebug() << "[DataModel::setAdjustablePointsLateralChangeState] 可调节点横向拖拽状态变更为:" << enable;
        // 状态变化时发送数据变化信号，以便界面更新
        emit dataChanged();
    }
}

// 新增：设置可调节点纵向拖拽状态
void DataModel::setAdjustablePointsVerticalChangeState(bool enable)
{
    if (m_adjustablePointsVerticalChange != enable) {
        m_adjustablePointsVerticalChange = enable;
        qDebug() << "[DataModel::setAdjustablePointsVerticalChangeState] 可调节点纵向拖拽状态变更为:" << enable;
        // 状态变化时发送数据变化信号，以便界面更新
        emit dataChanged();
    }
}

// 新增：封装曲线点更新的通用逻辑
void DataModel::processLinePointsWithFilter(int lineIndex, QVector<QPointF>& array) {
    /**
     * @brief 曲线点生成算法（滤波器+可调点物理叠加）
     * 正确物理意义：
     *   1. 先计算高低通滤波器复数响应
     *   2. 对每个可调点，生成peaking filter响应并叠加（复数相乘）
     *   3. y = 20*log10(|总响应|)
     */
    DoubleArray freq_list;
    freq_list.size = array.size();
    freq_list.data = (double*)malloc(sizeof(double) * array.size());
    for (int i = 0; i < array.size(); ++i) {
        freq_list.data[i] = 20.0 * pow(40000.0 / 20.0, array[i].x() / 30.0);
    }

    // 1. 计算高低通滤波器复数响应
    double_complex* total_resp = (double_complex*)calloc(array.size(), sizeof(double_complex));
    for (int i = 0; i < array.size(); ++i) {
        total_resp[i] = double_complex(1.0, 0.0);
    }
    const auto& hp = m_curveHighpassParams.at(lineIndex);
    const auto& lp = m_curveLowpassParams.at(lineIndex);
    process_filter_points(&hp, &lp, &freq_list, total_resp);

    // 2. 对每个可调点，根据EQ类型生成相应的滤波器响应并叠加
    if (lineIndex < m_adjustablePointsData.size() && !m_adjustablePointsData[lineIndex].isEmpty()) {
        const QVector<AdjustablePointData>& pointsData = m_adjustablePointsData[lineIndex];
        for (const auto& pt : pointsData) {
            if (fabs(pt.gain) < 1e-6) continue;

            // 将DataModel::EQType映射到calc_iir_filter_coeff的滤波器类型
            int filterType = 0; // 默认为PEQ_PEAKING_FILTER
            switch (pt.type) {
                case EQ_TYPE_PEAK:
                    filterType = 0; // PEQ_PEAKING_FILTER
                    break;
                case EQ_TYPE_LS1:
                case EQ_TYPE_LS2:
                    filterType = 1; // PEQ_LOW_SHELF_FILTER
                    break;
                case EQ_TYPE_HS1:
                case EQ_TYPE_HS2:
                    filterType = 2; // PEQ_HIGH_SHELF_FILTER
                    break;
                case EQ_TYPE_LP1:
                case EQ_TYPE_LP2:
                case EQ_TYPE_LP2_ALT:
                    filterType = 3; // PEQ_LOWPASS_FILTER
                    break;
                case EQ_TYPE_HP2:
                    filterType = 4; // PEQ_HIGHPASS_FILTER
                    break;
                case EQ_TYPE_NOTCH:
                case EQ_TYPE_AP1:
                case EQ_TYPE_AP2:
                case EQ_TYPE_BP2:
                default:
                    filterType = 0; // 暂时使用PEQ_PEAKING_FILTER，后续可扩展
                    break;
            }

            // 为shelving filter使用合适的Q值
            double effectiveQ = pt.qValue;
            if (filterType == 1 || filterType == 2) { // PEQ_LOW_SHELF_FILTER 或 PEQ_HIGH_SHELF_FILTER
                // Shelving filter使用较低的Q值以获得平滑的架形响应
                // 标准值0.707提供最平滑的过渡，避免在截止频率处产生峰值
                effectiveQ = 0.707;
            }

            BiquadCoeffs coeffs = {0};
            calc_iir_filter_coeff(FS, filterType, pt.frequency, pt.gain, effectiveQ, &coeffs);
            double_complex* peq_resp = (double_complex*)calloc(array.size(), sizeof(double_complex));
            for (int i = 0; i < array.size(); ++i) peq_resp[i] = double_complex(1.0, 0.0);
            calc_single_biquad_resp(&freq_list, FS, &coeffs, peq_resp, 2);
            for (int i = 0; i < array.size(); ++i) {
                total_resp[i] *= peq_resp[i];
            }
            free(peq_resp);

            // 调试输出：显示使用的滤波器类型和实际Q值
            qDebug() << "[processLinePointsWithFilter] 点" << "频率:" << pt.frequency
                     << "增益:" << pt.gain << "原始Q:" << pt.qValue << "实际Q:" << effectiveQ
                     << "EQ类型:" << pt.type << "映射为滤波器类型:" << filterType;
        }
    }

    // 3. 转为dB，写入y值，for循环结束后单行输出
    QStringList logList;
    for (int i = 0; i < array.size(); ++i) {
        double mag = std::abs(total_resp[i]);
        if (mag < 1e-12) mag = 1e-12;
        double y = 20.0 * log10(mag);
        if (y < -100) y = -100;
        if (y > 20) y = 20;
        array[i].setY(y);
        if (i < 10) {
            logList << QString("%1:%2,%3,%4,%5")
                .arg(i)
                .arg(QString::number(freq_list.data[i], 'f', 2))
                .arg(QString::number(std::real(total_resp[i]), 'f', 3))
                .arg(QString::number(std::imag(total_resp[i]), 'f', 3))
                .arg(QString::number(y, 'f', 2));
        }
    }
    if (!logList.isEmpty()) {
        qDebug().noquote() << QString("[processLinePointsWithFilter] %1 ...").arg(logList.join(" | "));
    }

    free(total_resp);
    free(freq_list.data);
}

void DataModel::updateLineData(int lineIndex, QVector<QPointF> points)
{

    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 记录是否已经调用过generatePoints
    static int lastGeneratedLine = -1;
    static QElapsedTimer lastGenerateTime;

    // 如果是首次调用，启动计时器
    if (!lastGenerateTime.isValid()) {
        lastGenerateTime.start();
    }

    // 设置自定义点
    setCustomPoints(lineIndex, points);

    // 使用新的generatePoints函数重新生成该通道的曲线数据

    // 但避免短时间内对同一通道重复生成
    if (lineIndex >= 0 && lineIndex < curveCount()) {
        // 如果是不同的通道，或者距离上次生成时间超过100ms，才重新生成
        qint64 elapsed = lastGenerateTime.elapsed();
        if (lineIndex != lastGeneratedLine || elapsed > 15) {
            qDebug() << "[DataModel::updateLineData] 重新生成曲线" << lineIndex
                     << "的数据，距上次生成时间:" << elapsed << "ms";

            generatePoints(lineIndex);
            lastGeneratedLine = lineIndex;
            lastGenerateTime.restart();
        } else {
            qDebug() << "[DataModel::updateLineData] 跳过重复生成曲线" << lineIndex
                     << "的数据，距上次生成时间仅" << elapsed << "ms";
        }

        // 确保当前通道不变
        if (m_currentLine != currentLine) {
            qDebug() << "[警告] 当前通道在updateLineData中被意外修改，恢复为原值:" << currentLine;
            m_currentLine = currentLine;
        }

        emit dataChanged();
    }
}

void DataModel::updateLinePoint(int lineIndex, int pointIndex, QPointF point)
{
    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    qDebug() << "[DataModel] updateLinePoint called" << lineIndex << pointIndex << point;
    setCustomPoint(lineIndex, pointIndex, point);

    // 使用新的generatePoints函数重新生成该通道的曲线数据
    if (lineIndex >= 0 && lineIndex < curveCount()) {
        generatePoints(lineIndex);


        // 确保当前通道不变
        if (m_currentLine != currentLine) {
            qDebug() << "[警告] 当前通道在updateLinePoint中被意外修改，恢复为原值:" << currentLine;
            m_currentLine = currentLine;
        }

        emit dataChanged();
    }
}

void DataModel::setCurveFilterParams(int curveIndex, int filterType, double fc, int slope)
{
    // 已废弃：请使用setCurveHighpassParam/setCurveLowpassParam
    Q_UNUSED(curveIndex);
    Q_UNUSED(filterType);
    Q_UNUSED(fc);
    Q_UNUSED(slope);
}

int DataModel::curveFilterType(int curveIndex) const {
    // 已废弃：请使用getCurveHighpassParam/getCurveLowpassParam
    Q_UNUSED(curveIndex);
    return 0;
}

double DataModel::curveFc(int curveIndex) const {
    // 已废弃：请使用getCurveHighpassParam/getCurveLowpassParam
    Q_UNUSED(curveIndex);
    return 0.0;
}

int DataModel::curveSlope(int curveIndex) const {
    // 已废弃：请使用getCurveHighpassParam/getCurveLowpassParam
    Q_UNUSED(curveIndex);
    return 0;
}

const QVector<QPointF>& DataModel::customPoints(int lineIndex) const {
    if (lineIndex >= 0 && lineIndex < m_customPoints.size()) {
        return m_customPoints[lineIndex];
    }
    static QVector<QPointF> empty;
    return empty;
}

void DataModel::setCustomPoint(int lineIndex, int pointIndex, const QPointF& pt) {
    qDebug() << "[DataModel] setCustomPoint called" << lineIndex << pointIndex << pt;
    if (lineIndex >= 0) {
        if (lineIndex >= m_customPoints.size()) m_customPoints.resize(lineIndex + 1);
        if (pointIndex >= m_customPoints[lineIndex].size()) m_customPoints[lineIndex].resize(pointIndex + 1);
        m_customPoints[lineIndex][pointIndex] = pt;
    }
}

void DataModel::setCustomPoints(int lineIndex, const QVector<QPointF>& pts) {
    qDebug() << "[DataModel] setCustomPoints called" << lineIndex << pts.size();
    if (lineIndex >= 0) {
        if (lineIndex >= m_customPoints.size()) m_customPoints.resize(lineIndex + 1);
        m_customPoints[lineIndex] = pts;
    }
}

void DataModel::clearCustomPoints(int lineIndex) {
    qDebug() << "[DataModel] clearCustomPoints called" << lineIndex;
    if (lineIndex >= 0 && lineIndex < m_customPoints.size()) {
        m_customPoints[lineIndex].clear();
    }
}

void DataModel::processFilter(QVector<QPointF>& array, const FilterParams& params)
{
    // 生成频率点
    DoubleArray freq_list;
    freq_list.size = array.size();
    freq_list.data = (double*)malloc(sizeof(double) * array.size());
    for (int i = 0; i < array.size(); ++i) freq_list.data[i] = 20.0 + (40000.0 - 20.0) * array[i].x() / 30.0;
    double_complex* resp = (double_complex*)calloc(array.size(), sizeof(double_complex));
    for (int i = 0; i < array.size(); ++i) resp[i] = double_complex(0.0, 0.0);
    // 调试日志：打印部分频率点
    qDebug() << "[processFilter] freq_list (前5):";
    for (int i = 0; i < qMin(5, (int)freq_list.size); ++i) qDebug() << freq_list.data[i];
    BiquadCoeffs coeffs;
    qDebug() << "[processFilter] filterType:" << params.filterType << "fc:" << params.fc << "q:" << params.q << "gain:" << params.gain << "slope:" << params.slope;
    // 根据filterType选择不同滤波算法
    switch (params.filterType) {
    case 1: // biquad
        calc_iir_filter_coeff(FS, params.passType, params.fc, params.gain, params.q, &coeffs);
        calc_single_biquad_resp(&freq_list, FS, &coeffs, resp, 2);
        break;
    case 2: // 1st order
        {
            double b[2], a[2];
            float C = tanf(PI * params.fc / FS);
            complex_filter_1st_order_coeff(params.passType, C, b, a);
            // 填充coeffs结构体
            coeffs.b1st[0] = b[0];
            coeffs.b1st[1] = b[1];
            coeffs.a1st[0] = a[0];
            coeffs.a1st[1] = a[1];
            calc_single_biquad_resp(&freq_list, FS, &coeffs, resp, 1);
        }
        break;
    case 3: // iir
        calc_iir_filter_coeff(FS, params.passType, params.fc, params.gain, params.q, &coeffs);
        calc_single_biquad_resp(&freq_list, FS, &coeffs, resp, params.order > 1 ? 2 : 1);
        break;
    case 4: // combined
        complex_filter_combined_method(params.passType, params.fc, params.slope, &freq_list, resp);
    case 0: // 无滤波
        // 保持resp为1.0
    default:
        break;
    }
    qDebug() << "[processFilter] resp.real (前5):";
    for (int i = 0; i < qMin(5, array.size()); ++i) qDebug() << resp[i].real();
    // 获取自定义点x坐标集合
    QVector<double> customX;
    for (const auto& pt : array) {
        if (pt.y() != 0.0) customX.append(pt.x()); // 这里假设用户编辑点y!=0，实际可用其他标记
    }
    // 更新array的y值，用户自定义点不覆盖
    for (int i = 0; i < array.size(); ++i) {
        bool isCustom = false;
        for (double cx : customX) {
            if (qAbs(array[i].x() - cx) < 0.01) { isCustom = true; break; }
        }
        if (!isCustom) {
            array[i].setY(20.0 * log10(std::abs(resp[i])));
        }
    }
    free(resp);
    free(freq_list.data);
}

void DataModel::setFilterParams(int curveIndex, const FilterParams& params)
{
    if (curveIndex >= 0) {
        if (curveIndex >= m_curveFilterParams.size()) m_curveFilterParams.resize(curveIndex + 1);
        m_curveFilterParams[curveIndex] = params;
    }
}

DataModel::FilterParams DataModel::getFilterParams(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_curveFilterParams.size()) {
        return m_curveFilterParams[curveIndex];
    }
    return FilterParams();
}

// 高通参数单项设置/获取
void DataModel::setCurveHighpassFc(int curveIndex, double fc) {
    FilterParamsUniversal param = getCurveHighpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveHighpassFc] 设置曲线" << curveIndex << "的高通频率: " << fc << "Hz";
    param.fc = fc;
    setCurveHighpassParam(curveIndex, param);
}
double DataModel::getCurveHighpassFc(int curveIndex) const {
    return getCurveHighpassParam(curveIndex).fc;
}
void DataModel::setCurveHighpassType(int curveIndex, FilterTypeUniversal type) {
    FilterParamsUniversal param = getCurveHighpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveHighpassType] 设置曲线" << curveIndex << "的高通类型: " << filterTypeToString(type);
    param.type = type;
    setCurveHighpassParam(curveIndex, param);
}
FilterTypeUniversal DataModel::getCurveHighpassType(int curveIndex) const {
    return getCurveHighpassParam(curveIndex).type;
}
void DataModel::setCurveHighpassSlope(int curveIndex, int slope) {
    FilterParamsUniversal param = getCurveHighpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveHighpassSlope] 设置曲线" << curveIndex << "的高通斜率: " << slope << "dB/oct";
    param.slope = slope;
    setCurveHighpassParam(curveIndex, param);
}
int DataModel::getCurveHighpassSlope(int curveIndex) const {
    return getCurveHighpassParam(curveIndex).slope;
}
// 低通参数单项设置/获取
void DataModel::setCurveLowpassFc(int curveIndex, double fc) {
    FilterParamsUniversal param = getCurveLowpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveLowpassFc] 设置曲线" << curveIndex << "的低通频率: " << fc << "Hz";
    param.fc = fc;
    setCurveLowpassParam(curveIndex, param);
}
double DataModel::getCurveLowpassFc(int curveIndex) const {
    return getCurveLowpassParam(curveIndex).fc;
}
void DataModel::setCurveLowpassType(int curveIndex, FilterTypeUniversal type) {
    FilterParamsUniversal param = getCurveLowpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveLowpassType] 设置曲线" << curveIndex << "的低通类型: " << filterTypeToString(type);
    param.type = type;
    setCurveLowpassParam(curveIndex, param);
}
FilterTypeUniversal DataModel::getCurveLowpassType(int curveIndex) const {
    return getCurveLowpassParam(curveIndex).type;
}
void DataModel::setCurveLowpassSlope(int curveIndex, int slope) {
    FilterParamsUniversal param = getCurveLowpassParam(curveIndex);
    qDebug() << "[DataModel::setCurveLowpassSlope] 设置曲线" << curveIndex << "的低通斜率: " << slope << "dB/oct";
    param.slope = slope;
    setCurveLowpassParam(curveIndex, param);
}
int DataModel::getCurveLowpassSlope(int curveIndex) const {
    return getCurveLowpassParam(curveIndex).slope;
}

// 初始化所有曲线的高通/低通参数
void DataModel::initCurveFilterParams(int curveCount) {
    m_curveHighpassParams.clear();
    m_curveLowpassParams.clear();
    for (int i = 0; i < curveCount; ++i) {
        FilterParamsUniversal hp;
        hp.fc = 600.0;  // 修改默认值为600Hz
        hp.type = FILTER_TYPE_BESSEL_HP;
        hp.slope = 0;   // 默认斜率为0dB
        m_curveHighpassParams.append(hp);
        FilterParamsUniversal lp;
        lp.fc = 10000.0;
        lp.type = FILTER_TYPE_BESSEL_LP;
        lp.slope = 0;   // 默认斜率为0dB
        m_curveLowpassParams.append(lp);
    }
}

// 获取高通参数
FilterParamsUniversal DataModel::getCurveHighpassParam(int curveIndex) const {
    if (curveIndex >= 0 && curveIndex < m_curveHighpassParams.size()) {
        return m_curveHighpassParams[curveIndex];
    }
    // 默认值
    FilterParamsUniversal def;
    def.fc = 600.0;  // 默认600Hz
    def.type = FILTER_TYPE_BUTTERWORTH_HP;
    def.slope = 0;   // 默认斜率为0dB
    return def;
}
// 获取低通参数
FilterParamsUniversal DataModel::getCurveLowpassParam(int curveIndex) const {
    if (curveIndex >= 0 && curveIndex < m_curveLowpassParams.size()) {
        return m_curveLowpassParams[curveIndex];
    }
    // 默认值
    FilterParamsUniversal def;
    def.fc = 10000.0;
    def.type = FILTER_TYPE_BUTTERWORTH_LP;
    def.slope = 0;   // 默认斜率为0dB
    return def;
}

// 设置高通参数
void DataModel::setCurveHighpassParam(int curveIndex, const FilterParamsUniversal& param) {
    if (curveIndex >= 0) {
        if (curveIndex >= m_curveHighpassParams.size()) m_curveHighpassParams.resize(curveIndex + 1);

        // 打印设置的参数
        qDebug() << "[DataModel::setCurveHighpassParam] 设置曲线" << curveIndex << "的高通参数:";
        qDebug() << "  类型=" << filterTypeToString(param.type) << ", fc=" << param.fc << "Hz, 斜率=" << param.slope << "dB/oct";

        m_curveHighpassParams[curveIndex] = param;
        // 新增：主动发射信号，确保界面刷新
        emit highpassPointParamsChanged(curveIndex, param.fc, param.type, param.slope);
        emit highpassPointChanged(curveIndex, param.fc);
        emit dataChanged();
        generatePoints(curveIndex);
    }
}
// 设置低通参数
void DataModel::setCurveLowpassParam(int curveIndex, const FilterParamsUniversal& param) {
    if (curveIndex >= 0) {
        if (curveIndex >= m_curveLowpassParams.size()) m_curveLowpassParams.resize(curveIndex + 1);

        // 打印设置的参数
        qDebug() << "[DataModel::setCurveLowpassParam] 设置曲线" << curveIndex << "的低通参数:";
        qDebug() << "  类型=" << filterTypeToString(param.type) << ", fc=" << param.fc << "Hz, 斜率=" << param.slope << "dB/oct";

        m_curveLowpassParams[curveIndex] = param;
        // 新增：主动发射信号，确保界面刷新
        emit lowpassPointParamsChanged(curveIndex, param.fc, param.type, param.slope);
        emit lowpassPointChanged(curveIndex, param.fc);
        emit dataChanged();
        generatePoints(curveIndex);
    }
}

// 辅助函数：将FilterTypeUniversal枚举值转换为字符串
QString DataModel::filterTypeToString(FilterTypeUniversal type) const {
    switch (type) {
    case FILTER_TYPE_PEQ:              return "PEQ";
    case FILTER_TYPE_LSF:              return "LSF(低通)";
    case FILTER_TYPE_HSF:              return "HSF(高通)";
    case FILTER_TYPE_BESSEL_LP:        return "Bessel低通";
    case FILTER_TYPE_BESSEL_HP:        return "Bessel高通";
    case FILTER_TYPE_BUTTERWORTH_LP:   return "Butterworth低通";
    case FILTER_TYPE_BUTTERWORTH_HP:   return "Butterworth高通";
    case FILTER_TYPE_LINKWITZ_RILEY_LP: return "Linkwitz-Riley低通";
    case FILTER_TYPE_LINKWITZ_RILEY_HP: return "Linkwitz-Riley高通";
    default:                           return QString("未知(%1)").arg(static_cast<int>(type));
    }
}

// 新增：设置选中点（只允许选择前31个可见点）
void DataModel::setSelectedPoint(int curveIndex, int pointIndex) {
    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 检查索引有效性（只允许选择前31个可见点）
    if (curveIndex >= 0 && curveIndex < m_adjustablePoints.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {

        // 如果选中的是不同的点，则更新选中状态
        if (m_selectedCurveIndex != curveIndex || m_selectedPointIndex != pointIndex) {
            m_selectedCurveIndex = curveIndex;
            m_selectedPointIndex = pointIndex;

            // 标记该点为"被选中过"
            if (pointIndex < m_adjustablePointsData[curveIndex].size()) {
                m_adjustablePointsData[curveIndex][pointIndex].hasBeenSelected = true;
                qDebug() << "[DataModel::setSelectedPoint] 标记曲线" << curveIndex << "点" << pointIndex << "为被选中过";
            }

            // 发送选中点变化信号
            emit selectedPointChanged(curveIndex, pointIndex);

            // 获取点的频率、Q值和增益
            double frequency = getPointFrequency(curveIndex, pointIndex);
            double qValue = getPointQValue(curveIndex, pointIndex);
            double gain = getPointGain(curveIndex, pointIndex);
            EQType type = getPointType(curveIndex, pointIndex);

            // 发送包含点编号和F、Q、G信息的信号
            emit adjustablePointChanged(curveIndex, pointIndex, frequency, qValue, gain, type);

            // 打印日志
            qDebug() << "选中点变化 - 曲线:" << curveIndex << "点索引:" << pointIndex
                     << "频率:" << frequency << "Q值:" << qValue << "增益:" << gain
                     << "当前通道保持为:" << currentLine;

            emit dataChanged();
        }
    } else if (pointIndex < 0) {
        // 清除选中点
        if (m_selectedPointIndex >= 0) {
            int oldPoint = m_selectedPointIndex;

            // 保持选中曲线不变，只清除选中点
            m_selectedPointIndex = -1;

            // 发送选中点变化信号
            emit selectedPointChanged(m_selectedCurveIndex, -1);

            // 打印日志
            qDebug() << "清除选中点 - 保持曲线:" << m_selectedCurveIndex
                     << "原点索引:" << oldPoint;

            emit dataChanged();
        }
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在setSelectedPoint中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：清除选中点
void DataModel::clearSelectedPoint() {
    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 如果当前有选中的点，则清除选中状态
    if (m_selectedPointIndex >= 0) {
        int oldPoint = m_selectedPointIndex;

        // 只清除选中点索引，保持选中曲线索引不变
        m_selectedPointIndex = -1;

        // 发送选中点变化信号
        emit selectedPointChanged(m_selectedCurveIndex, -1);

        // 打印日志
        qDebug() << "清除选中点 - 保持曲线:" << m_selectedCurveIndex
                 << "原点索引:" << oldPoint;

        emit dataChanged();
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在clearSelectedPoint中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：获取点的频率值（只允许访问前31个可见点）
double DataModel::getPointFrequency(int curveIndex, int pointIndex) const {
    // 只允许访问前31个可见点
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {
        return m_adjustablePointsData[curveIndex][pointIndex].frequency;
    }

    // 兼容旧版：从位置计算频率
    if (curveIndex >= 0 && curveIndex < m_adjustablePoints.size() &&
        pointIndex >= 0 && pointIndex < m_adjustablePoints[curveIndex].size()) {

        // 将x坐标(0-30)映射到频率(20Hz-20000Hz)，使用对数映射
        double x = m_adjustablePoints[curveIndex][pointIndex].x();
        // 确保x在0-30范围内
        x = qBound(0.0, x, 30.0);

        // 将x值映射到20Hz-20000Hz的对数范围
        double freq = 20.0 * pow(1000.0, x / 30.0);

        // 确保频率不超过20000Hz
        freq = qMin(freq, 20000.0);

        return freq;
    }
    return 1000.0; // 默认返回1kHz
}

// 新增：获取点的Q值（只允许访问前31个可见点）
double DataModel::getPointQValue(int curveIndex, int pointIndex) const {
    // 只允许访问前31个可见点
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {
        return m_adjustablePointsData[curveIndex][pointIndex].qValue;
    }

    // todo: q值应该是读取存储值，不是通过计算来的

    return 4.32; // 默认返回4.32
}

// 新增：获取点的增益值（只允许访问前31个可见点）
double DataModel::getPointGain(int curveIndex, int pointIndex) const {
    // 只允许访问前31个可见点
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {
        return m_adjustablePointsData[curveIndex][pointIndex].gain;
    }

    // 兼容旧版：直接使用y坐标作为增益
    if (curveIndex >= 0 && curveIndex < m_adjustablePoints.size() &&
        pointIndex >= 0 && pointIndex < m_adjustablePoints[curveIndex].size()) {
        // 增益直接使用y坐标值，范围-10到10
        return m_adjustablePoints[curveIndex][pointIndex].y();
    }
    return 0.0; // 默认返回0dB
}

// 新增：获取高通滤波点
QPointF DataModel::getHighpassPoint(int curveIndex) const {
    if (curveIndex >= 0 && curveIndex < m_curveHighpassParams.size()) {
        // 将高通频率映射到x坐标
        double fc = m_curveHighpassParams[curveIndex].fc;
        // 确保频率在有效范围内
        fc = qBound(20.0, fc, 40000.0);
        // 映射频率到0-30范围的x坐标
        double x = log10(fc / 20.0) / log10(40000.0 / 20.0) * 30.0;
        // 固定y坐标为-10
        return QPointF(x, -10.0);
    }
    return QPointF(0, -10.0);
}

// 新增：获取低通滤波点
QPointF DataModel::getLowpassPoint(int curveIndex) const {
    if (curveIndex >= 0 && curveIndex < m_curveLowpassParams.size()) {
        // 将低通频率映射到x坐标
        double fc = m_curveLowpassParams[curveIndex].fc;
        // 确保频率在有效范围内
        fc = qBound(20.0, fc, 40000.0);
        // 映射频率到0-30范围的x坐标
        double x = log10(fc / 20.0) / log10(40000.0 / 20.0) * 30.0;
        // 固定y坐标为-10
        return QPointF(x, -10.0);
    }
    return QPointF(30, -10.0);
}

// 新增：设置高通滤波点参数
void DataModel::setHighpassPointParams(int curveIndex, double frequency, FilterTypeUniversal type, int slope)
{
    if (curveIndex >= 0 && curveIndex < m_curveHighpassParams.size()) {
        // 获取当前参数
        const auto& currentParams = m_curveHighpassParams[curveIndex];

        // 检查参数是否有变化
        bool paramsChanged = false;

        if (currentParams.fc != frequency) {
            qDebug() << "[DataModel::setHighpassPointParams] 高通频率变化: " << currentParams.fc << " -> " << frequency << "Hz";
            paramsChanged = true;
        }

        if (currentParams.type != type) {
            qDebug() << "[DataModel::setHighpassPointParams] 高通类型变化: " << filterTypeToString(currentParams.type) << " -> " << filterTypeToString(type);
            paramsChanged = true;
        }

        if (currentParams.slope != slope) {
            qDebug() << "[DataModel::setHighpassPointParams] 高通斜率变化: " << currentParams.slope << " -> " << slope << "dB/oct";
            paramsChanged = true;
        }

        // 如果有参数变化，更新参数并重新生成曲线
        if (paramsChanged) {
            // 更新参数
            m_curveHighpassParams[curveIndex].fc = frequency;
            m_curveHighpassParams[curveIndex].type = type;
            m_curveHighpassParams[curveIndex].slope = slope;

            // 重新生成曲线数据
            generatePoints(curveIndex);

            // 发送高通点参数变化信号
            emit highpassPointParamsChanged(curveIndex, frequency, type, slope);

            // 发送高通点频率变化信号
            emit highpassPointChanged(curveIndex, frequency);

            // 发送数据变化信号
            emit dataChanged();
        }
    }
}

// 新增：设置低通滤波点参数
void DataModel::setLowpassPointParams(int curveIndex, double frequency, FilterTypeUniversal type, int slope)
{
    if (curveIndex >= 0 && curveIndex < m_curveLowpassParams.size()) {
        // 获取当前参数
        const auto& currentParams = m_curveLowpassParams[curveIndex];

        // 检查参数是否有变化
        bool paramsChanged = false;

        if (currentParams.fc != frequency) {
            qDebug() << "[DataModel::setLowpassPointParams] 低通频率变化: " << currentParams.fc << " -> " << frequency << "Hz";
            paramsChanged = true;
        }

        if (currentParams.type != type) {
            qDebug() << "[DataModel::setLowpassPointParams] 低通类型变化: " << filterTypeToString(currentParams.type) << " -> " << filterTypeToString(type);
            paramsChanged = true;
        }

        if (currentParams.slope != slope) {
            qDebug() << "[DataModel::setLowpassPointParams] 低通斜率变化: " << currentParams.slope << " -> " << slope << "dB/oct";
            paramsChanged = true;
        }

        // 如果有参数变化，更新参数并重新生成曲线
        if (paramsChanged) {
            // 更新参数
            m_curveLowpassParams[curveIndex].fc = frequency;
            m_curveLowpassParams[curveIndex].type = type;
            m_curveLowpassParams[curveIndex].slope = slope;

            // 重新生成曲线数据
            generatePoints(curveIndex);

            // 发送低通点参数变化信号
            emit lowpassPointParamsChanged(curveIndex, frequency, type, slope);

            // 发送低通点频率变化信号
            emit lowpassPointChanged(curveIndex, frequency);

            // 发送数据变化信号
            emit dataChanged();
        }
    }
}

// 新增：获取高通滤波点完整参数
void DataModel::getHighpassPointParams(int curveIndex, double& frequency, FilterTypeUniversal& type, int& slope) const
{
    if (curveIndex >= 0 && curveIndex < m_curveHighpassParams.size()) {
        frequency = m_curveHighpassParams[curveIndex].fc;
        type = m_curveHighpassParams[curveIndex].type;
        slope = m_curveHighpassParams[curveIndex].slope;
    } else {
        // 默认值
        frequency = 600.0;  // 默认600Hz
        type = FILTER_TYPE_BUTTERWORTH_HP;
        slope = 12;
    }
}

// 新增：获取低通滤波点完整参数
void DataModel::getLowpassPointParams(int curveIndex, double& frequency, FilterTypeUniversal& type, int& slope) const
{
    if (curveIndex >= 0 && curveIndex < m_curveLowpassParams.size()) {
        frequency = m_curveLowpassParams[curveIndex].fc;
        type = m_curveLowpassParams[curveIndex].type;
        slope = m_curveLowpassParams[curveIndex].slope;
    } else {
        // 默认值
        frequency = 10000.0;  // 默认10kHz
        type = FILTER_TYPE_BUTTERWORTH_LP;
        slope = 12;
    }
}

// 新增：设置可调节点参数（只允许设置前31个可见点）
void DataModel::setAdjustablePointParams(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type)
{

    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 只允许设置前31个可见点
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {

        // 更新详细数据
        AdjustablePointData& data = m_adjustablePointsData[curveIndex][pointIndex];
        data.frequency = std::round(frequency);
        data.qValue = qValue;
        data.gain = gain;
        data.type = type;


        // 计算并更新位置
        // 将频率映射到x坐标
        double x = 0.0;
        if (frequency >= 20.0 && frequency <= 40000.0) {
            x = 30.0 * log10(frequency / 20.0) / log10(40000.0 / 20.0);
        }

        // 将增益直接作为y坐标
        double y = gain;

        // 限制坐标范围
        x = qBound(0.0, x, 30.0);
        y = qBound(-18.0, y, 18.0);

        // 更新位置
        data.position = QPointF(x, y);
        m_adjustablePoints[curveIndex][pointIndex] = data.position;

        // 发送点位置变化信号
        emit pointPositionChanged(curveIndex, pointIndex, data.position);

        // 发送包含点编号和F、Q、G信息的信号
        emit adjustablePointChanged(curveIndex, pointIndex, frequency, qValue, gain, type);

        // 使用新的generatePoints函数重新生成该通道的曲线数据
        generatePoints(curveIndex);

        // 更新数据模型
        emit dataChanged();
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在setAdjustablePointParams中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：获取点的类型（只允许访问前31个可见点）
DataModel::EQType DataModel::getPointType(int curveIndex, int pointIndex) const {
    // 只允许访问前31个可见点
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < ADJUSTABLE_POINTS_COUNT) {
        return m_adjustablePointsData[curveIndex][pointIndex].type;
    }

    return EQ_TYPE_PEAK; // 默认返回PEAK类型
}

// 更新高通滤波点 - 只更新x坐标（频率）
void DataModel::updateHighpassPoint(int curveIndex, double xValue) {
    if (curveIndex >= 0 && curveIndex < m_curveHighpassParams.size()) {
        // 限制x值范围
        xValue = qBound(0.0, xValue, 30.0);

        // 将x坐标映射回频率 (20Hz-40000Hz)
        double fc = 20.0 * pow(2000.0, xValue / 30.0);

        // 确保频率在有效范围内
        fc = qBound(20.0, fc, 40000.0);

        // 获取低通滤波器的频率
        double lowpassFc = m_curveLowpassParams[curveIndex].fc;

        // 确保高通频率不高于低通频率
        double originalFc = fc;
        fc = qMin(fc, lowpassFc * 0.95);  // 留5%的余量

        if (originalFc != fc) {
            qDebug() << "[DataModel::updateHighpassPoint] 高通频率被限制: " << originalFc << " -> " << fc
                     << "Hz (低通频率: " << lowpassFc << "Hz)";
        }

        // 如果频率变化，则更新参数
        if (fc != m_curveHighpassParams[curveIndex].fc) {
            qDebug() << "[DataModel::updateHighpassPoint] 更新曲线" << curveIndex << "的高通点频率: "
                     << m_curveHighpassParams[curveIndex].fc << " -> " << fc << "Hz";

            // 获取当前类型和斜率
            FilterTypeUniversal type = m_curveHighpassParams[curveIndex].type;
            int slope = m_curveHighpassParams[curveIndex].slope;

            // 调用完整的参数设置函数
            setHighpassPointParams(curveIndex, fc, type, slope);

            // 使用新的generatePoints函数重新生成该通道的曲线数据
            generatePoints(curveIndex);
            emit dataChanged();
        } else {
            qDebug() << "[DataModel::updateHighpassPoint] 高通频率未变化: " << fc << "Hz";
        }
    }
}

// 更新低通滤波点 - 只更新x坐标（频率）
void DataModel::updateLowpassPoint(int curveIndex, double xValue) {
    if (curveIndex >= 0 && curveIndex < m_curveLowpassParams.size()) {
        // 限制x值范围
        xValue = qBound(0.0, xValue, 30.0);

        // 将x坐标映射回频率 (20Hz-40000Hz)
        double fc = 20.0 * pow(2000.0, xValue / 30.0);

        // 确保频率在有效范围内
        fc = qBound(20.0, fc, 40000.0);

        // 获取高通滤波器的频率
        double highpassFc = m_curveHighpassParams[curveIndex].fc;

        // 确保低通频率不低于高通频率
        double originalFc = fc;
        fc = qMax(fc, highpassFc * 1.05);  // 留5%的余量

        if (originalFc != fc) {
            qDebug() << "[DataModel::updateLowpassPoint] 低通频率被限制: " << originalFc << " -> " << fc
                     << "Hz (高通频率: " << highpassFc << "Hz)";
        }

        // 如果频率变化，则更新参数
        if (fc != m_curveLowpassParams[curveIndex].fc) {
            qDebug() << "[DataModel::updateLowpassPoint] 更新曲线" << curveIndex << "的低通点频率: "
                     << m_curveLowpassParams[curveIndex].fc << " -> " << fc << "Hz";

            // 获取当前类型和斜率
            FilterTypeUniversal type = m_curveLowpassParams[curveIndex].type;
            int slope = m_curveLowpassParams[curveIndex].slope;

            // 调用完整的参数设置函数
            setLowpassPointParams(curveIndex, fc, type, slope);

            // 使用新的generatePoints函数重新生成该通道的曲线数据
            generatePoints(curveIndex);
            emit dataChanged();
        } else {
            qDebug() << "[DataModel::updateLowpassPoint] 低通频率未变化: " << fc << "Hz";
        }
    }
}

// 新增：获取可调节点详细数据
const DataModel::AdjustablePointData& DataModel::getAdjustablePointData(int curveIndex, int pointIndex) const {
    static AdjustablePointData defaultData;
    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < m_adjustablePointsData[curveIndex].size()) {
        return m_adjustablePointsData[curveIndex][pointIndex];
    }
    return defaultData;
}

// 新增：设置可调节点详细数据
void DataModel::setAdjustablePointData(int curveIndex, int pointIndex, const AdjustablePointData& data) {
    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < m_adjustablePointsData[curveIndex].size()) {

        // 打印设置的数据
        qDebug() << "[DataModel::setAdjustablePointData] 设置曲线" << curveIndex
                 << "点" << pointIndex << "的详细数据:";
        qDebug() << "  位置: (" << data.position.x() << ", " << data.position.y() << ")";
        qDebug() << "  频率: " << data.frequency << "Hz";
        qDebug() << "  Q值: " << data.qValue;
        qDebug() << "  增益: " << data.gain << "dB";
        qDebug() << "  类型: 0x" << QString::number(static_cast<int>(data.type), 16).toUpper();

        // 保存详细数据
        m_adjustablePointsData[curveIndex][pointIndex] = data;

        // 同步更新位置数据
        m_adjustablePoints[curveIndex][pointIndex] = data.position;

        // 发送点位置变化信号
        emit pointPositionChanged(curveIndex, pointIndex, data.position);

        // 发送包含点编号和F、Q、G信息的信号
        emit adjustablePointChanged(curveIndex, pointIndex, data.frequency, data.qValue, data.gain, data.type);

        // 使用新的generatePoints函数重新生成该通道的曲线数据
        generatePoints(curveIndex);

        // 更新数据模型
        emit dataChanged();
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在setAdjustablePointData中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：设置高低通滤波点显示状态（兼容接口）
void DataModel::setFilterPointsVisible(bool visible) {
    bool needUpdate = false;

    if (m_highpassPointVisible != visible) {
        m_highpassPointVisible = visible;
        needUpdate = true;
        emit highpassPointVisibilityChanged(visible);
    }

    if (m_lowpassPointVisible != visible) {
        m_lowpassPointVisible = visible;
        needUpdate = true;
        emit lowpassPointVisibilityChanged(visible);
    }

    if (needUpdate) {
        // 更新兼容状态
        bool newFilterPointsVisible = m_highpassPointVisible && m_lowpassPointVisible;
        if (m_filterPointsVisible != newFilterPointsVisible) {
            m_filterPointsVisible = newFilterPointsVisible;
            emit filterPointsVisibilityChanged(newFilterPointsVisible);
        }

        qDebug() << "[DataModel::setFilterPointsVisible] 高低通滤波点显示状态变更为:" << visible;
        emit dataChanged();
    }
}

// 新增：设置高通滤波点显示状态
void DataModel::setHighpassPointVisible(bool visible) {
    if (m_highpassPointVisible != visible) {
        m_highpassPointVisible = visible;
        qDebug() << "[DataModel::setHighpassPointVisible] 高通滤波点显示状态变更为:" << visible;

        // 更新兼容状态
        bool newFilterPointsVisible = m_highpassPointVisible && m_lowpassPointVisible;
        if (m_filterPointsVisible != newFilterPointsVisible) {
            m_filterPointsVisible = newFilterPointsVisible;
            emit filterPointsVisibilityChanged(newFilterPointsVisible);
        }

        emit highpassPointVisibilityChanged(visible);
        emit dataChanged();
    }
}

// 新增：设置低通滤波点显示状态
void DataModel::setLowpassPointVisible(bool visible) {
    if (m_lowpassPointVisible != visible) {
        m_lowpassPointVisible = visible;
        qDebug() << "[DataModel::setLowpassPointVisible] 低通滤波点显示状态变更为:" << visible;

        // 更新兼容状态
        bool newFilterPointsVisible = m_highpassPointVisible && m_lowpassPointVisible;
        if (m_filterPointsVisible != newFilterPointsVisible) {
            m_filterPointsVisible = newFilterPointsVisible;
            emit filterPointsVisibilityChanged(newFilterPointsVisible);
        }

        emit lowpassPointVisibilityChanged(visible);
        emit dataChanged();
    }
}

// 新增：更新可调节点详细数据
void DataModel::updateAdjustablePointData(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type) {
    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        pointIndex >= 0 && pointIndex < m_adjustablePointsData[curveIndex].size()) {

        // 获取当前数据
        AdjustablePointData& currentData = m_adjustablePointsData[curveIndex][pointIndex];

        // 打印更新前后的数据
        qDebug() << "[DataModel::updateAdjustablePointData] 更新曲线" << curveIndex
                 << "点" << pointIndex << "的详细数据:";
        qDebug() << "  频率: " << currentData.frequency << " -> " << frequency << "Hz";
        qDebug() << "  Q值: " << currentData.qValue << " -> " << qValue;
        qDebug() << "  增益: " << currentData.gain << " -> " << gain << "dB";
        qDebug() << "  类型: 0x" << QString::number(static_cast<int>(currentData.type), 16).toUpper()
                 << " -> 0x" << QString::number(static_cast<int>(type), 16).toUpper();

        // 更新数据
        currentData.frequency = std::round(frequency);
        currentData.qValue = qValue;
        currentData.gain = gain;
        currentData.type = type;

        // 计算并更新位置
        // 将频率映射到x坐标
        double x = 0.0;
        if (frequency >= 20.0 && frequency <= 40000.0) {
            x = 30.0 * log10(frequency / 20.0) / log10(40000.0 / 20.0);
        }

        // 将增益直接作为y坐标
        double y = gain;

        // 限制坐标范围
        x = qBound(0.0, x, 30.0);
        y = qBound(-18.0, y, 18.0);

        // 更新位置
        currentData.position = QPointF(x, y);
        m_adjustablePoints[curveIndex][pointIndex] = currentData.position;

        // 发送点位置变化信号
        emit pointPositionChanged(curveIndex, pointIndex, currentData.position);

        // 发送包含点编号和F、Q、G信息的信号
        emit adjustablePointChanged(curveIndex, pointIndex, frequency, qValue, gain, type);

        // 使用新的generatePoints函数重新生成该通道的曲线数据
        generatePoints(curveIndex);

        // 更新数据模型
        emit dataChanged();
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在updateAdjustablePointData中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：设置32~36号不可见点参数
void DataModel::setInvisiblePointParams(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type)
{
    // 检查点索引是否在32~36范围内（数组索引31~35）
    int actualIndex = pointIndex - 1; // 点编号转为数组索引
    if (actualIndex < ADJUSTABLE_POINTS_COUNT || actualIndex >= TOTAL_POINTS_COUNT) {
        qDebug() << "[DataModel::setInvisiblePointParams] 错误：点索引" << pointIndex << "不在32~36范围内";
        return;
    }

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        actualIndex < m_adjustablePointsData[curveIndex].size()) {

        // 保存当前通道索引，确保不会被修改
        int currentLine = m_currentLine;

        // 更新详细数据
        AdjustablePointData& data = m_adjustablePointsData[curveIndex][actualIndex];
        data.frequency = std::round(frequency);
        data.qValue = qValue;
        data.gain = gain;
        data.type = type;

        // 计算并更新位置
        double x = 0.0;
        if (frequency >= 20.0 && frequency <= 40000.0) {
            x = 30.0 * log10(frequency / 20.0) / log10(40000.0 / 20.0);
        }
        double y = gain;
        x = qBound(0.0, x, 30.0);
        y = qBound(-18.0, y, 18.0);

        data.position = QPointF(x, y);
        m_adjustablePoints[curveIndex][actualIndex] = data.position;

        qDebug() << "[DataModel::setInvisiblePointParams] 设置曲线" << curveIndex << "不可见点" << pointIndex
                 << ": 频率=" << frequency << "Hz, Q=" << qValue << ", 增益=" << gain << "dB, 类型=" << static_cast<int>(type);

        // 重新生成曲线数据
        generatePoints(curveIndex);
        emit dataChanged();

        // 确保当前通道不变
        if (m_currentLine != currentLine) {
            qDebug() << "[警告] 当前通道在setInvisiblePointParams中被意外修改，恢复为原值:" << currentLine;
            m_currentLine = currentLine;
        }
    }
}

// 新增：获取32~36号不可见点参数
void DataModel::getInvisiblePointParams(int curveIndex, int pointIndex, double& frequency, double& qValue, double& gain, EQType& type) const
{
    // 检查点索引是否在32~36范围内（数组索引31~35）
    int actualIndex = pointIndex - 1; // 点编号转为数组索引
    if (actualIndex < ADJUSTABLE_POINTS_COUNT || actualIndex >= TOTAL_POINTS_COUNT) {
        qDebug() << "[DataModel::getInvisiblePointParams] 错误：点索引" << pointIndex << "不在32~36范围内";
        frequency = 1000.0;
        qValue = 4.32;
        gain = 0.0;
        type = EQ_TYPE_PEAK;
        return;
    }

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        actualIndex < m_adjustablePointsData[curveIndex].size()) {
        const AdjustablePointData& data = m_adjustablePointsData[curveIndex][actualIndex];
        frequency = data.frequency;
        qValue = data.qValue;
        gain = data.gain;
        type = data.type;
    } else {
        // 默认值
        frequency = 1000.0;
        qValue = 4.32;
        gain = 0.0;
        type = EQ_TYPE_PEAK;
    }
}

// 新增：更新32~36号不可见点数据
void DataModel::updateInvisiblePointData(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type)
{
    setInvisiblePointParams(curveIndex, pointIndex, frequency, qValue, gain, type);
}

// 新增：获取32~36号不可见点详细数据
const DataModel::AdjustablePointData& DataModel::getInvisiblePointData(int curveIndex, int pointIndex) const
{
    static AdjustablePointData defaultData;

    // 检查点索引是否在32~36范围内（数组索引31~35）
    int actualIndex = pointIndex - 1; // 点编号转为数组索引
    if (actualIndex < ADJUSTABLE_POINTS_COUNT || actualIndex >= TOTAL_POINTS_COUNT) {
        qDebug() << "[DataModel::getInvisiblePointData] 错误：点索引" << pointIndex << "不在32~36范围内";
        return defaultData;
    }

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size() &&
        actualIndex < m_adjustablePointsData[curveIndex].size()) {
        return m_adjustablePointsData[curveIndex][actualIndex];
    }
    return defaultData;
}

// 新增：批量设置某个通道的所有不可见点
void DataModel::setInvisiblePointsForCurve(int curveIndex, const QVector<AdjustablePointData>& invisiblePointsData)
{
    if (invisiblePointsData.size() != INVISIBLE_POINTS_COUNT) {
        qDebug() << "[DataModel::setInvisiblePointsForCurve] 错误：提供的不可见点数据数量" << invisiblePointsData.size()
                 << "不等于期望的" << INVISIBLE_POINTS_COUNT;
        return;
    }

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size()) {
        // 保存当前通道索引，确保不会被修改
        int currentLine = m_currentLine;

        // 更新32~36号点的数据
        for (int i = 0; i < INVISIBLE_POINTS_COUNT; ++i) {
            int actualIndex = ADJUSTABLE_POINTS_COUNT + i; // 数组索引31~35
            if (actualIndex < m_adjustablePointsData[curveIndex].size()) {
                const AdjustablePointData& newData = invisiblePointsData[i];

                // 更新详细数据
                AdjustablePointData& data = m_adjustablePointsData[curveIndex][actualIndex];
                data.frequency = newData.frequency;
                data.qValue = newData.qValue;
                data.gain = newData.gain;
                data.type = newData.type;

                // 计算并更新位置
                double x = 0.0;
                if (newData.frequency >= 20.0 && newData.frequency <= 40000.0) {
                    x = 30.0 * log10(newData.frequency / 20.0) / log10(40000.0 / 20.0);
                }
                double y = newData.gain;
                x = qBound(0.0, x, 30.0);
                y = qBound(-18.0, y, 18.0);

                data.position = QPointF(x, y);
                m_adjustablePoints[curveIndex][actualIndex] = data.position;

                qDebug() << "[DataModel::setInvisiblePointsForCurve] 曲线" << curveIndex << "不可见点" << (actualIndex + 1)
                         << ": 频率=" << newData.frequency << "Hz, Q=" << newData.qValue
                         << ", 增益=" << newData.gain << "dB, 类型=" << static_cast<int>(newData.type);
            }
        }

        // 重新生成曲线数据
        generatePoints(curveIndex);
        emit dataChanged();

        // 确保当前通道不变
        if (m_currentLine != currentLine) {
            qDebug() << "[警告] 当前通道在setInvisiblePointsForCurve中被意外修改，恢复为原值:" << currentLine;
            m_currentLine = currentLine;
        }
    }
}

// 新增：获取某个通道的所有不可见点数据
QVector<DataModel::AdjustablePointData> DataModel::getInvisiblePointsForCurve(int curveIndex) const
{
    QVector<AdjustablePointData> invisiblePoints;

    if (curveIndex >= 0 && curveIndex < m_adjustablePointsData.size()) {
        // 获取32~36号点的数据
        for (int i = 0; i < INVISIBLE_POINTS_COUNT; ++i) {
            int actualIndex = ADJUSTABLE_POINTS_COUNT + i; // 数组索引31~35
            if (actualIndex < m_adjustablePointsData[curveIndex].size()) {
                invisiblePoints.append(m_adjustablePointsData[curveIndex][actualIndex]);
            }
        }
    }

    return invisiblePoints;
}

// 新增：初始化预设数据
void DataModel::initPresetData()
{
    // 初始化每条曲线的预设状态
    m_curvePresetTypes.fill(PRESET_TYPE_FLAT, MAX_CURVES);
    m_curvePresetLevels.fill(PRESET_LEVEL_1, MAX_CURVES);
    m_curvePresetEnabled.fill(false, MAX_CURVES);

    qDebug() << "[DataModel::initPresetData] 预设状态初始化完成，使用PresetManager作为数据源";
}

// 新增：设置某个通道的预设
void DataModel::setCurvePreset(int curveIndex, PresetType type, PresetLevel level)
{
    if (curveIndex < 0 || curveIndex >= MAX_CURVES) {
        qDebug() << "[DataModel::setCurvePreset] 错误：通道索引" << curveIndex << "超出范围";
        return;
    }

    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 确保预设状态向量有足够的空间
    if (m_curvePresetTypes.size() <= curveIndex) {
        m_curvePresetTypes.resize(curveIndex + 1);
        m_curvePresetLevels.resize(curveIndex + 1);
        m_curvePresetEnabled.resize(curveIndex + 1);
    }

    // 检查是否需要更新
    bool needUpdate = false;
    if (!m_curvePresetEnabled[curveIndex] ||
        m_curvePresetTypes[curveIndex] != type ||
        m_curvePresetLevels[curveIndex] != level) {
        needUpdate = true;
    }

    if (needUpdate) {
        qDebug() << "[DataModel::setCurvePreset] 设置通道" << curveIndex << "的预设:"
                 << presetTypeToString(type) << presetLevelToString(level);

        // 更新预设状态
        m_curvePresetTypes[curveIndex] = type;
        m_curvePresetLevels[curveIndex] = level;
        m_curvePresetEnabled[curveIndex] = true;

        // 转换DataModel枚举到PresetManager枚举
        PresetManager::PresetType pmType = static_cast<PresetManager::PresetType>(type);
        PresetManager::PresetLevel pmLevel = static_cast<PresetManager::PresetLevel>(level);

        // 从PresetManager获取预设数据
        // PresetManager::PresetData presetData = m_presetManager->getPresetData(pmType, pmLevel);

        // 临时屏蔽预设数据，全取0
        PresetManager::PresetData presetData = m_presetManager->getPresetData(PresetManager::PresetType::Flat, pmLevel);

        qDebug() << "[DataModel::setCurvePreset] 获取到预设数据："
                 << "100Hz=" << presetData.gain100Hz
                 << "315Hz=" << presetData.gain315Hz
                 << "1250Hz=" << presetData.gain1250Hz
                 << "3150Hz=" << presetData.gain3150Hz
                 << "8000Hz=" << presetData.gain8000Hz;

        // 构建频率和增益数组
        QVector<double> frequencies = {100.0, 315.0, 1250.0, 3150.0, 8000.0};
        QVector<double> gainValues = {
            presetData.gain100Hz,
            presetData.gain315Hz,
            presetData.gain1250Hz,
            presetData.gain3150Hz,
            presetData.gain8000Hz
        };

        // 更新32~36号不可见点
        for (int i = 0; i < INVISIBLE_POINTS_COUNT && i < gainValues.size() && i < frequencies.size(); ++i) {
            int pointIndex = 32 + i; // 点编号32~36
            double frequency = frequencies[i];
            double gain = gainValues[i];
            double qValue = 0.5; // preset 默认Q值
            EQType eqType = EQ_TYPE_PEAK; // 默认类型

            qDebug() << "[DataModel::setCurvePreset] 更新点" << pointIndex
                     << ": 频率=" << frequency << "Hz, 增益=" << gain << "dB";

            // 调用已有的接口设置不可见点参数
            setInvisiblePointParams(curveIndex, pointIndex, frequency, qValue, gain, eqType);
        }

        // 重新生成曲线数据
        generatePoints(curveIndex);

        // 发送预设变化信号
        emit curvePresetChanged(curveIndex, type, level);
        emit dataChanged();
    } else {
        qDebug() << "[DataModel::setCurvePreset] 通道" << curveIndex << "的预设无需更新";
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在setCurvePreset中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：获取某个通道的预设类型
DataModel::PresetType DataModel::getCurvePresetType(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_curvePresetTypes.size()) {
        return m_curvePresetTypes[curveIndex];
    }
    return PRESET_TYPE_FLAT; // 默认返回Flat（关闭状态）
}

// 新增：获取某个通道的预设等级
DataModel::PresetLevel DataModel::getCurvePresetLevel(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_curvePresetLevels.size()) {
        return m_curvePresetLevels[curveIndex];
    }
    return PRESET_LEVEL_1; // 默认返回Level1
}

// 新增：检查某个通道是否启用了预设
bool DataModel::isCurvePresetEnabled(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_curvePresetEnabled.size()) {
        return m_curvePresetEnabled[curveIndex];
    }
    return false; // 默认返回未启用
}

// 新增：检查某个通道是否启用了非Flat预设
bool DataModel::isCurvePresetActive(int curveIndex) const
{
    if (curveIndex >= 0 && curveIndex < m_curvePresetEnabled.size() &&
        curveIndex < m_curvePresetTypes.size()) {
        return m_curvePresetEnabled[curveIndex] &&
               m_curvePresetTypes[curveIndex] != PRESET_TYPE_FLAT;
    }
    return false; // 默认返回未激活
}

// 新增：清除某个通道的预设
void DataModel::clearCurvePreset(int curveIndex)
{
    if (curveIndex < 0 || curveIndex >= MAX_CURVES) {
        qDebug() << "[DataModel::clearCurvePreset] 错误：通道索引" << curveIndex << "超出范围";
        return;
    }

    // 保存当前通道索引，确保不会被修改
    int currentLine = m_currentLine;

    // 确保预设状态向量有足够的空间
    if (m_curvePresetEnabled.size() <= curveIndex) {
        m_curvePresetEnabled.resize(curveIndex + 1);
    }

    // 检查是否需要更新（如果已经是Flat类型则无需更新）
    bool needUpdate = false;
    if (!m_curvePresetEnabled[curveIndex] || m_curvePresetTypes[curveIndex] != PRESET_TYPE_FLAT) {
        needUpdate = true;
    }

    if (needUpdate) {
        qDebug() << "[DataModel::clearCurvePreset] 设置通道" << curveIndex << "为Flat预设";

        // 设置为Flat预设
        m_curvePresetTypes[curveIndex] = PRESET_TYPE_FLAT;
        m_curvePresetLevels[curveIndex] = PRESET_LEVEL_1; // Flat预设默认Level1
        m_curvePresetEnabled[curveIndex] = true; // 启用Flat预设

        // 应用Flat预设数据
        PresetManager::PresetData flatData = m_presetManager->getPresetData(PresetManager::Flat, PresetManager::Level1);
        QVector<double> frequencies = {100.0, 315.0, 1250.0, 3150.0, 8000.0};
        QVector<double> gainValues = {
            flatData.gain100Hz,
            flatData.gain315Hz,
            flatData.gain1250Hz,
            flatData.gain3150Hz,
            flatData.gain8000Hz
        };

        // 更新32~36号不可见点
        for (int i = 0; i < INVISIBLE_POINTS_COUNT && i < gainValues.size() && i < frequencies.size(); ++i) {
            int pointIndex = 32 + i; // 点编号32~36
            double frequency = frequencies[i];
            double gain = gainValues[i]; // 对于Empty预设，这些值都是0
            double qValue = 4.32; // 默认Q值
            EQType eqType = EQ_TYPE_PEAK; // 默认类型

            qDebug() << "[DataModel::clearCurvePreset] 设置点" << pointIndex
                     << ": 频率=" << frequency << "Hz, 增益=" << gain << "dB (Flat预设)";

            // 调用已有的接口设置不可见点参数
            setInvisiblePointParams(curveIndex, pointIndex, frequency, qValue, gain, eqType);
        }

        // 重新生成曲线数据
        generatePoints(curveIndex);

        // 发送预设变化信号（Flat预设）
        emit curvePresetChanged(curveIndex, PRESET_TYPE_FLAT, PRESET_LEVEL_1);
        emit dataChanged();
    } else {
        qDebug() << "[DataModel::clearCurvePreset] 通道" << curveIndex << "已经是Flat预设状态";
    }

    // 确保当前通道不变
    if (m_currentLine != currentLine) {
        qDebug() << "[警告] 当前通道在clearCurvePreset中被意外修改，恢复为原值:" << currentLine;
        m_currentLine = currentLine;
    }
}

// 新增：预设类型转换为字符串
QString DataModel::presetTypeToString(PresetType type) const
{
    switch (type) {
    case PRESET_TYPE_FLAT:      return "Flat";
    case PRESET_TYPE_SUPERBASS: return "SuperBass";
    case PRESET_TYPE_POWERFUL:  return "Powerful";
    case PRESET_TYPE_VOCAL:     return "Vocal";
    case PRESET_TYPE_NATURAL:   return "Natural";
    default:                    return QString("Unknown(%1)").arg(static_cast<int>(type));
    }
}

// 新增：预设等级转换为字符串
QString DataModel::presetLevelToString(PresetLevel level) const
{
    switch (level) {
    case PRESET_LEVEL_1: return "Level1";
    case PRESET_LEVEL_2: return "Level2";
    case PRESET_LEVEL_3: return "Level3";
    case PRESET_LEVEL_4: return "Level4";
    case PRESET_LEVEL_5: return "Level5";
    default:             return QString("Unknown(%1)").arg(static_cast<int>(level));
    }
}
