#include "Mid02Handler.h"

Mid02Handler::Mid02Handler()
{
    for(uint8_t channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
    {
        ChannelOperation0102 temp12;
        memset(&temp12, 0, sizeof(ChannelOperation0102));
        temp12.channel = channel;
        mChannelOperation02.append(temp12);

        ChannelOperation0708 temp78;
        memset(&temp78, 0, sizeof(ChannelOperation0708));
        temp78.channel = channel;
        mChannelOperation08.append(temp78);
    }
}

void Mid02Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(ChannelOperation::SID::OUTPUT_CHANNEL_QUERY):
    {
        parse02Data(data);
        break;
    }
    case static_cast<uint8_t>(ChannelOperation::SID::CHANNEL_CROSSOVER_QUERY):
    {
        parse08Data(data);
        break;
    }
    default:
        break;
    }
}

void Mid02Handler::parse02Data(const QByteArray &data)
{
    // bool ret = false;
    ChannelOperation0102 temp02;
    memcpy(&temp02, data.data(), data.size());
    if(OUTPUT_CHANNEL_MAX <= temp02.channel)
    {
        qDebug("ChannelOperation0102 channel >= OUTPUT_CHANNEL_MAX, channel: %x", temp02.channel);
        return;
    }

    // if(temp02.type.value != mChannelOperation02[temp02.channel].type.value)
    // {
        emit outputChannelTypeChanged(temp02.channel, temp02.type.value);
    //     ret = true;
    // }
    // if(temp02.gain != mChannelOperation02[temp02.channel].gain)
    // {
        emit outputChannelGainChanged(temp02.channel, temp02.gain);
    //     ret = true;
    // }
    // if(temp02.positiveNegative != mChannelOperation02[temp02.channel].positiveNegative)
    // {
        emit outputChannelPositiveNegativeChanged(temp02.channel, temp02.positiveNegative);
    //     ret = true;
    // }
    // if(temp02.mute != mChannelOperation02[temp02.channel].mute)
    // {
        emit outputChannelMuteChanged(temp02.channel, temp02.mute);
    //     ret = true;
    // }
    // if(temp02.delay != mChannelOperation02[temp02.channel].delay)
    // {
        emit outputChannelDelayChanged(temp02.channel, temp02.delay);
    //     ret = true;
    // }
    // if(temp02.delayGroup != mChannelOperation02[temp02.channel].delayGroup)
    // {
        emit outputChannelDelayGroupChanged(temp02.channel, temp02.delayGroup);
    //     ret = true;
    // }
    // if(temp02.eqSet != mChannelOperation02[temp02.channel].eqSet)
    // {
        emit outputChannelEqSetChanged(temp02.channel, temp02.eqSet);
    //     ret = true;
    // }
    // if(temp02.eqType != mChannelOperation02[temp02.channel].eqType)
    // {
        emit outputChannelEqTypeChanged(temp02.channel, temp02.eqType);
    //     ret = true;
    // }
    // if(temp02.linkType != mChannelOperation02[temp02.channel].linkType)
    // {
        emit outputChannelLinkTypeChanged(temp02.channel, temp02.linkType);
    //     ret = true;
    // }
    // if(temp02.linkChannel != mChannelOperation02[temp02.channel].linkChannel)
    // {
        emit outputChannelLinkChannelChanged(temp02.channel, temp02.linkChannel);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mChannelOperation02[temp02.channel], data.data(), data.size());
    // }
}

void Mid02Handler::parse08Data(const QByteArray &data)
{
    // bool ret = false;
    ChannelOperation0708 temp08;
    memcpy(&temp08, data.data(), data.size());
    if(OUTPUT_CHANNEL_MAX <= temp08.channel)
    {
        qDebug("ChannelOperation0708 channel >= OUTPUT_CHANNEL_MAX, channel: %x", temp08.channel);
        return;
    }

    // if(temp08.hpFreq != mChannelOperation08[temp08.channel].hpFreq)
    // {
        emit outputChannelHpFreqChanged(temp08.channel, temp08.hpFreq);
    //     ret = true;
    // }
    // if(temp08.hpSlope != mChannelOperation08[temp08.channel].hpSlope)
    // {
        emit outputChannelHpSlopeChanged(temp08.channel, temp08.hpSlope);
    //     ret = true;
    // }
    // if(temp08.hpType != mChannelOperation08[temp08.channel].hpType)
    // {
        emit outputChannelHpTypeChanged(temp08.channel, temp08.hpType);
    //     ret = true;
    // }
    // if(temp08.hpEnable != mChannelOperation08[temp08.channel].hpEnable)
    // {
        emit outputChannelHpEnableChanged(temp08.channel, temp08.hpEnable);
    //     ret = true;
    // }
    // if(temp08.lpFreq != mChannelOperation08[temp08.channel].lpFreq)
    // {
        emit outputChannelLpFreqChanged(temp08.channel, temp08.lpFreq);
    //     ret = true;
    // }
    // if(temp08.lpSlope != mChannelOperation08[temp08.channel].lpSlope)
    // {
        emit outputChannelLpSlopeChanged(temp08.channel, temp08.lpSlope);
    //     ret = true;
    // }
    // if(temp08.lpType != mChannelOperation08[temp08.channel].lpType)
    // {
        emit outputChannelLpTypeChanged(temp08.channel, temp08.lpType);
    //     ret = true;
    // }
    // if(temp08.lpEnable != mChannelOperation08[temp08.channel].lpEnable)
    // {
        emit outputChannelLpEnableChanged(temp08.channel, temp08.lpEnable);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mChannelOperation08[temp08.channel], data.data(), data.size());
    // }
}

QByteArray Mid02Handler::send01Data(const ChannelOperation0102 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(ChannelOperation0102)));

    return frameData;
}

QByteArray Mid02Handler::send02Data(uint8_t channel)
{
    QByteArray frameData;
    frameData.append(channel);

    return frameData;
}

const QVector<ChannelOperation0102>& Mid02Handler::getChannelOperation0102Vector()
{
    return mChannelOperation02;
}

const QVector<ChannelOperation0708>& Mid02Handler::getChannelOperation0708Vector()
{
    return mChannelOperation08;
}

QByteArray Mid02Handler::send07Data(const ChannelOperation0708 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(ChannelOperation0708)));

    return frameData;
}

QByteArray Mid02Handler::send08Data(uint8_t channel)
{
    QByteArray frameData;
    frameData.append(channel);

    return frameData;
}
