import QtQuick
import QtQuick.Controls.Basic

ComboBox {
    id: control
    implicitHeight: 24
    leftPadding: 0
    rightPadding: 0
    font.family: "Segoe UI"
    font.pixelSize: 10
    opacity: enabled ? 1 : 0.3

    delegate: ItemDelegate {
        id: delegate
        implicitWidth: control.width
        implicitHeight: control.height
        highlighted: control.highlightedIndex === index
        leftPadding: control.leftPadding
        rightPadding: control.rightPadding
        opacity: enabled ? 1 : 0.3

        required property var model
        required property int index

        contentItem: Item {
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font: control.font
                verticalAlignment: Text.AlignVCenter
                horizontalAlignment: Text.AlignHCenter
                text: delegate.model[control.textRole]
            }
        }

        background: Rectangle {
            color: delegate.hovered ? "#646870" : "#484C54"
        }
    }

    indicator: Item {
        anchors.verticalCenter: control.verticalCenter
        anchors.right: control.right
        width: 0
        height: control.height
    }

    contentItem: Text {
        // height: 8
        color: "#E8E8E8"
        font: control.font
        verticalAlignment: Text.AlignVCenter
        horizontalAlignment: Text.AlignHCenter
        text: control.displayText
    }

    background: Rectangle {
        // color: control.hovered ? "#6C7078" : control.pressed ? "#545860" : "#5C6068"
        color: "transparent"
        border.color: control.hovered ? "#7C8088" : "#747880"
        border.width: 1
    }

    popup: Popup {
        y: control.height - 1
        width: control.width
        height: Math.min(contentItem.implicitHeight + padding * 2, control.Window.height - topMargin - bottomMargin - control.height)
        padding: 1

        contentItem: ListView {
            clip: true
            implicitHeight: contentHeight
            model: control.delegateModel
            currentIndex: control.highlightedIndex
            ScrollIndicator.vertical: ScrollIndicator { }
        }

        background: Rectangle {
            border.width: 1
            border.color: "#646870"
            // color: "#484C54"
        }
    }
}
