/*
 * Copyright (c) 2025 , NIO , LTD 
 * All Rights Reserved.
 * Product      : 
 * Component ID : 
 * File Name    : bessel_filter_wrapper.cpp
 * Description  : 贝塞尔滤波器C++包装器，采用与test_bessel_filter.cpp相同的调用方式
 * History      : 
 * Version		date		author		context
 * v1.0.0 		<date>		<user>		<context>
 */

#include "BesselFilter.h"
#include "bessel_filter.h"
#include <vector>
#include <complex>
#include <cmath>

using namespace BesselFilter;

extern "C" {

/**
 * @brief 贝塞尔滤波器频率响应计算（采用与test_bessel_filter.cpp完全相同的调用方式）
 */
void call_bessel_filter_cpp(int type, double fc, int slope_rate,
                           const DoubleArray* freq_list,
                           double_complex* resp) {
    try {
        // 转换参数 - 与test_bessel_filter.cpp完全一致
        int order = slope_rate / 6;  // 斜率转阶数
        if (order < 1) order = 1;
        if (order > 8) order = 8;
        
        FilterType filter_type = (type == FILTER_TYPE_BESSEL_LP) ?
            FilterType::LOWPASS : FilterType::HIGHPASS;

        // 使用与test_bessel_filter.cpp完全相同的调用方式
        // 注意：使用FS (96000.0) 作为采样率，与系统保持一致
        auto coeffs = Designer::design(fc, order, filter_type, FS);
        
        // 转换频率列表 - 与test_bessel_filter.cpp完全相同的方式
        std::vector<double> frequencies;
        frequencies.reserve(freq_list->size);
        for (size_t i = 0; i < freq_list->size; ++i) {
            frequencies.push_back(freq_list->data[i]);
        }
        
        // 获取频率响应 - 与test_bessel_filter.cpp完全相同的调用方式
        auto response = Designer::getFrequencyResponse(coeffs, frequencies, FS);
        
        // 转换响应到原有格式（累乘到现有响应）
        // 使用与test_bessel_filter.cpp完全相同的数据处理方式
        for (size_t i = 0; i < freq_list->size && i < response.magnitude_db.size(); ++i) {
            // 将dB转换为线性幅度
            double magnitude = std::pow(10.0, response.magnitude_db[i] / 20.0);
            // 将度转换为弧度
            double phase_rad = response.phase_deg[i] * M_PI / 180.0;
            
            // 创建复数响应 - 与test_bessel_filter.cpp中的复数处理方式完全一致
            std::complex<double> filter_resp(magnitude * std::cos(phase_rad), 
                                           magnitude * std::sin(phase_rad));
            
            // 累乘到现有响应
            resp[i] = resp[i] * filter_resp;
        }
        
    } catch (const std::exception& e) {
        // 如果BesselFilter.cpp调用失败，将响应设为1（无滤波效果）
        for (size_t i = 0; i < freq_list->size; ++i) {
            resp[i] = resp[i] * std::complex<double>(1.0, 0.0);
        }
    }
}

/**
 * @brief 贝塞尔滤波器系数计算（采用与test_bessel_filter.cpp相同的调用方式）
 */
void call_bessel_section_cpp(int order, int section, double fs, double fc,
                            BiquadCoeffs* coeffs, int type) {
    try {
        FilterType filter_type = (type == FILTER_TYPE_BESSEL_LP) ?
            FilterType::LOWPASS : FilterType::HIGHPASS;

        // 直接使用BesselFilter.cpp的实现
        auto filter_coeffs = Designer::design(fc, order, filter_type, fs);

        // 数据格式转换：将BesselFilter的系数转换为BiquadCoeffs格式
        if (order == 1) {
            // 一阶滤波器
            if (filter_coeffs.b.size() >= 2 && filter_coeffs.a.size() >= 2) {
                coeffs->b1st[0] = filter_coeffs.b[0];
                coeffs->b1st[1] = filter_coeffs.b[1];
                coeffs->a1st[0] = filter_coeffs.a[0];
                coeffs->a1st[1] = filter_coeffs.a[1];
            }
        } else {
            // 二阶及高阶滤波器
            if (filter_coeffs.b.size() >= 3 && filter_coeffs.a.size() >= 3) {
                coeffs->b[0] = filter_coeffs.b[0];
                coeffs->b[1] = filter_coeffs.b[1];
                coeffs->b[2] = filter_coeffs.b[2];
                coeffs->a[0] = filter_coeffs.a[0];
                coeffs->a[1] = filter_coeffs.a[1];
                coeffs->a[2] = filter_coeffs.a[2];
            } else {
                // 回退到单位滤波器
                coeffs->b[0] = 1.0;
                coeffs->b[1] = 0.0;
                coeffs->b[2] = 0.0;
                coeffs->a[0] = 1.0;
                coeffs->a[1] = 0.0;
                coeffs->a[2] = 0.0;
            }
        }
    } catch (...) {
        // 回退到单位滤波器
        coeffs->b[0] = 1.0;
        coeffs->b[1] = 0.0;
        coeffs->b[2] = 0.0;
        coeffs->a[0] = 1.0;
        coeffs->a[1] = 0.0;
        coeffs->a[2] = 0.0;
    }
}

} // extern "C"
