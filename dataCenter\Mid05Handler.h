#ifndef MID05HANDLER_H
#define MID05HANDLER_H

#include "DataHandlerAbstract.h"

class Mid05Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid05Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    QByteArray send01Data();
    void send02Data(uint8_t type, const QByteArray &data, QList<QByteArray> &frameDatas);
    QByteArray send03Data();

signals:
    void dspMainVersionChanged(uint8_t version);
    void dspSubVersionChanged(uint8_t version);
    void remoteMainVersionChanged(uint8_t version);
    void remoteSubVersionChanged(uint8_t version);

    void upgradeError(uint8_t code);
    void upgradeProgress(uint8_t progress);

    void upgradeStatus(uint8_t status);

private:
    void parse01Data(const QByteArray &data);
    void parse02Data(const QByteArray &data);
    void parse03Data(const QByteArray &data);

};

#endif // MID05HANDLER_H
