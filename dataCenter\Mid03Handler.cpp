#include "Mid03Handler.h"

Mid03Handler::Mid03<PERSON><PERSON><PERSON>()
{

}

void Mid03Handler::onInit(int deviceLevel)
{
    mDeviceLevel = deviceLevel;
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;
    int mixHighChannelMax = (1 == deviceLevel) ? MIX_HIGH_CHANNEL_L_MAX : MIX_HIGH_CHANNEL_MAX;

    memset(&mAudioOperation0102, 0, sizeof(mAudioOperation0102));
    memset(&mAudioOperation0304, 0, sizeof(mAudioOperation0304));

    QVector<Mix> gains(outputChannelMax);
    QMap<uint8_t, QVector<Mix>> high;
    for(int channel = 0; channel < mixHighChannelMax; channel++)
    {
        high.insert(channel, gains);
    }
    QMap<uint8_t, QVector<Mix>> rca;
    for(int channel = 0; channel < MIX_RCA_CHANNEL_MAX; channel++)
    {
        rca.insert(channel, gains);
    }
    QMap<uint8_t, QVector<Mix>> dsp;
    for(int channel = 0; channel < MIX_DSP_CHANNEL_MAX; channel++)
    {
        dsp.insert(channel, gains);
    }
    mMixMap[static_cast<uint8_t>(MixTypeEnum::HIGH)] = high;
    mMixMap[static_cast<uint8_t>(MixTypeEnum::RCA)] = rca;
    mMixMap[static_cast<uint8_t>(MixTypeEnum::DSP)] = dsp;

    for(int channel = 0; channel < outputChannelMax; channel++)
    {
        QVector<EqParm> eqs(EQ_BAND_MAX);
        mEqParmMap[channel] = eqs;
    }

    for(int type = 0; type < 2; type++)
    {
        QVector<MemorySetting> memories(MEMORY_MAX);
        mMemorySettingMap[type] = memories;
    }

    memset(&mAudioOperation0E0F, 0, sizeof(mAudioOperation0E0F));
}

void Mid03Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(AudioOperation::SID::MAIN_VOLUME_QUERY):
    {
        parse02Data(data);
        break;
    }
    case static_cast<uint8_t>(AudioOperation::SID::MUSIC_SOURCE_QUERY):
    {
        parse04Data(data);
        break;
    }
    case static_cast<uint8_t>(AudioOperation::SID::CHANNEL_MIX_QUERY):
    {
        parse06Data(data);
        break;
    }
    case static_cast<uint8_t>(AudioOperation::SID::EQ_QUERY):
    {
        parse08Data(data);
        break;
    }
    case static_cast<uint8_t>(AudioOperation::SID::MEMORY_QUERY):
    {
        parse0BData(data);
        break;
    }
    case static_cast<uint8_t>(AudioOperation::SID::PRESET_QUERY):
    {
        parse0FData(data);
        break;
    }
    default:
        break;
    }
}

void Mid03Handler::parse02Data(const QByteArray &data)
{
    // bool ret = false;
    AudioOperation0102 temp02;
    memcpy(&temp02, data.data(), data.size());

    // if(temp02.mainGain != mAudioOperation0102.mainGain)
    // {
        emit mainGainChanged(temp02.mainGain);
    //     ret = true;
    // }
    // if(temp02.mainMute != mAudioOperation0102.mainMute)
    // {
        emit mainMuteChanged(temp02.mainMute);
    //     ret = true;
    // }
    // if(temp02.dspGain != mAudioOperation0102.dspGain)
    // {
        emit dspGainChanged(temp02.dspGain);
    //     ret = true;
    // }
    // if(temp02.dspMute != mAudioOperation0102.dspMute)
    // {
        emit dspMuteChanged(temp02.dspMute);
    //     ret = true;
    // }
    // if(temp02.bassLevel != mAudioOperation0102.bassLevel)
    // {
        emit bassLevelChanged(temp02.bassLevel);
    //     ret = true;
    // }
    // if(temp02.bassMute != mAudioOperation0102.bassMute)
    // {
        emit bassMuteChanged(temp02.bassMute);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mAudioOperation0102, data.data(), data.size());
    // }
}

void Mid03Handler::parse04Data(const QByteArray &data)
{
    // bool ret = false;
    AudioOperation0304 temp04;
    memcpy(&temp04, data.data(), data.size());

    // if(temp04.source != mAudioOperation0304.source)
    // {
        emit currentSourceChanged(temp04.source);
    //     ret = true;
    // }
    // if(temp04.memory != mAudioOperation0304.memory)
    // {
        emit currentMemoryChanged(temp04.memory);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mAudioOperation0304, data.data(), data.size());
    // }
}

void Mid03Handler::parse06Data(const QByteArray &data)
{
    uint8_t source = data.at(0);
    uint8_t startChannel = data.at(1);
    uint8_t num = data.at(2);
    QByteArray gains = data.mid(3, (data.size() - 3));

    int outputChannelMax = (1 == mDeviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;
    for(int input = 0; input < num; input++)
    {
        for(int output = 0; output < outputChannelMax; output++)
        {
            Mix gain;
            gain.value = gains.at((input * outputChannelMax) + output);
            // if(mMixMap[source][startChannel + input][output].bits.gain != gain.bits.gain)
            // {
                mMixMap[source][startChannel + input][output].bits.gain = gain.bits.gain;
                emit mixGainChanged(source, (startChannel + input), output, gain.bits.gain);
            // }
            // if(mMixMap[source][startChannel + input][output].bits.enable != gain.bits.enable)
            // {
                mMixMap[source][startChannel + input][output].bits.enable = gain.bits.enable;
                emit mixEnableChanged(source, (startChannel + input), output, gain.bits.enable);
            // }
        }
    }
}

void Mid03Handler::parse08Data(const QByteArray &data)
{
    uint8_t channel = data.at(0);
    uint8_t startBand = data.at(1);
    uint8_t num = data.at(2);
    QByteArray eqParms = data.mid(3, (data.size() - 3));
    for(int band = 0; band < num; band++)
    {
        EqParm eq;
        memcpy(&eq, eqParms.mid(band * sizeof(EqParm), sizeof(EqParm)), sizeof(EqParm));
        // if(mEqParmMap[channel][startBand + band].type.bits.type != eq.type.bits.type)
        // {
            mEqParmMap[channel][startBand + band].type.bits.type = eq.type.bits.type;
            emit eqTypeChanged(channel, (startBand + band), eq.type.bits.type);
        // }
        // if(mEqParmMap[channel][startBand + band].freq != eq.freq)
        // {
            mEqParmMap[channel][startBand + band].freq = eq.freq;
            emit eqFreqChanged(channel, (startBand + band), eq.freq);
        // }
        // if(mEqParmMap[channel][startBand + band].qValue != eq.qValue)
        // {
            mEqParmMap[channel][startBand + band].qValue = eq.qValue;
            emit eqQValueChanged(channel, (startBand + band), eq.qValue);
        // }
        // if(mEqParmMap[channel][startBand + band].gain != eq.gain)
        // {
            mEqParmMap[channel][startBand + band].gain = eq.gain;
            emit eqGainChanged(channel, (startBand + band), eq.gain);
        // }
        // if(mEqParmMap[channel][startBand + band].type.bits.enable != eq.type.bits.enable)
        // {
            mEqParmMap[channel][startBand + band].type.bits.enable = eq.type.bits.enable;
            emit eqEnableChanged(channel, (startBand + band), eq.type.bits.enable);
        // }
    }
}

void Mid03Handler::parse0BData(const QByteArray &data)
{
    uint8_t startMemory = data.at(0);
    uint8_t type = data.at(1);
    uint8_t num = data.at(2);
    QByteArray memories = data.mid(3, (data.size() - 3));
    for(int index = 0; index < num; index++)
    {
        MemorySetting memory;
        memcpy(&memory, memories.mid(index * sizeof(MemorySetting), sizeof(MemorySetting)), sizeof(MemorySetting));
        int memoryId = (MEMORY_MAX > (startMemory + index)) ?
                           (startMemory + index) : (startMemory + index - MEMORY_MAX);
        // if(mMemorySettingMap[type][memoryId].enable != memory.enable)
        // {
            mMemorySettingMap[type][memoryId].enable = memory.enable;
            emit memoryEnableChanged(type, (memoryId), memory.enable);
        // }
        // if(QString::fromLocal8Bit(reinterpret_cast<const char*>(mMemorySettingMap[type][memoryId].name))
        //     != QString::fromLocal8Bit(reinterpret_cast<const char*>(memory.name)))
        // {
            memcpy(mMemorySettingMap[type][memoryId].name, memory.name, 32);

            // QByteArray nameArray = QByteArray::fromRawData(reinterpret_cast<const char*>(memory.name), 32);
            // auto toUtf16 = QStringDecoder(QStringDecoder::Utf16);
            // QString string = toUtf16(nameArray);
            // emit memoryNameChanged(type, (memoryId), string);
            uint8_t nameAdd0[34] = {0};
            memcpy(nameAdd0, memory.name, 32);
            emit memoryNameChanged(type, (memoryId), QString::fromUtf8(reinterpret_cast<const char*>(nameAdd0), -1));
        // }
    }
}

void Mid03Handler::parse0FData(const QByteArray &data)
{
    // bool ret = false;
    AudioOperation0E0F temp0f;
    memcpy(&temp0f, data.data(), data.size());

    // if(temp0f.mode != mAudioOperation0E0F.mode)
    // {
        emit presetModeChanged(temp0f.mode);
        // ret = true;
    // }
    // if(temp0f.level != mAudioOperation0E0F.level)
    // {
        emit presetLevelChanged(temp0f.level);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mAudioOperation0E0F, data.data(), data.size());
    // }
}

QByteArray Mid03Handler::send01Data(const AudioOperation0102 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(AudioOperation0102)));

    return frameData;
}

QByteArray Mid03Handler::send02Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid03Handler::send03Data(const AudioOperation0304 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(AudioOperation0304)));

    return frameData;
}

QByteArray Mid03Handler::send04Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid03Handler::send05Data(const AudioOperation0506 &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.source, sizeof(data.source));
    frameData.append((char *)&data.startChannel, sizeof(data.startChannel));
    frameData.append((char *)&data.num, sizeof(data.num));

    for(Mix i: data.gains) {
        frameData.append((char *)&i, sizeof(i));
    }

    return frameData;
}

QByteArray Mid03Handler::send06Data(uint8_t source, uint8_t channel, uint8_t num)
{
    QByteArray frameData;
    frameData.append(source);
    frameData.append(channel);
    frameData.append(num);

    return frameData;
}

QByteArray Mid03Handler::send07Data(const AudioOperation0708 &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.channel, sizeof(data.channel));
    frameData.append((char *)&data.startBand, sizeof(data.startBand));
    frameData.append((char *)&data.num, sizeof(data.num));

    for(EqParm i: data.parms) {
        frameData.append((char *)&i, sizeof(i));
    }

    return frameData;
}

QByteArray Mid03Handler::send08Data(uint8_t channel, uint8_t band, uint8_t num)
{
    QByteArray frameData;
    frameData.append(channel);
    frameData.append(band);
    frameData.append(num);

    return frameData;
}

QByteArray Mid03Handler::send09Data(const AudioOperation09 &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.num, sizeof(data.num));

    for(EqLinkParm i: data.links) {
        frameData.append((char *)&i, sizeof(i));
    }

    return frameData;
}

QByteArray Mid03Handler::send0AData(const AudioOperation0A0B &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.startMemory, sizeof(data.startMemory));
    frameData.append((char *)&data.type, sizeof(data.type));
    frameData.append((char *)&data.num, sizeof(data.num));

    for(MemorySetting i: data.memories) {
        frameData.append((char *)&i, sizeof(i));
    }

    return frameData;
}

QByteArray Mid03Handler::send0BData(uint8_t type, uint8_t memoryId, uint8_t num)
{
    QByteArray frameData;
    frameData.append(memoryId);
    frameData.append(type);
    frameData.append(num);

    return frameData;
}

QByteArray Mid03Handler::send0CData(const AudioOperation0C &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(AudioOperation0C)));

    return frameData;
}

QByteArray Mid03Handler::send0DData(const AudioOperation0D &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.num, sizeof(data.num));

    for(DelaySetting i: data.delaySettings) {
        frameData.append((char *)&i, sizeof(i));
    }

    return frameData;
}

QByteArray Mid03Handler::send0EData(const AudioOperation0E0F &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(AudioOperation0E0F)));

    return frameData;
}

QByteArray Mid03Handler::send0FData()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid03Handler::send10Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

const AudioOperation0102& Mid03Handler::getAudioOperation0102()
{
    return mAudioOperation0102;
}

const AudioOperation0304& Mid03Handler::getAudioOperation0304()
{
    return mAudioOperation0304;
}

const QMap<uint8_t, QMap<uint8_t, QVector<Mix>>>& Mid03Handler::getAudioOperation0506Map()
{
    return mMixMap;
}

const QMap<uint8_t, QVector<EqParm>>& Mid03Handler::getAudioOperation0708Map()
{
    return mEqParmMap;
}

const QMap<uint8_t, QVector<MemorySetting>>& Mid03Handler::getAudioOperation0A0B()
{
    return mMemorySettingMap;
}

const AudioOperation0E0F& Mid03Handler::getAudioOperation0E0F()
{
    return mAudioOperation0E0F;
}
