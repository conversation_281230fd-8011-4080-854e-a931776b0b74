/**
 * @file canvas_redraw_test_example.cpp
 * @brief Canvas重绘测试示例
 * 演示某个通道32~36号点更新完之后如何触发canvas重绘
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */

#include "datamodel.h"
#include "canvasview.h"
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QDebug>
#include <QTimer>

/**
 * @brief Canvas重绘测试示例类
 * 提供UI界面来测试预设变化时的canvas重绘功能
 */
class CanvasRedrawTestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CanvasRedrawTestWidget(QWidget *parent = nullptr)
        : QWidget(parent)
        , m_dataModel(new DataModel(this))
        , m_canvasView(new CanvasView(m_dataModel, this))
    {
        setupUI();
        connectSignals();

        qDebug() << "=== Canvas重绘测试示例初始化完成 ===";
    }

private slots:
    /**
     * @brief 应用SuperBass预设
     */
    void applySuperBassPreset()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 应用SuperBass Level5预设到通道" << channel << " ===";

        // 这将触发32~36号点的更新，并自动触发canvas重绘
        m_dataModel->setCurvePreset(channel, DataModel::PRESET_TYPE_SUPERBASS, DataModel::PRESET_LEVEL_5);

        updateStatusLabel("已应用SuperBass Level5预设");
    }

    /**
     * @brief 应用Vocal预设
     */
    void applyVocalPreset()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 应用Vocal Level3预设到通道" << channel << " ===";

        // 这将触发32~36号点的更新，并自动触发canvas重绘
        m_dataModel->setCurvePreset(channel, DataModel::PRESET_TYPE_VOCAL, DataModel::PRESET_LEVEL_3);

        updateStatusLabel("已应用Vocal Level3预设");
    }

    /**
     * @brief 应用Powerful预设
     */
    void applyPowerfulPreset()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 应用Powerful Level4预设到通道" << channel << " ===";

        // 这将触发32~36号点的更新，并自动触发canvas重绘
        m_dataModel->setCurvePreset(channel, DataModel::PRESET_TYPE_POWERFUL, DataModel::PRESET_LEVEL_4);

        updateStatusLabel("已应用Powerful Level4预设");
    }

    /**
     * @brief 清除预设
     */
    void clearPreset()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 清除通道" << channel << "的预设 ===";

        // 这将重置32~36号点为默认值，并自动触发canvas重绘
        m_dataModel->clearCurvePreset(channel);

        updateStatusLabel("已清除预设");
    }

    /**
     * @brief 手动更新32~36号点
     */
    void manuallyUpdate32to36Points()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 手动更新通道" << channel << "的32~36号点 ===";

        // 手动设置32~36号点的参数，每个点都会触发重绘
        // 32号点: 100Hz, 增益3dB
        m_dataModel->setInvisiblePointParams(channel, 32, 100.0, 4.32, 3.0, DataModel::EQ_TYPE_PEAK);

        // 33号点: 315Hz, 增益-2dB
        m_dataModel->setInvisiblePointParams(channel, 33, 315.0, 4.32, -2.0, DataModel::EQ_TYPE_PEAK);

        // 34号点: 1250Hz, 增益0dB
        m_dataModel->setInvisiblePointParams(channel, 34, 1250.0, 4.32, 0.0, DataModel::EQ_TYPE_PEAK);

        // 35号点: 3150Hz, 增益1dB
        m_dataModel->setInvisiblePointParams(channel, 35, 3150.0, 4.32, 1.0, DataModel::EQ_TYPE_PEAK);

        // 36号点: 8000Hz, 增益2dB
        m_dataModel->setInvisiblePointParams(channel, 36, 8000.0, 4.32, 2.0, DataModel::EQ_TYPE_PEAK);

        updateStatusLabel("已手动更新32~36号点");
    }

    /**
     * @brief 批量更新不可见点
     */
    void batchUpdateInvisiblePoints()
    {
        int channel = m_channelCombo->currentIndex();
        qDebug() << "=== 批量更新通道" << channel << "的不可见点 ===";

        // 准备批量数据
        QVector<DataModel::AdjustablePointData> invisiblePointsData;

        // 32号点: 100Hz, 增益5dB
        DataModel::AdjustablePointData point32;
        point32.frequency = 100.0;
        point32.qValue = 4.32;
        point32.gain = 5.0;
        point32.type = DataModel::EQ_TYPE_PEAK;
        point32.position = QPointF(0.0, 5.0); // 会被重新计算
        invisiblePointsData.append(point32);

        // 33号点: 315Hz, 增益-3dB
        DataModel::AdjustablePointData point33;
        point33.frequency = 315.0;
        point33.qValue = 4.32;
        point33.gain = -3.0;
        point33.type = DataModel::EQ_TYPE_PEAK;
        point33.position = QPointF(0.0, -3.0); // 会被重新计算
        invisiblePointsData.append(point33);

        // 34号点: 1250Hz, 增益1dB
        DataModel::AdjustablePointData point34;
        point34.frequency = 1250.0;
        point34.qValue = 4.32;
        point34.gain = 1.0;
        point34.type = DataModel::EQ_TYPE_PEAK;
        point34.position = QPointF(0.0, 1.0); // 会被重新计算
        invisiblePointsData.append(point34);

        // 35号点: 3150Hz, 增益2dB
        DataModel::AdjustablePointData point35;
        point35.frequency = 3150.0;
        point35.qValue = 4.32;
        point35.gain = 2.0;
        point35.type = DataModel::EQ_TYPE_PEAK;
        point35.position = QPointF(0.0, 2.0); // 会被重新计算
        invisiblePointsData.append(point35);

        // 36号点: 8000Hz, 增益4dB
        DataModel::AdjustablePointData point36;
        point36.frequency = 8000.0;
        point36.qValue = 4.32;
        point36.gain = 4.0;
        point36.type = DataModel::EQ_TYPE_PEAK;
        point36.position = QPointF(0.0, 4.0); // 会被重新计算
        invisiblePointsData.append(point36);

        // 批量设置，这将触发一次重绘
        m_dataModel->setInvisiblePointsForCurve(channel, invisiblePointsData);

        updateStatusLabel("已批量更新不可见点");
    }

    /**
     * @brief 通道切换处理
     */
    void onChannelChanged(int channel)
    {
        qDebug() << "=== 切换到通道" << channel << " ===";

        // 设置当前活动通道
        m_dataModel->setSelectedPoint(channel, -1); // 设置活动通道，但不选中任何点

        // 确保该通道可见
        m_dataModel->setCurveVisible(channel, true);

        // 更新状态显示
        bool hasPreset = m_dataModel->isCurvePresetEnabled(channel);
        if (hasPreset) {
            DataModel::PresetType type = m_dataModel->getCurvePresetType(channel);
            DataModel::PresetLevel level = m_dataModel->getCurvePresetLevel(channel);
            QString statusText = QString("通道%1: %2 %3")
                .arg(channel)
                .arg(m_dataModel->presetTypeToString(type))
                .arg(m_dataModel->presetLevelToString(level));
            updateStatusLabel(statusText);
        } else {
            updateStatusLabel(QString("通道%1: 无预设").arg(channel));
        }
    }

private:
    /**
     * @brief 设置UI界面
     */
    void setupUI()
    {
        setWindowTitle("Canvas重绘测试 - 32~36号点更新");
        setFixedSize(1200, 800);

        QVBoxLayout *mainLayout = new QVBoxLayout(this);

        // 控制面板
        QWidget *controlPanel = new QWidget;
        QHBoxLayout *controlLayout = new QHBoxLayout(controlPanel);

        // 通道选择
        controlLayout->addWidget(new QLabel("通道:"));
        m_channelCombo = new QComboBox;
        for (int i = 0; i < 8; ++i) {
            m_channelCombo->addItem(QString("CH%1").arg(i));
        }
        controlLayout->addWidget(m_channelCombo);

        controlLayout->addStretch();

        // 预设按钮
        m_superBassBtn = new QPushButton("SuperBass L5");
        m_vocalBtn = new QPushButton("Vocal L3");
        m_powerfulBtn = new QPushButton("Powerful L4");
        m_clearBtn = new QPushButton("清除预设");

        controlLayout->addWidget(m_superBassBtn);
        controlLayout->addWidget(m_vocalBtn);
        controlLayout->addWidget(m_powerfulBtn);
        controlLayout->addWidget(m_clearBtn);

        controlLayout->addStretch();

        // 手动更新按钮
        m_manualUpdateBtn = new QPushButton("手动更新32~36");
        m_batchUpdateBtn = new QPushButton("批量更新不可见点");

        controlLayout->addWidget(m_manualUpdateBtn);
        controlLayout->addWidget(m_batchUpdateBtn);

        mainLayout->addWidget(controlPanel);

        // Canvas视图
        mainLayout->addWidget(m_canvasView, 1);

        // 状态标签
        m_statusLabel = new QLabel("准备就绪 - 请选择通道和操作");
        m_statusLabel->setStyleSheet("QLabel { background-color: #f0f0f0; padding: 8px; border: 1px solid #ccc; }");
        mainLayout->addWidget(m_statusLabel);
    }

    /**
     * @brief 连接信号槽
     */
    void connectSignals()
    {
        // 按钮信号连接
        connect(m_superBassBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::applySuperBassPreset);
        connect(m_vocalBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::applyVocalPreset);
        connect(m_powerfulBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::applyPowerfulPreset);
        connect(m_clearBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::clearPreset);
        connect(m_manualUpdateBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::manuallyUpdate32to36Points);
        connect(m_batchUpdateBtn, &QPushButton::clicked, this, &CanvasRedrawTestWidget::batchUpdateInvisiblePoints);

        // 通道切换信号连接
        connect(m_channelCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &CanvasRedrawTestWidget::onChannelChanged);

        // 监听DataModel的预设变化信号
        connect(m_dataModel, &DataModel::curvePresetChanged,
                [this](int curveIndex, DataModel::PresetType type, DataModel::PresetLevel level) {
            qDebug() << "检测到预设变化信号: 通道" << curveIndex
                     << "预设变化为" << m_dataModel->presetTypeToString(type)
                     << m_dataModel->presetLevelToString(level);
        });

        connect(m_dataModel, &DataModel::curvePresetCleared,
                [this](int curveIndex) {
            qDebug() << "检测到预设清除信号: 通道" << curveIndex << "预设已清除";
        });

        // 监听DataModel的数据变化信号
        connect(m_dataModel, &DataModel::dataChanged,
                [this]() {
            qDebug() << "检测到数据变化信号: DataModel数据已更新，Canvas将重绘";
        });
    }

    /**
     * @brief 更新状态标签
     */
    void updateStatusLabel(const QString &text)
    {
        m_statusLabel->setText(QString("%1 - %2")
            .arg(QTime::currentTime().toString("hh:mm:ss.zzz"))
            .arg(text));
    }

private:
    DataModel *m_dataModel;        ///< 数据模型
    CanvasView *m_canvasView;      ///< Canvas视图

    // UI控件
    QComboBox *m_channelCombo;     ///< 通道选择组合框
    QPushButton *m_superBassBtn;   ///< SuperBass预设按钮
    QPushButton *m_vocalBtn;       ///< Vocal预设按钮
    QPushButton *m_powerfulBtn;    ///< Powerful预设按钮
    QPushButton *m_clearBtn;       ///< 清除预设按钮
    QPushButton *m_manualUpdateBtn; ///< 手动更新按钮
    QPushButton *m_batchUpdateBtn; ///< 批量更新按钮
    QLabel *m_statusLabel;         ///< 状态标签
};

/**
 * @brief 主函数 - 运行Canvas重绘测试
 */
int runCanvasRedrawTest(int argc, char *argv[])
{
    QApplication app(argc, argv);

    CanvasRedrawTestWidget testWidget;
    testWidget.show();

    qDebug() << "\n=== Canvas重绘测试说明 ===";
    qDebug() << "1. 选择要测试的通道";
    qDebug() << "2. 点击预设按钮应用不同的预设";
    qDebug() << "3. 观察控制台输出，查看重绘触发情况";
    qDebug() << "4. 查看Canvas界面的曲线变化";
    qDebug() << "5. 测试手动更新32~36号点功能";
    qDebug() << "6. 测试批量更新不可见点功能";
    qDebug() << "=====================================\n";

    return app.exec();
}

// 包含MOC文件以支持信号槽
#include "canvas_redraw_test_example.moc"

/**
 * @brief 演示使用示例
 */
void demonstrateCanvasRedrawFeature()
{
    qDebug() << "\n=== Canvas重绘功能演示 ===";

    // 创建数据模型
    DataModel dataModel;

    qDebug() << "\n1. 测试预设应用触发重绘:";
    qDebug() << "应用SuperBass Level5预设到通道0...";
    dataModel.setCurvePreset(0, DataModel::PRESET_TYPE_SUPERBASS, DataModel::PRESET_LEVEL_5);
    qDebug() << "→ 32~36号点已更新，Canvas重绘信号已发送";

    qDebug() << "\n2. 测试预设清除触发重绘:";
    qDebug() << "清除通道0的预设...";
    dataModel.clearCurvePreset(0);
    qDebug() << "→ 32~36号点已重置，Canvas重绘信号已发送";

    qDebug() << "\n3. 测试手动更新单个不可见点:";
    qDebug() << "更新通道0的32号点...";
    dataModel.setInvisiblePointParams(0, 32, 100.0, 4.32, 3.0, DataModel::EQ_TYPE_PEAK);
    qDebug() << "→ 32号点已更新，Canvas重绘信号已发送";

    qDebug() << "\n4. 测试批量更新不可见点:";
    QVector<DataModel::AdjustablePointData> batchData;
    for (int i = 0; i < 5; ++i) {
        DataModel::AdjustablePointData pointData;
        pointData.frequency = 100.0 * (i + 1);
        pointData.qValue = 4.32;
        pointData.gain = i - 2.0; // -2 to +2 dB
        pointData.type = DataModel::EQ_TYPE_PEAK;
        batchData.append(pointData);
    }
    dataModel.setInvisiblePointsForCurve(0, batchData);
    qDebug() << "→ 32~36号点已批量更新，Canvas重绘信号已发送";

    qDebug() << "\n=== 总结 ===";
    qDebug() << "✅ 预设应用会触发32~36号点更新和Canvas重绘";
    qDebug() << "✅ 预设清除会触发32~36号点重置和Canvas重绘";
    qDebug() << "✅ 手动更新不可见点会触发Canvas重绘";
    qDebug() << "✅ 批量更新不可见点会触发Canvas重绘";
    qDebug() << "✅ 所有操作都通过信号槽机制自动触发重绘";
    qDebug() << "====================\n";
}