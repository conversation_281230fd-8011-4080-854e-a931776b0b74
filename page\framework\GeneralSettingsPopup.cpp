#include "GeneralSettingsPopup.h"
#include "ui_GeneralSettingsPopup.h"

GeneralSettingsPopup::GeneralSettingsPopup(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::GeneralSettingsPopup)
{
    ui->setupUi(this);
    setWindowFlags(Qt::Popup);

    mBtnGroup = new QButtonGroup(this);

    mBtnGroup->addButton(ui->languages, LANGUAGES);
    mBtnGroup->addButton(ui->DSPSettings, DSP_SETTINGS);
    mBtnGroup->addButton(ui->sources, SOURCES);
    mBtnGroup->addButton(ui->updatePC, UPDATE_PC);
    mBtnGroup->addButton(ui->updateDSP, UPDATE_DSP);
    mBtnGroup->addButton(ui->updateRemote, UPDATE_REMOTE);
    mBtnGroup->addButton(ui->info, INFO);
    mBtnGroup->addButton(ui->reset, RESET);

    connect(mBtnGroup, &QButtonGroup::idClicked, this, &GeneralSettingsPopup::buttonClicked);
}

GeneralSettingsPopup::~GeneralSettingsPopup()
{
    delete ui;
}

void GeneralSettingsPopup::mousePressEvent(QMouseEvent* e)
{
    // setAttribute(Qt::WA_NoMouseReplay);
    QWidget::mousePressEvent(e);
}
