import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickattachedpropertypropagator.h"
        name: "QQuickAttachedPropertyPropagator"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquickmaterialstyle_p.h"
        name: "QQuickMaterialStyle"
        accessSemantics: "reference"
        prototype: "QQuickAttachedPropertyPropagator"
        exports: [
            "QtQuick.Controls.Material/Material 2.0",
            "QtQuick.Controls.Material/Material 2.15",
            "QtQuick.Controls.Material/Material 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 527, 1536]
        attachedType: "QQuickMaterialStyle"
        Enum {
            name: "Theme"
            values: ["Light", "Dark", "System"]
        }
        Enum {
            name: "Variant"
            values: ["<PERSON>", "<PERSON><PERSON>"]
        }
        Enum {
            name: "Color"
            values: [
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "DeepP<PERSON>ple",
                "Indigo",
                "<PERSON>",
                "LightBlue",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "Green",
                "<PERSON>Green",
                "Lime",
                "Yellow",
                "Amber",
                "Orange",
                "DeepOrange",
                "Brown",
                "Grey",
                "BlueGrey"
            ]
        }
        Enum {
            name: "Shade"
            values: [
                "Shade50",
                "Shade100",
                "Shade200",
                "Shade300",
                "Shade400",
                "Shade500",
                "Shade600",
                "Shade700",
                "Shade800",
                "Shade900",
                "ShadeA100",
                "ShadeA200",
                "ShadeA400",
                "ShadeA700"
            ]
        }
        Enum {
            name: "RoundedScale"
            isScoped: true
            values: [
                "NotRounded",
                "ExtraSmallScale",
                "SmallScale",
                "MediumScale",
                "LargeScale",
                "ExtraLargeScale",
                "FullScale"
            ]
        }
        Enum {
            name: "ContainerStyle"
            isScoped: true
            values: ["Filled", "Outlined"]
        }
        Property {
            name: "theme"
            type: "Theme"
            read: "theme"
            write: "setTheme"
            reset: "resetTheme"
            notify: "themeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "primary"
            type: "QVariant"
            read: "primary"
            write: "setPrimary"
            reset: "resetPrimary"
            notify: "primaryChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "accent"
            type: "QVariant"
            read: "accent"
            write: "setAccent"
            reset: "resetAccent"
            notify: "accentChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "foreground"
            type: "QVariant"
            read: "foreground"
            write: "setForeground"
            reset: "resetForeground"
            notify: "foregroundChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "background"
            type: "QVariant"
            read: "background"
            write: "setBackground"
            reset: "resetBackground"
            notify: "backgroundChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "elevation"
            type: "int"
            read: "elevation"
            write: "setElevation"
            reset: "resetElevation"
            notify: "elevationChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "roundedScale"
            type: "RoundedScale"
            read: "roundedScale"
            write: "setRoundedScale"
            reset: "resetRoundedScale"
            notify: "roundedScaleChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "containerStyle"
            type: "ContainerStyle"
            read: "containerStyle"
            write: "setContainerStyle"
            reset: "resetContainerStyle"
            notify: "containerStyleChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "primaryColor"
            type: "QColor"
            read: "primaryColor"
            notify: "primaryChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "accentColor"
            type: "QColor"
            read: "accentColor"
            notify: "accentChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            notify: "backgroundChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "primaryTextColor"
            type: "QColor"
            read: "primaryTextColor"
            notify: "themeChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "primaryHighlightedTextColor"
            type: "QColor"
            read: "primaryHighlightedTextColor"
            notify: "primaryHighlightedTextColorChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "secondaryTextColor"
            type: "QColor"
            read: "secondaryTextColor"
            notify: "themeChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hintTextColor"
            type: "QColor"
            read: "hintTextColor"
            notify: "themeChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "textSelectionColor"
            type: "QColor"
            read: "textSelectionColor"
            notify: "themeOrAccentChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dropShadowColor"
            type: "QColor"
            read: "dropShadowColor"
            index: 16
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "dividerColor"
            type: "QColor"
            read: "dividerColor"
            notify: "themeChanged"
            index: 17
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "iconColor"
            type: "QColor"
            read: "iconColor"
            notify: "themeChanged"
            index: 18
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "iconDisabledColor"
            type: "QColor"
            read: "iconDisabledColor"
            notify: "themeChanged"
            index: 19
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "frameColor"
            type: "QColor"
            read: "frameColor"
            notify: "themeChanged"
            index: 20
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rippleColor"
            type: "QColor"
            read: "rippleColor"
            notify: "themeChanged"
            index: 21
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "highlightedRippleColor"
            type: "QColor"
            read: "highlightedRippleColor"
            notify: "themeOrAccentChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchUncheckedTrackColor"
            type: "QColor"
            read: "switchUncheckedTrackColor"
            notify: "themeChanged"
            index: 23
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchCheckedTrackColor"
            type: "QColor"
            read: "switchCheckedTrackColor"
            notify: "themeOrAccentChanged"
            index: 24
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchUncheckedHandleColor"
            type: "QColor"
            read: "switchUncheckedHandleColor"
            notify: "themeChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchUncheckedHoveredHandleColor"
            type: "QColor"
            read: "switchUncheckedHoveredHandleColor"
            notify: "themeChanged"
            index: 26
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledUncheckedTrackColor"
            type: "QColor"
            read: "switchDisabledUncheckedTrackColor"
            notify: "themeChanged"
            index: 27
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledCheckedTrackColor"
            type: "QColor"
            read: "switchDisabledCheckedTrackColor"
            notify: "themeChanged"
            index: 28
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledUncheckedTrackBorderColor"
            type: "QColor"
            read: "switchDisabledUncheckedTrackBorderColor"
            notify: "themeChanged"
            index: 29
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchCheckedHandleColor"
            type: "QColor"
            read: "switchCheckedHandleColor"
            notify: "themeOrAccentChanged"
            index: 30
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledUncheckedHandleColor"
            type: "QColor"
            read: "switchDisabledUncheckedHandleColor"
            notify: "themeChanged"
            index: 31
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledCheckedHandleColor"
            type: "QColor"
            read: "switchDisabledCheckedHandleColor"
            notify: "themeChanged"
            index: 32
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledCheckedIconColor"
            type: "QColor"
            read: "switchDisabledCheckedIconColor"
            notify: "themeChanged"
            index: 33
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledUncheckedIconColor"
            type: "QColor"
            read: "switchDisabledUncheckedIconColor"
            notify: "themeChanged"
            index: 34
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarColor"
            type: "QColor"
            read: "scrollBarColor"
            notify: "themeChanged"
            index: 35
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarHoveredColor"
            type: "QColor"
            read: "scrollBarHoveredColor"
            notify: "themeChanged"
            index: 36
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarPressedColor"
            type: "QColor"
            read: "scrollBarPressedColor"
            notify: "themeChanged"
            index: 37
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dialogColor"
            type: "QColor"
            read: "dialogColor"
            notify: "dialogColorChanged"
            index: 38
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "backgroundDimColor"
            type: "QColor"
            read: "backgroundDimColor"
            notify: "themeChanged"
            index: 39
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "listHighlightColor"
            type: "QColor"
            read: "listHighlightColor"
            notify: "themeChanged"
            index: 40
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "tooltipColor"
            type: "QColor"
            read: "tooltipColor"
            notify: "tooltipColorChanged"
            index: 41
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "toolBarColor"
            type: "QColor"
            read: "toolBarColor"
            notify: "toolBarColorChanged"
            index: 42
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "toolTextColor"
            type: "QColor"
            read: "toolTextColor"
            notify: "toolTextColorChanged"
            index: 43
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "spinBoxDisabledIconColor"
            type: "QColor"
            read: "spinBoxDisabledIconColor"
            notify: "themeChanged"
            index: 44
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "sliderDisabledColor"
            revision: 527
            type: "QColor"
            read: "sliderDisabledColor"
            notify: "themeChanged"
            index: 45
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "textFieldFilledContainerColor"
            type: "QColor"
            read: "textFieldFilledContainerColor"
            notify: "themeChanged"
            index: 46
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "touchTarget"
            type: "int"
            read: "touchTarget"
            index: 47
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonVerticalPadding"
            type: "int"
            read: "buttonVerticalPadding"
            index: 48
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buttonHeight"
            type: "int"
            read: "buttonHeight"
            index: 49
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "delegateHeight"
            type: "int"
            read: "delegateHeight"
            index: 50
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "dialogButtonBoxHeight"
            type: "int"
            read: "dialogButtonBoxHeight"
            index: 51
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "dialogTitleFontPixelSize"
            type: "int"
            read: "dialogTitleFontPixelSize"
            index: 52
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "dialogRoundedScale"
            type: "RoundedScale"
            read: "dialogRoundedScale"
            index: 53
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "frameVerticalPadding"
            type: "int"
            read: "frameVerticalPadding"
            index: 54
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "menuItemHeight"
            type: "int"
            read: "menuItemHeight"
            index: 55
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "menuItemVerticalPadding"
            type: "int"
            read: "menuItemVerticalPadding"
            index: 56
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchIndicatorWidth"
            type: "int"
            read: "switchIndicatorWidth"
            index: 57
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchIndicatorHeight"
            type: "int"
            read: "switchIndicatorHeight"
            index: 58
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchNormalHandleHeight"
            type: "int"
            read: "switchNormalHandleHeight"
            index: 59
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchCheckedHandleHeight"
            type: "int"
            read: "switchCheckedHandleHeight"
            index: 60
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchLargestHandleHeight"
            type: "int"
            read: "switchLargestHandleHeight"
            index: 61
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "switchDelegateVerticalPadding"
            type: "int"
            read: "switchDelegateVerticalPadding"
            index: 62
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textFieldHeight"
            type: "int"
            read: "textFieldHeight"
            index: 63
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textFieldHorizontalPadding"
            type: "int"
            read: "textFieldHorizontalPadding"
            index: 64
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "textFieldVerticalPadding"
            type: "int"
            read: "textFieldVerticalPadding"
            index: 65
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "tooltipHeight"
            type: "int"
            read: "tooltipHeight"
            index: 66
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "themeChanged" }
        Signal { name: "primaryChanged" }
        Signal { name: "accentChanged" }
        Signal { name: "foregroundChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "elevationChanged" }
        Signal { name: "themeOrAccentChanged" }
        Signal { name: "primaryHighlightedTextColorChanged" }
        Signal { name: "dialogColorChanged" }
        Signal { name: "tooltipColorChanged" }
        Signal { name: "toolBarColorChanged" }
        Signal { name: "toolTextColorChanged" }
        Signal { name: "roundedScaleChanged" }
        Signal { name: "containerStyleChanged" }
        Method {
            name: "buttonColor"
            type: "QColor"
            isMethodConstant: true
            Parameter { name: "theme"; type: "Theme" }
            Parameter { name: "background"; type: "QVariant" }
            Parameter { name: "accent"; type: "QVariant" }
            Parameter { name: "enabled"; type: "bool" }
            Parameter { name: "flat"; type: "bool" }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "checked"; type: "bool" }
        }
        Method {
            name: "color"
            type: "QColor"
            isMethodConstant: true
            Parameter { name: "color"; type: "Color" }
            Parameter { name: "shade"; type: "Shade" }
        }
        Method {
            name: "color"
            type: "QColor"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "color"; type: "Color" }
        }
        Method {
            name: "shade"
            type: "QColor"
            isMethodConstant: true
            Parameter { name: "color"; type: "QColor" }
            Parameter { name: "shade"; type: "Shade" }
        }
        Method {
            name: "buttonLeftPadding"
            type: "int"
            isMethodConstant: true
            Parameter { name: "flat"; type: "bool" }
            Parameter { name: "hasIcon"; type: "bool" }
        }
        Method {
            name: "buttonRightPadding"
            type: "int"
            isMethodConstant: true
            Parameter { name: "flat"; type: "bool" }
            Parameter { name: "hasIcon"; type: "bool" }
            Parameter { name: "hasText"; type: "bool" }
        }
    }
}
