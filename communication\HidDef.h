#ifndef HIDDEF_H
#define HIDDEF_H

#include <stdint.h>

constexpr uint16_t HID_VID = 0x08E4;
constexpr uint16_t HID_H_PID = 0x020D;
constexpr uint16_t HID_L_PID = 0x020E;

constexpr uint8_t MIN_FRAME_LENGTH = 10;                                        // 最小帧长度（帧长1+帧头2+版本1+操作码1+数据长2+MID1+SID1+校验1）
constexpr uint16_t MAX_FRAME_LENGTH = 64;                                       // 最大单帧长度
constexpr uint16_t MAX_FRAMES_LENGTH = MAX_FRAME_LENGTH*8;                      // 最大总帧长度
constexpr uint16_t MAX_DATA_LENGTH = (MAX_FRAMES_LENGTH-(MIN_FRAME_LENGTH-1)
                                      -(MAX_FRAMES_LENGTH/MAX_FRAME_LENGTH));   // 最大数据长度

#endif // HIDDEF_H
