import QtQuick
import QtQuick.Controls.Basic
import Qt5Compat.GraphicalEffects
import "../framework"

Rectangle {
    id: root
    color: "#4D000000"

    required property string titleStr
    required property int contentWidth
    required property int contentHeight
    property Component contentItem
    property alias exitVisible: exit.visible
    property alias confirmVisible: confirmBtn.visible
    property alias cancelVisible: cancelBtn.visible
    property alias confirmEnabled: confirmBtn.enabled
    property alias confirmStr: confirmBtn.text
    property alias cancelStr: cancelBtn.text
    property alias titleFontFamily: title.font.family

    signal clickClose()
    signal clickConfirm()
    signal clickCancel()

    MouseArea {
        anchors.fill: parent
        onClicked: function(mouse) {
            mouse.accepted = true
        }
        onReleased: function(mouse) {
            mouse.accepted = true
        }
        onPressed: function(mouse) {
            mouse.accepted = true
        }
    }

    Rectangle {
        anchors.centerIn: parent
        width: contentWidth
        height: contentHeight
        color: "#50545C"
        layer.enabled: true
        layer.effect: DropShadow {
            transparentBorder: false
            horizontalOffset: 0
            verticalOffset: 3
            radius: 3
            color: "#80000000"
        }

        Rectangle {
            id: header
            width: parent.width
            height: 30
            color: "#D4D8E0"

            Text {
                id: title
                anchors.left: parent.left
                anchors.leftMargin: 10
                anchors.verticalCenter: parent.verticalCenter
                color: "#303030"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: titleStr
            }

            Button {
                id: exit
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                width: 36
                height: parent.height

                background: Rectangle {
                    color: exit.hovered ? "#BCC0C8" : exit.pressed ? "#A4A8B0" : "transparent"

                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/Image/exit.png"
                    }
                }

                onClicked: clickClose()
            }
        }

        Loader {
            anchors.top: header.bottom
            anchors.topMargin: 8
            anchors.horizontalCenter: parent.horizontalCenter
            width: parent.width - 8 - 8
            height: parent.height - 30 - 8 - 16 - 24 - 16
            sourceComponent: contentItem
        }

        Row {
            anchors.right: parent.right
            anchors.rightMargin: 16
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 16
            spacing: 8

            ButtonA {
                id: confirmBtn
                width: 96
                height: 24
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("OK")

                onClicked: clickConfirm()
            }

            ButtonA {
                id: cancelBtn
                width: 96
                height: 24
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Cancel")

                onClicked: clickCancel()
            }
        }
    }
}
