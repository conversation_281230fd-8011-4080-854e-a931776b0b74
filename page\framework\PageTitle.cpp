#include "PageTitle.h"
#include "ui_PageTitle.h"
#include <QMenu>
#include "../CommonController.h"

PageTitle::PageTitle(QWidget *parent)
    : QFrame(parent)
    , ui(new Ui::PageTitle)
    // , mDeviceConnection(DISCONNECTION)
{
    ui->setupUi(this);

    QLocale::Language language = QLocale::system().language();
    QString languageStr = (QLocale::Japanese == language) ? "Meiryo UI" : "Segoe UI";
    QString labelQss = QString("QLabel{ font: 14px '%1'; color: #C8D8FF; padding: 12px; }").arg(languageStr);
    QString btnQss = QString("QPushButton{ background-color: #00242830; border: 0px; font: 12px '%1'; color: #E8E8E8; margin: 10px; }"
                             "QPushButton::menu-indicator{ subcontrol-position: center right; image: url(:/Image/down.png); }"
                             "QPushButton::menu-indicator:open{ image: url(:/Image/up.png); }").arg(languageStr);
    ui->deviceType->setStyleSheet(labelQss);
    ui->generalSettings->setStyleSheet(btnQss);
    ui->memorySettings->setStyleSheet(btnQss);
    ui->mixSettings->setStyleSheet(btnQss);
    ui->delaySettings->setStyleSheet(btnQss);

    QString menuQss = QString("QMenu { background-color: #1C2028; color: #747880; border: 1px, solid, #747880; }"
                              "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                              "QMenu::item { background-color: #1C2028; color: #E8E8E8; height: 24px; padding-left: 10px; padding-right: 24px; font: 12px '%1'; }"
                              "QMenu::separator { height: 1px; background-color: #7C8088; margin-left: 10px; margin-right: 10px; }"
                              "QMenu::item:selected { background-color: #4C5058; }"
                              "QMenu::item:disabled { color: #4DE8E8E8; }"
                              "QMenu::indicator { width: 24px; height: 24px; subcontrol-origin: padding; subcontrol-position: center right; }"
                              "QMenu::indicator:checked { image: url(:/Image/icn_menulistcheck.png); }"
                              "QMenu::indicator:unchecked { image: none; }").arg(languageStr);
    setStyleSheet(menuQss);

    mMenuActions.clear();

    QMenu* generalSettingsMenu = new QMenu(this);
    mLanguageMenu = generalSettingsMenu->addMenu(tr("Language"));
    QString languageMenuQss = QString("QMenu::item { background-color: #1C2028; color: #E8E8E8; width: 120px; height: 24px; padding-left: -14px; padding-right: 24px; font: 12px '%1'; }"
                                      "QMenu::item:selected { background-color: #4C5058; }").arg(languageStr);
    mLanguageMenu->setStyleSheet(languageMenuQss);
    mLangGroup = new QActionGroup(this);
    mLangGroup->setExclusive(true);
    mMenuActions.insert(JAPANESE, mLanguageMenu->addAction(tr("Japanese"), [this](){emit btnClicked(JAPANESE);}));
    mMenuActions[JAPANESE]->setCheckable(true);
    mMenuActions[JAPANESE]->setChecked((QLocale::Japanese == language) ? true : false);
    mMenuActions[JAPANESE]->setActionGroup(mLangGroup);
    mMenuActions.insert(ENGLISH, mLanguageMenu->addAction(tr("English"), [this](){emit btnClicked(ENGLISH);}));
    mMenuActions[ENGLISH]->setCheckable(true);
    mMenuActions[ENGLISH]->setChecked((QLocale::Japanese == language) ? false : true);
    mMenuActions[ENGLISH]->setActionGroup(mLangGroup);
    generalSettingsMenu->addSeparator();
    mMenuActions.insert(DSP_SETTINGS, generalSettingsMenu->addAction(tr("Device Setting"), [this](){emit btnClicked(DSP_SETTINGS);}));
    mMenuActions.insert(SOURCES, generalSettingsMenu->addAction(tr("Source Level Adjuster"), [this](){emit btnClicked(SOURCES);}));
    generalSettingsMenu->addSeparator();
    // mMenuActions.insert(UPDATE_PC, generalSettingsMenu->addAction(tr("PC Tool Update"), [this](){emit btnClicked(UPDATE_PC);}));
    mMenuActions.insert(UPDATE_DSP, generalSettingsMenu->addAction(tr("Device Firmware Update"), [this](){emit btnClicked(UPDATE_DSP);}));
    // mMenuActions.insert(UPDATE_REMOTE, generalSettingsMenu->addAction(tr("Remote Firmware Update"), [this](){emit btnClicked(UPDATE_REMOTE);}));
    generalSettingsMenu->addSeparator();
    mMenuActions.insert(INFO, generalSettingsMenu->addAction(tr("Information"), [this](){emit btnClicked(INFO);}));
    mMenuActions.insert(RESET, generalSettingsMenu->addAction(tr("Initialization"), [this](){emit btnClicked(RESET);}));
    ui->generalSettings->setMenu(generalSettingsMenu);

    QMenu* memorySettingsMenu = new QMenu(this);
    mMenuActions.insert(MEMORY, memorySettingsMenu->addAction(tr("Memory copy"), [this](){emit btnClicked(MEMORY);}));
    memorySettingsMenu->addSeparator();
    mMenuActions.insert(SETTINGS_SAVE, memorySettingsMenu->addAction(tr("Save as Device Setting"), [this](){emit btnClicked(SETTINGS_SAVE);}));
    mMenuActions.insert(SETTINGS_LOAD, memorySettingsMenu->addAction(tr("Open Device Setting"), [this](){emit btnClicked(SETTINGS_LOAD);}));
    ui->memorySettings->setMenu(memorySettingsMenu);

    setMenuEnabled(PageTitle::UPDATE_DSP, false);

    mBtnGroup = new QButtonGroup(this);
    mBtnGroup->setExclusive(false);
    mBtnGroup->addButton(ui->min, MIN);
    mBtnGroup->addButton(ui->max, MAX);
    mBtnGroup->addButton(ui->close, CLOSE);
    // mBtnGroup->addButton(ui->generalSettings, GENERAL);
    // mBtnGroup->addButton(ui->memorySettings, MEMORY);
    mBtnGroup->addButton(ui->mixSettings, MIX);
    mBtnGroup->addButton(ui->delaySettings, DELAY);

    connect(mBtnGroup, &QButtonGroup::idClicked, this, &PageTitle::btnClicked);
}

PageTitle::~PageTitle()
{
    delete ui;
}

void PageTitle::setDeviceType(QString type, int iconIndex)
{
    ui->deviceType->setText(type);

    QString qss = QString("QLabel { font-size: 14px; color: #C8D8FF; padding: 12px; }");

    switch (iconIndex) {
    case 0: //disconnet
        ui->deviceImg->setStyleSheet("image: url(:/Image/icn_dsp_access.png);");
        qss = QString("QLabel { font-size: 14px; color: #B91440; padding: 12px; }");
        break;
    case 1: //connect
        ui->deviceImg->setStyleSheet("image: url(:/Image/dspConnected.png);");
        break;
    case 2: //demo
        ui->deviceImg->setStyleSheet("");
        break;
    default:
        break;
    }
    ui->deviceType->setStyleSheet(qss);
}

// PageTitle::ENDeviceConnection PageTitle::deviceConnection()
// {
//     return mDeviceConnection;
// }

// void PageTitle::setDeviceConnection(ENDeviceConnection type)
// {
//     if(mDeviceConnection != type)
//     {
//         mDeviceConnection = type;

//         switch (type) {
//         case DEQ2000A:
//             ui->deviceType->setText("DEQ-2000A");
//             break;
//         case DEQ7000A:
//             ui->deviceType->setText("DEQ-7000A");
//             break;
//         default:
//             ui->deviceType->setText("Demo mode");
//             break;
//         }
//     }
// }

void PageTitle::changeEvent(QEvent * event)
{
    if (NULL != event) {
        switch (event->type())
        {
        case QEvent::LanguageChange:
        {
            ui->retranslateUi(this);
            if(0 == CommonController::getInstance().mUiDataMap["deviceLevel"].toUInt())
            {
                setDeviceType(tr("Demo mode"), 2);
            }
            else
            {
                int hidConnection = CommonController::getInstance().mUiDataMap["hidConnection"].toInt();
                if(1 == hidConnection)
                {
                    setDeviceType(CommonController::getInstance().mDataMap["deviceType"].toString());
                }
                else
                {
                    setDeviceType("Disconnect", 0);
                }
            }

            if(1 == CommonController::getInstance().mUiDataMap["language"].toUInt())
            {
                QString labelQss = "QLabel{ font: 14px 'Meiryo UI'; color: #C8D8FF; padding: 12px; }";
                QString btnQss = "QPushButton{ background-color: #00242830; border: 0px; font: 12px 'Meiryo UI'; color: #E8E8E8; margin: 10px; }"
                                 "QPushButton::menu-indicator{ subcontrol-position: center right; image: url(:/Image/down.png); }"
                                 "QPushButton::menu-indicator:open{ image: url(:/Image/up.png); }";

                ui->deviceType->setStyleSheet(labelQss);
                ui->generalSettings->setStyleSheet(btnQss);
                ui->memorySettings->setStyleSheet(btnQss);
                ui->mixSettings->setStyleSheet(btnQss);
                ui->delaySettings->setStyleSheet(btnQss);

                QString menuQss = "QMenu { background-color: #1C2028; color: #747880; border: 1px, solid, #747880; }"
                                  "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                                  "QMenu::item { background-color: #1C2028; color: #E8E8E8; height: 24px; padding-left: 10px; padding-right: 24px; font: 12px 'Meiryo UI'; }"
                                  "QMenu::separator { height: 1px; background-color: #7C8088; margin-left: 10px; margin-right: 10px; }"
                                  "QMenu::item:selected { background-color: #4C5058; }"
                                  "QMenu::item:disabled { color: #4DE8E8E8; }"
                                  "QMenu::indicator { width: 24px; height: 24px; subcontrol-origin: padding; subcontrol-position: center right; }"
                                  "QMenu::indicator:checked { image: url(:/Image/icn_menulistcheck.png); }"
                                  "QMenu::indicator:unchecked { image: none; }";
                setStyleSheet(menuQss);
                QString languageMenuQss = "QMenu::item { background-color: #1C2028; color: #E8E8E8; width: 120px; height: 24px; padding-left: -14px; padding-right: 24px; font: 12px 'Meiryo UI'; }"
                                          "QMenu::item:selected { background-color: #4C5058; }";
                mLanguageMenu->setStyleSheet(languageMenuQss);
            }
            else
            {
                QString labelQss = "QLabel{ font: 14px 'Segoe UI'; color: #C8D8FF; padding: 12px; }";
                QString btnQss = "QPushButton{ background-color: #00242830; border: 0px; font: 12px 'Segoe UI'; color: #E8E8E8; margin: 10px; }"
                                 "QPushButton::menu-indicator{ subcontrol-position: center right; image: url(:/Image/down.png); }"
                                 "QPushButton::menu-indicator:open{ image: url(:/Image/up.png); }";

                ui->deviceType->setStyleSheet(labelQss);
                ui->generalSettings->setStyleSheet(btnQss);
                ui->memorySettings->setStyleSheet(btnQss);
                ui->mixSettings->setStyleSheet(btnQss);
                ui->delaySettings->setStyleSheet(btnQss);

                QString menuQss = "QMenu { background-color: #1C2028; color: #747880; border: 1px, solid, #747880; }"
                                  "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                                  "QMenu::item { background-color: #1C2028; color: #E8E8E8; height: 24px; padding-left: 10px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                                  "QMenu::separator { height: 1px; background-color: #7C8088; margin-left: 10px; margin-right: 10px; }"
                                  "QMenu::item:selected { background-color: #4C5058; }"
                                  "QMenu::item:disabled { color: #4DE8E8E8; }"
                                  "QMenu::indicator { width: 24px; height: 24px; subcontrol-origin: padding; subcontrol-position: center right; }"
                                  "QMenu::indicator:checked { image: url(:/Image/icn_menulistcheck.png); }"
                                  "QMenu::indicator:unchecked { image: none; }";
                setStyleSheet(menuQss);
                QString languageMenuQss = "QMenu::item { background-color: #1C2028; color: #E8E8E8; width: 120px; height: 24px; padding-left: -14px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                                          "QMenu::item:selected { background-color: #4C5058; }";
                mLanguageMenu->setStyleSheet(languageMenuQss);
            }

            mLanguageMenu->setTitle(tr("Language"));
            mMenuActions[JAPANESE]->setText(tr("Japanese"));
            mMenuActions[ENGLISH]->setText(tr("English"));
            mMenuActions[DSP_SETTINGS]->setText(tr("Device Setting"));
            mMenuActions[SOURCES]->setText(tr("Source Level Adjuster"));
            // mMenuActions[UPDATE_PC]->setText(tr("PC Tool Update"));
            mMenuActions[UPDATE_DSP]->setText(tr("Device Firmware Update"));
            // mMenuActions[UPDATE_REMOTE]->setText(tr("Remote Firmware Update"));
            mMenuActions[INFO]->setText(tr("Information"));
            mMenuActions[RESET]->setText(tr("Initialization"));
            mMenuActions[MEMORY]->setText(tr("Memory copy"));
            mMenuActions[SETTINGS_SAVE]->setText(tr("Save as Device Setting"));
            mMenuActions[SETTINGS_LOAD]->setText(tr("Open Device Setting"));
            break;
        }
        default:
            break;
        }
    }
    QFrame::changeEvent(event);
}

void PageTitle::setMenuVisible(ENButton action, bool isVisible)
{
    mMenuActions[action]->setVisible(isVisible);
}

void PageTitle::setMenuEnabled(ENButton action, bool isEnabled)
{
    // mMenuActions[action]->setEnabled(isEnabled);
}

bool PageTitle::isMaxChecked()
{
    return ui->max->isChecked();
}

void PageTitle::setMaxChecked(bool isChecked)
{
    ui->max->setChecked(isChecked);
}
