#ifndef PAGETOOLBAR_H
#define PAGETOOLBAR_H

#include <QFrame>
#include <QMenu>

namespace Ui {
class PageToolBar;
}

class PageToolBar : public QFrame
{
    Q_OBJECT

public:
    explicit PageToolBar(QWidget *parent = nullptr);
    ~PageToolBar();

    enum ENMusicSource
    {
        MUSICSOURCE_DEFAULT = -1,
        MUSICSOURCE_MAIN_UNIT,
        MUSICSOURCE_USB_AUDIO,
        MUSICSOURCE_BT_AUDIO,
        MUSICSOURCE_SPDIF,
        MUSICSOURCE_AUX
    };

    enum ENMemory
    {
        MEMORY_DEFAULT = -1,
        MEMORY_M1,
        MEMORY_M2,
        MEMORY_M3,
        MEMORY_M4,
        MEMORY_M5,
        MEMORY_M6,
        MEMORY_S1,
        MEMORY_S2,
        MEMORY_S3,
        MEMORY_S4,
        MEMORY_S5,
        MEMORY_S6
    };

public slots:

public:
    void setMusicSourceList(int deviceLevel);
    void setMusicSourceEnabled(ENMusicSource source, bool isEnabled);
    void setMusicSource(ENMusicSource source);
    void setMemoryName(uint8_t type, uint8_t memoryId, QString name);
    void setMemoryEnabled(uint8_t type, uint8_t memoryId, bool enable);
    void setMemory(ENMemory memory);
    void setDspMute(bool isMute);
    void setDspVolumn(int value);
    void setBassMute(bool isMute);
    void setBassLevel(int value);
    void setBtAudioStatus(int status);
    bool eventFilter(QObject *target, QEvent *event);

signals:
    void musicSourceChanged(ENMusicSource source);
    void memoryChanged(ENMemory memory);
    void dspMuteChanged(bool isMute);
    void dspVolumnChanged(int value);
    void bassMuteChanged(bool isMute);
    void bassLevelChanged(int value);
    void btAudioStatusChanged(int status);

private:
    Ui::PageToolBar *ui;
    QMap<ENMusicSource, QAction*> mSourceActions;
    QMap<ENMemory, QAction*> mMemoryActions;

    ENMusicSource mMusicSource;
    ENMemory mMemory;
    bool mDspMute;
    int mDSPVolumn;
    bool mBassMute;
    int mBassLevel;
    int mBtAudioStatus; //0:off; 1:on; 2:connecting
    int mDeviceLevel;
};

#endif // PAGETOOLBAR_H
