#include "aesapi.h"
#include <stdio.h>

// #include <cstdio>
#include "aes.h"
// unsigned char *aes_key[] = {
//     "LSSA&&ADF@LC1204", //panel key
//     "LSSA&QIYIN240902", //PC/app   key
// };
#define aes_key "LSSA&QIYIN240902"

#include <QDebug>



AesApi::AesApi(QObject *parent)
    : QObject{parent}
{}


void AesApi::lsk_uuid(char uuid[], int len)
{
    int i, val;
    int count = len / 2;
    unsigned char hex[16] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    srand((unsigned int)time(0)); // 修改种子
    for (i = 0; i < count; i++)
    {
        val = rand();
        val = val % 256;
        uuid[2 * i] = hex[(val >> 4) & 0x0F];
        uuid[2 * i + 1] = hex[val & 0x0F];
    }
}


/*******************************************************************/
/******************************master*******************************/


int AesApi::master_auth1(unsigned char output_str[32])
{
    unsigned char enc_data[16] = {0};
    if (output_str == NULL)
    {
        return -1;
    }

    lsk_uuid(reinterpret_cast<char *>(master_random_1), 16);

    aesEnc(master_random_1, 16, enc_data, (unsigned char *)aes_key);
    for (size_t i = 0; i < 16; i++)
    {
        sprintf(reinterpret_cast<char *>(output_str + 2 * i), "%02x", enc_data[i]);
    }
    return 0;
}
// 函数返回值不为0则认证失败
int AesApi::master_auth2(unsigned char input_str[64], unsigned char output_str[64])
{
    size_t i = 0;
    unsigned char enc_data[16] = {0};
    unsigned char master_random_tmp[16] = {0};
    unsigned char blended_data[32] = {0};
    unsigned char enc_blended_data[32] = {0};

    if ((input_str == NULL) || (output_str == NULL))
    {
        return -1;
    }

    for (i = 0; i < 32; i++)
    {
        sscanf(reinterpret_cast<const char *>(input_str + 2 * i), "%02hhx", &blended_data[i]);
    }

    for (i = 0; i < 16; ++i)
    {
        master_random_tmp[i] = blended_data[2 * i];
        enc_data[i] = blended_data[2 * i + 1];
    }

    if (memcmp(master_random_tmp, master_random_1, 16))
    {
        return -1;
    }

    aesDec((unsigned char *)enc_data, 16, (unsigned char *)slave_random_1, (unsigned char *)aes_key);
    // 将主设备和从设备的随机数明文交织
    for (i = 0; i < 16; i++)
    {
        blended_data[i * 2] = master_random_1[i];
        blended_data[i * 2 + 1] = slave_random_1[i];
    }
    aesEnc((unsigned char *)blended_data, 32, (unsigned char *)enc_blended_data, (unsigned char *)aes_key);

    for (i = 0; i < 32; i++)
    {
        sprintf((char *)output_str + 2 * i, "%02x", enc_blended_data[i]);
    }
    return 0;
}
/*********************************************************************/

#if 1
int AesApi::aesInit()
{

    if (master_auth1(moutput_str1))
    {
        printf("master_auth1 err!\n");
        // qDebug() << "master_auth1 err!\n";
        return 0;
    }
    printf("AT+USERAUTH1=%s\n", moutput_str1);
    // qDebug() << "AT+USERAUTH1=" << moutput_str1;
    scanf("%s", moutput_str2);
    printf("output_str2 %s\n", moutput_str2);
    // qDebug() << "output_str2=" << moutput_str2;
    if (master_auth2(moutput_str2, moutput_str3))
    {
        printf("master_auth2 err!\n");
        // qDebug() << "master_auth2 err!\n";
        return 0;
    }
    printf("AT+USERAUTH2=%s\n", moutput_str3);
    // qDebug() << "AT+USERAUTH2=" << moutput_str3;
    printf("Successful authentication!\n");
    // qDebug() << "Successful authentication!\n";
    return 0;
}
void AesApi::getAesInfo()
{
    printf("moutput_str1=%s\n", moutput_str1);
    printf("moutput_str2=%s\n", moutput_str2);
    printf("moutput_str3=%s\n", moutput_str3);

    // qDebug() << "moutput_str1=" << moutput_str1;
    // qDebug() << "moutput_str2=" << moutput_str2;
    // qDebug() << "moutput_str3=" << moutput_str3;
}
void AesApi::setOutput_str1(unsigned char output_str1[32])
{
    memcpy(moutput_str1, output_str1, 32 * sizeof(unsigned char));
}

void AesApi::setOutput_str2(unsigned char output_str1[64])
{
    memcpy(moutput_str2, output_str1, 64 * sizeof(unsigned char));
}
void AesApi::setOutput_str3(unsigned char output_str1[64])
{
    memcpy(moutput_str3, output_str1, 64 * sizeof(unsigned char));
}


unsigned char * AesApi::getMaster_random_1()
{
    printf("master_random_1=%s\n", master_random_1);
    return master_random_1;
}

unsigned char * AesApi::getSlave_random_1()
{
    printf("slave_random_1=%s\n", slave_random_1);
    return slave_random_1;
}

uint8_t AesApi::crc8(const uint8_t *data, size_t length) {
    // 示例数据
    // std::vector<uint8_t> data = {0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39};
    // 调用方式crc8(data.data(), data.size());
    const uint8_t polynomial = 0x07; // CRC-8-ATM标准多项式

    for (size_t i = 0; i < length; ++i) {
        crc ^= data[i]; // 将当前字节与CRC寄存器异或

        for (uint8_t j = 0; j < 8; ++j) {
            if (crc & 0x80) { // 如果最高位为1
                crc = (crc << 1) ^ polynomial; // 左移并异或多项式
            } else {
                crc <<= 1; // 仅左移
            }
        }

        // 清除超出8位的部分（实际上由于crc是uint8_t类型，这一步是多余的，但为了清晰性保留）
        crc &= 0xFF;
    }

    return crc;
}
#endif
