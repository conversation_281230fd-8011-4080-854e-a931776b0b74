#ifndef LINKWITZ_RILEY_FILTER_H
#define LINKWITZ_RILEY_FILTER_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 计算宁克-锐（<PERSON><PERSON>-<PERSON>）滤波器在各频率点的复数响应
 * @param type 滤波器类型（7=低通，8=高通）
 * @param fc 截止频率
 * @param slope_rate 斜率（dB/oct），支持6/12/24/48等
 * @param freq_list 频率点数组
 * @param resp 输出复数响应（累乘）
 *
 * Linkwitz-Riley滤波器为Butterworth二阶滤波器级联，偶数阶（如4阶、8阶），
 * 截止点-6dB，幅频/相位特性与专业分频器一致。
 */
void linkwitz_riley_optimized_response(int type, double fc, int slope_rate,
                                      const DoubleArray* freq_list,
                                      double_complex* resp);

/**
 * @brief 计算宁克-锐（<PERSON><PERSON>-<PERSON>）低通滤波器系数
 * @param fs 采样率
 * @param fc 截止频率
 * @param coeff 输出系数结构体
 */
void linkwitz_riley_lowpass_coeff(float fs, float fc, BiquadCoeffs* coeff);

/**
 * @brief 计算宁克-锐（Linkwitz-Riley）高通滤波器系数
 * @param fs 采样率
 * @param fc 截止频率
 * @param coeff 输出系数结构体
 */
void linkwitz_riley_highpass_coeff(float fs, float fc, BiquadCoeffs* coeff);

#ifdef __cplusplus
}
#endif

#endif // LINKWITZ_RILEY_FILTER_H