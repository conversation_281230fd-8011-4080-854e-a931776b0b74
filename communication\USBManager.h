#ifndef USBMANAGER_H
#define USBMANAGER_H

#include <QObject>
#include <QThread>

#include "HidSender.h"
#include "HidDef.h"
#include "LibUsb/include/hidapi.h"

class USBManager : public QObject
{
    Q_OBJECT

private:
    explicit USBManager(QObject *parent = nullptr);
    ~USBManager();

public:
    static USBManager& getInstance()
    {
        static USBManager instance;
        return instance;
    }

    // virtual int connectDevice(int vid = 0x1234, int pid = 0x1000);
    // virtual int connectDevice(QString hidPath);
    virtual int connectDevice();
    virtual void disconnectDevice();

    virtual int sendMessage(const QByteArray &data);
    virtual int receiveMessage(const QByteArray &data);

signals:
    void sendMessageSig(const QByteArray &data);
    void receiveMessageSig(const QByteArray &data);
    void receivedErrorSig(int error);

private:
    QThread* mHidSenderThread;
    HidSender* mHidSender;
    hid_device* mHidHandle;
};

#endif // USBMANAGER_H
