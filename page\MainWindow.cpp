#include "MainWindow.h"
#include "ui_MainWindow.h"

#include <QScreen>
#include <QQmlContext>
#include <QScrollArea>
#include <QSpacerItem>
#include <QHBoxLayout>
#include <QPixMap>
#include <QLineEdit>
#include <QScrollBar>
#include <QQuickItem>
#include <QTimer>

#include <QDebug>

#include "CommonController.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , mIsMouseBtnLeftPressed(false)
{
    ui->setupUi(this);

    this->setWindowFlags(Qt::Window|Qt::FramelessWindowHint|Qt::WindowMinimizeButtonHint);

    const QRect &screenGeometry = QApplication::primaryScreen()->geometry();
    qInfo("primery screen: width %d, height %d", screenGeometry.width(), screenGeometry.height());

    if(screenGeometry.size() == QSize(1366, 768))
    {
        setFixedSize(1280, 700);
    }
    setGeometry((QApplication::primaryScreen()->availableGeometry().width() - geometry().width()) / 2,
                (QApplication::primaryScreen()->availableGeometry().height() - geometry().height()) / 2,
                geometry().width(), geometry().height());
    mNormalRect.setRect(this->x(), this->y(), geometry().width(), geometry().height());

    mTranslator = new QTranslator();
    QLocale::Language language = QLocale::system().language();
    QString translateFile = (QLocale::Japanese == language) ?
                                ":/jp_JP.qm" : ":/en_US.qm";
    if(mTranslator->load(translateFile))
    {
        qApp->installTranslator(mTranslator);
    }

    installEventFilter(this);

    CommonController& commonCtrl = CommonController::getInstance();

    mInitWindow = new QQuickWidget();
    mInitWindow->setWindowFlags(Qt::Window|Qt::FramelessWindowHint|Qt::WindowMinimizeButtonHint);
    mInitWindow->setResizeMode(QQuickWidget::SizeRootObjectToView);
    // mInitWindow->setGeometry((screenGeometry.width()/2 - 200), (screenGeometry.height()/2 - 270), 400, 540);
    mInitWindow->setGeometry(QApplication::primaryScreen()->availableGeometry());
    mInitWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mInitWindow->setClearColor(Qt::transparent);
    mInitWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mInitWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    // mInitWindow->showMaximized();
    mInitWindow->setSource(QUrl("qrc:/qml/page/framework/InitWindow.qml"));
    mInitWindow->show();
    connect(&commonCtrl, &CommonController::showMainWindow, [&](){
        mInitWindow->hide();
        show();
    });
    connect(&commonCtrl, &CommonController::quitApp, [&](){
        QCoreApplication::quit();
    });

    ui->mainLayout->setAttribute(Qt::WA_AlwaysStackOnTop);
    ui->mainLayout->setClearColor(Qt::transparent);
    ui->mainLayout->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    ui->mainLayout->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    ui->mainLayout->setSource(QUrl("qrc:/qml/page/framework/MainLayout.qml"));

    mEqLinkWindow = new QQuickWidget(this);
    mEqLinkWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mEqLinkWindow->setClearColor(Qt::transparent);
    mEqLinkWindow->rootContext()->setContextProperty("windowWidth", width());
    mEqLinkWindow->rootContext()->setContextProperty("windowHeight", height());
    mEqLinkWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mEqLinkWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mEqLinkWindow->setSource(QUrl("qrc:/qml/page/subwindow/EqLinkPopup.qml"));
    mEqLinkWindow->hide();
    connect((QObject*)mEqLinkWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseEqLinkPopup()));

    mDeviceSettingWindow = new QQuickWidget(this);
    mDeviceSettingWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mDeviceSettingWindow->setClearColor(Qt::transparent);
    mDeviceSettingWindow->rootContext()->setContextProperty("windowWidth", width());
    mDeviceSettingWindow->rootContext()->setContextProperty("windowHeight", height());
    mDeviceSettingWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mDeviceSettingWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mDeviceSettingWindow->setSource(QUrl("qrc:/qml/page/subwindow/DeviceSetting.qml"));
    mDeviceSettingWindow->hide();
    connect((QObject*)mDeviceSettingWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseDeviceSetting()));

    mSourceLevelWindow = new QQuickWidget(this);
    mSourceLevelWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mSourceLevelWindow->setClearColor(Qt::transparent);
    mSourceLevelWindow->rootContext()->setContextProperty("windowWidth", width());
    mSourceLevelWindow->rootContext()->setContextProperty("windowHeight", height());
    mSourceLevelWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mSourceLevelWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mSourceLevelWindow->setSource(QUrl("qrc:/qml/page/subwindow/SourceLevel.qml"));
    mSourceLevelWindow->hide();
    connect((QObject*)mSourceLevelWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseSourceLevel()));

    mInfoWindow = new QQuickWidget(this);
    mInfoWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mInfoWindow->setClearColor(Qt::transparent);
    mInfoWindow->rootContext()->setContextProperty("windowWidth", width());
    mInfoWindow->rootContext()->setContextProperty("windowHeight", height());
    mInfoWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mInfoWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mInfoWindow->setSource(QUrl("qrc:/qml/page/subwindow/Info.qml"));
    mInfoWindow->hide();
    connect((QObject*)mInfoWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseInfo()));

    mMemoryCopyWindow = new QQuickWidget(this);
    mMemoryCopyWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mMemoryCopyWindow->setClearColor(Qt::transparent);
    mMemoryCopyWindow->rootContext()->setContextProperty("windowWidth", width());
    mMemoryCopyWindow->rootContext()->setContextProperty("windowHeight", height());
    mMemoryCopyWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mMemoryCopyWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mMemoryCopyWindow->setSource(QUrl("qrc:/qml/page/subwindow/MemoryCopy.qml"));
    mMemoryCopyWindow->hide();
    connect((QObject*)mMemoryCopyWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseMemoryCopy()));

    mMixerWindow = new QQuickWidget(this);
    mMixerWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mMixerWindow->setClearColor(Qt::transparent);
    mMixerWindow->rootContext()->setContextProperty("windowWidth", width());
    mMixerWindow->rootContext()->setContextProperty("windowHeight", height());
    mMixerWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mMixerWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mMixerWindow->setSource(QUrl("qrc:/qml/page/subwindow/MixerSetting.qml"));
    mMixerWindow->hide();
    connect((QObject*)mMixerWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseMixer()));

    mDelayWindow = new QQuickWidget(this);
    mDelayWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mDelayWindow->setClearColor(Qt::transparent);
    mDelayWindow->rootContext()->setContextProperty("windowWidth", width());
    mDelayWindow->rootContext()->setContextProperty("windowHeight", height());
    mDelayWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mDelayWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mDelayWindow->setSource(QUrl("qrc:/qml/page/subwindow/DelaySetting.qml"));
    mDelayWindow->hide();
    connect((QObject*)mDelayWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCloseDelay()));

    mWarningWindow = new QQuickWidget(this);
    mWarningWindow->setAttribute(Qt::WA_AlwaysStackOnTop);
    mWarningWindow->setClearColor(Qt::transparent);
    mWarningWindow->rootContext()->setContextProperty("windowWidth", width());
    mWarningWindow->rootContext()->setContextProperty("windowHeight", height());
    mWarningWindow->rootContext()->setContextProperty("commonCtrl", &commonCtrl);
    mWarningWindow->rootContext()->setContextProperty("dataMap", &commonCtrl.mDataMap);
    mWarningWindow->setSource(QUrl("qrc:/qml/page/subwindow/Warning.qml"));
    mWarningWindow->hide();
    connect((QObject*)mWarningWindow->rootObject(), SIGNAL(clickClose()), this, SLOT(sltCancelWarning()));
    connect((QObject*)mWarningWindow->rootObject(), SIGNAL(clickConfirm()), this, SLOT(sltConfirmWarning()));

    connect(ui->titleBar, &PageTitle::btnClicked, this, &MainWindow::sltTitleClicked);

    uint8_t deviceLevel = commonCtrl.mUiDataMap["deviceLevel"].toUInt();

    ui->titleBar->setDeviceType(commonCtrl.mDataMap["deviceType"].toString(), (0 == deviceLevel) ? 2 : 1);
    // ui->titleBar->setMenuVisible(PageTitle::UPDATE_REMOTE, (1 != deviceLevel));

    ui->toolBar->setMusicSourceList(deviceLevel);
    PageToolBar::ENMusicSource musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_DEFAULT;
    switch (commonCtrl.mDataMap["currentSource"].toUInt())
    {
    case 0:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_DEFAULT;
        break;
    case 1:
    case 2:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_MAIN_UNIT;
        break;
    case 3:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_AUX;
        break;
    case 4:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_BT_AUDIO;
        break;
    case 5:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_SPDIF;
        break;
    case 6:
        musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_USB_AUDIO;
        break;
    default:
        break;
    }
    ui->toolBar->setMusicSource(musicSource);
    ui->toolBar->setMemory(static_cast<PageToolBar::ENMemory>(commonCtrl.mDataMap["currentMemory"].toUInt()));
    ui->toolBar->setDspMute(commonCtrl.mDataMap["dspMute"].toBool());
    ui->toolBar->setDspVolumn(commonCtrl.mDataMap["dspGain"].toUInt());
    ui->toolBar->setBassMute(commonCtrl.mDataMap["bassMute"].toBool());
    ui->toolBar->setBassLevel(commonCtrl.mDataMap["bassLevel"].toUInt());

    connect(ui->toolBar, &PageToolBar::musicSourceChanged, [](PageToolBar::ENMusicSource source) {
        MusicSourceEnum musicSource = MusicSourceEnum::CLOSE;
        switch (source)
        {
        case PageToolBar::ENMusicSource::MUSICSOURCE_DEFAULT:
            musicSource = MusicSourceEnum::CLOSE;
            break;
        case PageToolBar::ENMusicSource::MUSICSOURCE_MAIN_UNIT:
            musicSource = (0x01 == CommonController::getInstance().mDataMap["mainUnitEnabledType"].toUInt()) ?
                MusicSourceEnum::HIGH : MusicSourceEnum::RCA;
            break;
        case PageToolBar::ENMusicSource::MUSICSOURCE_AUX:
            musicSource = MusicSourceEnum::AUX;
            break;
        case PageToolBar::ENMusicSource::MUSICSOURCE_BT_AUDIO:
            musicSource = MusicSourceEnum::BT;
            break;
        case PageToolBar::ENMusicSource::MUSICSOURCE_SPDIF:
            musicSource = MusicSourceEnum::SPDIF;
            break;
        case PageToolBar::ENMusicSource::MUSICSOURCE_USB_AUDIO:
            musicSource = MusicSourceEnum::USB_AUDIO;
            break;
        default:
            break;
        }

        if(static_cast<uint8_t>(musicSource) != CommonController::getInstance().mDataMap["currentSource"].toUInt())
        {
            CommonController::getInstance().setSource(static_cast<uint8_t>(musicSource));
        }
    });
    connect(ui->toolBar, &PageToolBar::memoryChanged, [](PageToolBar::ENMemory memory) {
        if(static_cast<uint8_t>(memory) != CommonController::getInstance().mDataMap["currentMemory"].toUInt())
        {
            CommonController::getInstance().setMemory(static_cast<uint8_t>(memory));
        }
    });
    connect(ui->toolBar, &PageToolBar::dspMuteChanged, [](bool mute) {
        if(mute != CommonController::getInstance().mDataMap["dspMute"].toBool())
        {
            CommonController::getInstance().setDspMute(mute ? 0x01 : 0x00);
        }
    });
    connect(ui->toolBar, &PageToolBar::dspVolumnChanged, [](int value) {
        if(value != CommonController::getInstance().mDataMap["dspGain"].toInt())
        {
            CommonController::getInstance().setDspGain((uint8_t)value);
        }
    });
    connect(ui->toolBar, &PageToolBar::bassMuteChanged, [](bool mute) {
        if(mute != CommonController::getInstance().mDataMap["bassMute"].toBool())
        {
            CommonController::getInstance().setBassMute(mute ? 0x01 : 0x00);
        }
    });
    connect(ui->toolBar, &PageToolBar::bassLevelChanged, [](int value) {
        if(value != CommonController::getInstance().mDataMap["bassLevel"].toInt())
        {
            CommonController::getInstance().setBassLevel((uint8_t)value);
        }
    });
    connect(ui->toolBar, &PageToolBar::btAudioStatusChanged, []() {
        CommonController::getInstance().setDisconnectBt();
    });

    connect(&commonCtrl, &CommonController::eqLinkPopupShown, [&](){
        mEqLinkWindow->rootContext()->setContextProperty("windowWidth", width());
        mEqLinkWindow->rootContext()->setContextProperty("windowHeight", height());
        mEqLinkWindow->show();
    });

    connect(&commonCtrl, &CommonController::channelListClicked, [=](int channel, bool isChecked){
        CommonController::getInstance().mLineDisplayState.insert(channel, isChecked);

        QMap<int, bool> lineMap = CommonController::getInstance().mLineDisplayState;
        lineMap.insert(CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt(), true);
        m_model->updateLineDisplayStatus(CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt(), lineMap);
    });

    for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
    {
        CommonController::getInstance().mLineDisplayState.insert(channel, false);
    }

    // 获取DataModel单例并初始化
    m_model = DataModel::getInstance();
    m_model->updateRedPointLateralChangeState(true);
    m_model->setHighpassPointVisible(false);
    m_model->setLowpassPointVisible(false);
    m_model->setCurveHighpassFc(0, 80);
    m_model->setCurveLowpassFc(0, 40000);
    // int channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
    // m_model->setCurvePreset(channel, static_cast<DataModel::PresetType>(CommonController::getInstance().mDataMap["presetMode"].toUInt()),
    //                         static_cast<DataModel::PresetLevel>(CommonController::getInstance().mDataMap["presetLevel"].toUInt() + 1));

    initDataModel();

    connect(m_model, &DataModel::adjustablePointChanged, [](int curveIndex, int pointIndex, double frequency, double qValue, double gain, DataModel::EQType type) {
        if(pointIndex < EQ_BAND_MAX)
        {
            int currentChannel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            if(currentChannel == curveIndex)
            {
                const double eps = 1;
                uint16_t currentFreq = CommonController::getInstance().mEqDataMaps[curveIndex][pointIndex]["freq"].toUInt();
                if(qAbs(frequency - static_cast<double>(currentFreq)) >= eps)
                {
                    uint16_t sendFreq = static_cast<uint16_t>(frequency);
                    CommonController::getInstance().setEqFreq(curveIndex, pointIndex, sendFreq);
                }

                uint16_t currentQValue = CommonController::getInstance().mEqDataMaps[curveIndex][pointIndex]["qValue"].toUInt();
                if(qAbs(qValue * 1000 - static_cast<double>(currentQValue)) >= eps)
                {
                    uint16_t sendQValue = static_cast<uint16_t>(qValue * 1000);
                    CommonController::getInstance().setEqQvalue(curveIndex, pointIndex, sendQValue);
                }

                uint16_t currentGain = CommonController::getInstance().mEqDataMaps[curveIndex][pointIndex]["gain"].toUInt();
                if(qAbs((gain * 10 + 600) - static_cast<double>(currentGain)) >= eps)
                {
                    uint16_t sendGain = static_cast<uint16_t>(gain * 10 + 600);
                    CommonController::getInstance().setEqGain(curveIndex, pointIndex, sendGain);
                }

                uint8_t sendType = static_cast<uint8_t>(type);
                uint8_t currentType = CommonController::getInstance().mEqDataMaps[curveIndex][pointIndex]["type"].toUInt();
                if(sendType != currentType)
                {
                    CommonController::getInstance().setEqType(curveIndex, pointIndex, sendType);
                }
            }
        }
    });
    connect(m_model, &DataModel::selectedPointChanged, [](int curveIndex, int pointIndex) {
        if(pointIndex < EQ_BAND_MAX)
        {
            int currentChannel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            if(currentChannel == curveIndex)
            {
                CommonController::getInstance().mUiDataMap.insert("selectedBand", pointIndex);
            }
        }
    });

    // connect(&commonCtrl, &CommonController::eqLinkChannelChanged, [&](int channel, int linkChannel) {
    //     mChannelItems[channel]->setLinkChannel(linkChannel);
    // });

    connect(&commonCtrl, &CommonController::openWarningPopup, [&](int warningNum) {
        CommonController::getInstance().mUiDataMap.insert("warningNum", warningNum);

        mWarningWindow->rootContext()->setContextProperty("windowWidth", width());
        mWarningWindow->rootContext()->setContextProperty("windowHeight", height());
        mWarningWindow->show();
    });

    connect(&commonCtrl, &CommonController::uiDataMapValueChanged, [=](const QString &key, const QVariant &value) {
        if("deviceLevel" == key)
        {
            // ui->titleBar->setMenuVisible(PageTitle::UPDATE_REMOTE, (1 != value.toUInt()));

            ui->toolBar->setMusicSourceList(value.toInt());
        }
        else if("selectedChannel" == key)
        {
            int channel = value.toUInt();

            QMap<int, bool> lineMap = CommonController::getInstance().mLineDisplayState;
            lineMap.insert(channel, true);
            m_model->updateLineDisplayStatus(channel, lineMap);
            m_model->updateRedPointLateralChangeState(0 == CommonController::getInstance().mChannelDataMaps[channel]["eqType"].toUInt());
            m_model->setHighpassPointVisible(0 != CommonController::getInstance().mChannelDataMaps[channel]["hpEnable"].toUInt());
            m_model->setLowpassPointVisible(0 != CommonController::getInstance().mChannelDataMaps[channel]["lpEnable"].toUInt());
            m_model->setAdjustablePointsVerticalChangeState(0 == CommonController::getInstance().mUiDataMap[QString("channel%1AllBypassState").arg(channel)].toUInt());

            double hpFcValue = static_cast<double>(CommonController::getInstance().mChannelDataMaps[channel]["hpFreq"].toUInt());
            int hpSlopeValue = (0 == CommonController::getInstance().mChannelDataMaps[channel]["hpEnable"].toUInt()) ?
                                   0 : (CommonController::getInstance().mChannelDataMaps[channel]["hpSlope"].toUInt() * 6);
            FilterTypeUniversal hpTypeValue = FILTER_TYPE_BUTTERWORTH_HP;
            switch (CommonController::getInstance().mChannelDataMaps[channel]["hpType"].toUInt())
            {
            case 0:
                hpTypeValue = FILTER_TYPE_BUTTERWORTH_HP;
                break;
            case 1:
                hpTypeValue = FILTER_TYPE_BESSEL_HP;
                break;
            case 2:
                hpTypeValue = FILTER_TYPE_LINKWITZ_RILEY_HP;
                break;
            default:
                break;
            }
            m_model->setCurveHighpassFc(channel, hpFcValue);
            m_model->setCurveHighpassSlope(channel, hpSlopeValue);
            m_model->setCurveHighpassType(channel, hpTypeValue);

            double lpFcValue = static_cast<double>(CommonController::getInstance().mChannelDataMaps[channel]["lpFreq"].toUInt());
            int lpSlopeValue = (0 == CommonController::getInstance().mChannelDataMaps[channel]["lpEnable"].toUInt()) ?
                                   0 : (CommonController::getInstance().mChannelDataMaps[channel]["lpSlope"].toUInt() * 6);
            FilterTypeUniversal lpTypeValue = FILTER_TYPE_BUTTERWORTH_LP;
            switch (CommonController::getInstance().mChannelDataMaps[channel]["lpType"].toUInt())
            {
            case 0:
                lpTypeValue = FILTER_TYPE_BUTTERWORTH_LP;
                break;
            case 1:
                lpTypeValue = FILTER_TYPE_BESSEL_LP;
                break;
            case 2:
                lpTypeValue = FILTER_TYPE_LINKWITZ_RILEY_LP;
                break;
            default:
                break;
            }
            m_model->setCurveLowpassFc(channel, lpFcValue);
            m_model->setCurveLowpassSlope(channel, lpSlopeValue);
            m_model->setCurveLowpassType(channel, lpTypeValue);

            for(int band = 0; band < EQ_BAND_MAX; band++)
            {
                m_model->setAdjustablePointParams(channel, band,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["freq"].toInt())),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["qValue"].toInt()) / 1000),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["gain"].toInt() - 600) / 10),
                                                  (static_cast<DataModel::EQType>(CommonController::getInstance().mEqDataMaps[channel][band]["type"].toInt())));
            }
        }
        else if("selectedBand" == key)
        {
            int channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            int band = value.toUInt();

            int curveSelectPoint = m_model->getSelectedPointIndex();
            if(curveSelectPoint != band)
            {
                m_model->setSelectedPoint(channel, band);
            }
        }
        else if("hidConnection" == key)
        {
            if(0 == CommonController::getInstance().mUiDataMap["deviceLevel"].toUInt())
            {
                ui->titleBar->setDeviceType(tr("Demo mode"), 2);
            }
            else
            {
                int hidConnection = value.toInt();
                if(1 == hidConnection)
                {
                    ui->titleBar->setDeviceType(CommonController::getInstance().mDataMap["deviceType"].toString());
                }
                else
                {
                    ui->titleBar->setDeviceType("Disconnect", 0);
                }
            }
        }
        else if("newDspVersion" == key)
        {
            uint8_t dspMainVersion = CommonController::getInstance().mDataMap["dspMainVersion"].toUInt();
            uint8_t dspSubVersion = CommonController::getInstance().mDataMap["dspSubVersion"].toUInt();
            QString newDspVersion = value.toString();
            if(((QString::number(dspMainVersion) + "." + QString::number(dspSubVersion)) != newDspVersion)
                && ("" != newDspVersion))
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, true);
            }
            else
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, false);
            }
        }
        else
        {

        }

        for (uint8_t channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++) {
            if(QString("channel%1AllBypassState").arg(channel) == key)
            {
                if(CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt() == channel)
                {
                    m_model->setAdjustablePointsVerticalChangeState(0 == value.toUInt());
                }
            }
        }
    });

    connect(&commonCtrl, &CommonController::dataMapValueChanged, [&](const QString &key, const QVariant &value) {
        if("deviceType" == key)
        {
            ui->titleBar->setDeviceType(value.toString());
        }
        else if("currentSource" == key)
        {
            PageToolBar::ENMusicSource musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_DEFAULT;
            switch (static_cast<MusicSourceEnum>(value.toUInt()))
            {
            case MusicSourceEnum::CLOSE:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_DEFAULT;
                break;
            case MusicSourceEnum::HIGH:
            case MusicSourceEnum::RCA:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_MAIN_UNIT;
                break;
            case MusicSourceEnum::AUX:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_AUX;
                break;
            case MusicSourceEnum::BT:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_BT_AUDIO;
                break;
            case MusicSourceEnum::SPDIF:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_SPDIF;
                break;
            case MusicSourceEnum::USB_AUDIO:
                musicSource = PageToolBar::ENMusicSource::MUSICSOURCE_USB_AUDIO;
                break;
            default:
                break;
            }
            ui->toolBar->setMusicSource(musicSource);
        }
        else if("currentMemory" == key)
        {
            ui->toolBar->setMemory(static_cast<PageToolBar::ENMemory>(value.toUInt()));
        }
        else if("dspMute" == key)
        {
            ui->toolBar->setDspMute(value.toBool());
        }
        else if("dspGain" == key)
        {
            ui->toolBar->setDspVolumn(value.toUInt());
        }
        else if("bassMute" == key)
        {
            ui->toolBar->setBassMute(value.toBool());
        }
        else if("bassLevel" == key)
        {
            ui->toolBar->setBassLevel(value.toUInt());
        }
        else if("btConnected" == key)
        {
            uint8_t btStatus = value.toUInt();
            ui->toolBar->setBtAudioStatus((2 > btStatus) ? 0 : ((2 == btStatus) ? 2 : 1));
        }
        else if("mainUnitEnabledType" == key)
        {
            uint currentSource = CommonController::getInstance().mDataMap["currentSource"].toUInt();
            if(2 == value.toUInt())
            {
                if(MusicSourceEnum::HIGH == static_cast<MusicSourceEnum>(currentSource))
                {
                    CommonController::getInstance().setSource(static_cast<uint8_t>(MusicSourceEnum::RCA));
                }

                ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_AUX, false);
            }
            else if(1 == value.toUInt())
            {
                if((MusicSourceEnum::RCA == static_cast<MusicSourceEnum>(currentSource)) ||
                    (MusicSourceEnum::AUX == static_cast<MusicSourceEnum>(currentSource)))
                {
                    CommonController::getInstance().setSource(static_cast<uint8_t>(MusicSourceEnum::HIGH));
                }

                uint auxEnabled = CommonController::getInstance().mDataMap["auxEnabled"].toUInt();
                ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_AUX, (0 == auxEnabled) ? false : true);
            }
        }
        else if("auxEnabled" == key)
        {
            uint mainUnitType = CommonController::getInstance().mDataMap["mainUnitEnabledType"].toUInt();
            ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_AUX,
                                               ((0 == value.toUInt()) || (1 != mainUnitType)) ? false : true);
        }
        else if("btEnabled" == key)
        {
            ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_BT_AUDIO, (0 == value.toUInt()) ? false : true);
        }
        else if("spdifEnabled" == key)
        {
            ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_SPDIF, (0 == value.toUInt()) ? false : true);
        }
        else if("usbEnabled" == key)
        {
            ui->toolBar->setMusicSourceEnabled(PageToolBar::ENMusicSource::MUSICSOURCE_USB_AUDIO, (0 == value.toUInt()) ? false : true);
        }
        else if("presetMode" == key)
        {
            // int channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            // m_model->setCurvePreset(channel, static_cast<DataModel::PresetType>(value.toUInt()),
            //                         static_cast<DataModel::PresetLevel>(CommonController::getInstance().mDataMap["presetLevel"].toUInt() + 1));
        }
        else if("presetLevel" == key)
        {
            // int channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            // m_model->setCurvePreset(channel, static_cast<DataModel::PresetType>(CommonController::getInstance().mDataMap["presetMode"].toUInt()),
            //                         static_cast<DataModel::PresetLevel>(value.toUInt() + 1));
        }
        else if("dspMainVersion" == key)
        {
            uint8_t dspMainVersion = value.toUInt();
            uint8_t dspSubVersion = CommonController::getInstance().mDataMap["dspSubVersion"].toUInt();
            QString newDspVersion = CommonController::getInstance().mUiDataMap["newDspVersion"].toString();
            if(((QString::number(dspMainVersion) + "." + QString::number(dspSubVersion)) != newDspVersion)
                && ("" != newDspVersion))
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, true);
            }
            else
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, false);
            }
        }
        else if("dspSubVersion" == key)
        {
            uint8_t dspMainVersion = CommonController::getInstance().mDataMap["dspMainVersion"].toUInt();
            uint8_t dspSubVersion = value.toUInt();
            QString newDspVersion = CommonController::getInstance().mUiDataMap["newDspVersion"].toString();
            if(((QString::number(dspMainVersion) + "." + QString::number(dspSubVersion)) != newDspVersion)
                && ("" != newDspVersion))
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, true);
            }
            else
            {
                ui->titleBar->setMenuEnabled(PageTitle::UPDATE_DSP, false);
            }
        }
        else
        {

        }
    });

    connect(&commonCtrl, &CommonController::channelDataMapsValueChanged, [&](int channel, const QString &key, const QVariant &value) {
        if("eqType" == key)
        {
            m_model->updateRedPointLateralChangeState(0 == value.toUInt());
        }
        else if("hpFreq" == key)
        {
            const double eps = 0.001;
            double convertValue = static_cast<double>(value.toUInt());
            double curveValue = m_model->getCurveHighpassFc(channel);
            if(qAbs(convertValue - curveValue) > eps)
            {
                m_model->setCurveHighpassFc(channel, convertValue);
            }
        }
        else if("hpSlope" == key)
        {
            int convertValue = (0 == CommonController::getInstance().mChannelDataMaps[channel]["hpEnable"].toUInt()) ?
                                   0 : (value.toUInt() * 6);
            int curveValue = m_model->getCurveHighpassSlope(channel);
            if(convertValue != curveValue)
            {
                m_model->setCurveHighpassSlope(channel, convertValue);
            }
        }
        else if("hpType" == key)
        {
            FilterTypeUniversal convertValue = FILTER_TYPE_BUTTERWORTH_HP;
            FilterTypeUniversal curveValue = m_model->getCurveHighpassType(channel);
            switch (value.toUInt())
            {
            case 0:
                convertValue = FILTER_TYPE_BUTTERWORTH_HP;
                break;
            case 1:
                convertValue = FILTER_TYPE_BESSEL_HP;
                break;
            case 2:
                convertValue = FILTER_TYPE_LINKWITZ_RILEY_HP;
                break;
            default:
                break;
            }

            if(convertValue != curveValue)
            {
                m_model->setCurveHighpassType(channel, convertValue);
            }
        }
        else if("hpEnable" == key)
        {
            int selectedChannel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            if(selectedChannel == channel)
            {
                m_model->setHighpassPointVisible(0 != value.toUInt());
            }

            int convertValue = (0 == value.toUInt()) ? 0 :
                                   (CommonController::getInstance().mChannelDataMaps[channel]["hpSlope"].toUInt() * 6);
            int curveValue = m_model->getCurveHighpassSlope(channel);
            if(convertValue != curveValue)
            {
                m_model->setCurveHighpassSlope(channel, convertValue);
            }
        }
        else if("lpFreq" == key)
        {
            const double eps = 0.001;
            double convertValue = static_cast<double>(value.toUInt());
            double curveValue = m_model->getCurveLowpassFc(channel);
            if(qAbs(convertValue - curveValue) > eps)
            {
                m_model->setCurveLowpassFc(channel, convertValue);
            }
        }
        else if("lpSlope" == key)
        {
            int convertValue = (0 == CommonController::getInstance().mChannelDataMaps[channel]["lpEnable"].toUInt()) ?
                                   0 : (value.toUInt() * 6);
            int curveValue = m_model->getCurveLowpassSlope(channel);
            if(convertValue != curveValue)
            {
                m_model->setCurveLowpassSlope(channel, convertValue);
            }
        }
        else if("lpType" == key)
        {
            FilterTypeUniversal convertValue = FILTER_TYPE_BUTTERWORTH_LP;
            FilterTypeUniversal curveValue = m_model->getCurveLowpassType(channel);
            switch (value.toUInt())
            {
            case 0:
                convertValue = FILTER_TYPE_BUTTERWORTH_LP;
                break;
            case 1:
                convertValue = FILTER_TYPE_BESSEL_LP;
                break;
            case 2:
                convertValue = FILTER_TYPE_LINKWITZ_RILEY_LP;
                break;
            default:
                break;
            }

            if(convertValue != curveValue)
            {
                m_model->setCurveLowpassType(channel, convertValue);
            }
        }
        else if("lpEnable" == key)
        {
            int selectedChannel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
            if(selectedChannel == channel)
            {
                m_model->setLowpassPointVisible(0 != value.toUInt());
            }

            int convertValue = (0 == value.toUInt()) ? 0 :
                                   (CommonController::getInstance().mChannelDataMaps[channel]["lpSlope"].toUInt() * 6);
            int curveValue = m_model->getCurveLowpassSlope(channel);
            if(convertValue != curveValue)
            {
                m_model->setCurveLowpassSlope(channel, convertValue);
            }
        }
        else
        {

        }
    });

    connect(&commonCtrl, &CommonController::eqDataMapsValueChanged, [&](int channel, int band, const QString &key, const QVariant &value) {
        if("freq" == key)
        {
            const double eps = 1;
            double convertValue = static_cast<double>(value.toInt());
            double curveValue = m_model->getPointFrequency(channel, band);
            if(qAbs(convertValue - curveValue) >= eps)
            {
                m_model->setAdjustablePointParams(channel, band,
                                                  convertValue,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["qValue"].toInt()) / 1000),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["gain"].toInt() - 600) / 10),
                                                  (static_cast<DataModel::EQType>(CommonController::getInstance().mEqDataMaps[channel][band]["type"].toInt())));
            }
        }
        else if("qValue" == key)
        {
            const double eps = 1;
            double convertValue = static_cast<double>(value.toInt()) / 1000;
            double curveValue = m_model->getPointQValue(channel, band);
            if(qAbs(value.toInt() - (curveValue * 1000)) >= eps)
            {
                m_model->setAdjustablePointParams(channel, band,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["freq"].toInt())),
                                                  convertValue,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["gain"].toInt() - 600) / 10),
                                                  (static_cast<DataModel::EQType>(CommonController::getInstance().mEqDataMaps[channel][band]["type"].toInt())));
            }
        }
        else if("gain" == key)
        {
            const double eps = 1;
            double convertValue = static_cast<double>(value.toInt() - 600) / 10;
            double curveValue = m_model->getPointGain(channel, band);
            if(qAbs((value.toInt() - 600) - (curveValue * 10)) >= eps)
            {
                m_model->setAdjustablePointParams(channel, band,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["freq"].toInt())),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["qValue"].toInt()) / 1000),
                                                  convertValue,
                                                  (static_cast<DataModel::EQType>(CommonController::getInstance().mEqDataMaps[channel][band]["type"].toInt())));
            }
        }
        else if("type" == key)
        {
            DataModel::EQType convertValue = static_cast<DataModel::EQType>(value.toInt());
            DataModel::EQType curveValue = m_model->getPointType(channel, band);
            if(convertValue != curveValue)
            {
                m_model->setAdjustablePointParams(channel, band,
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["freq"].toInt())),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["qValue"].toInt()) / 1000),
                                                  (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["gain"].toInt() - 600) / 10),
                                                  convertValue);
            }
        }
    });

    connect(&commonCtrl, &CommonController::memorySettingDataMapsValueChanged, [&](uint8_t type, uint8_t memoryId, const QString &key, const QVariant &value) {
        if("enable" == key)
        {
            ui->toolBar->setMemoryEnabled(type, memoryId, (0 == value.toUInt()) ? false : true);
        }
        else if("name" == key)
        {
            ui->toolBar->setMemoryName(type, memoryId, value.toString());
        }
    });

    // connect(&commonCtrl, &CommonController::speakerChanged, [&]() {
    //     CommonController& commonCtrl = CommonController::getInstance();
    //     QMap<SpeakerEnum, bool> spMap = commonCtrl.getSpeakerMap();

    //     for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
    //     {
    //         for(SpeakerEnum sp: spMap.keys())
    //         {
    //             if(SpeakerEnum::SP_DEFAULT != sp)
    //             {
                    // mChannelItems[channel]->setSpeakerEnabled(sp, spMap.value(sp));
    //             }
    //         }
    //     }
    // });
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    mIsMouseBtnLeftPressed = false;

    QWidget *childWidget = childAt(event->pos());
    if (childWidget != ui->titleBar) {
        return;
    }

    if (event->button() == Qt::LeftButton) {
        mMousePos = event->globalPosition();
        mIsMouseBtnLeftPressed = true;
    }
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        mIsMouseBtnLeftPressed = false;
    }
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    QWidget *childWidget = childAt(event->pos());
    if (childWidget != ui->titleBar) {
        return;
    }

    if (!mIsMouseBtnLeftPressed) {
        return;
    }

    // 判断是否是全屏
    if(Qt::WindowMaximized == windowState()) {
        return;
    }

    QPointF cursorPoint = event->globalPosition();
    int diffX = cursorPoint.x() - mMousePos.x();
    int diffY = cursorPoint.y() - mMousePos.y();
    this->move(this->x() + diffX, this->y() + diffY);

    mMousePos = cursorPoint;
    mNormalRect.setRect(this->x(), this->y(), mNormalRect.width(), mNormalRect.height());
}

void MainWindow::mouseDoubleClickEvent(QMouseEvent *event)
{
    QWidget *childWidget = childAt(event->pos());
    if (childWidget != ui->titleBar) {
        return;
    }

    if (event->button() == Qt::LeftButton) {
        ui->titleBar->setMaxChecked(!ui->titleBar->isMaxChecked());
        btnMaxClicked();
    }
}

void MainWindow::sltCloseEqLinkPopup()
{
    mEqLinkWindow->hide();
}

void MainWindow::sltCloseDeviceSetting()
{
    mDeviceSettingWindow->hide();
}

void MainWindow::sltCloseSourceLevel()
{
    mSourceLevelWindow->hide();
}

void MainWindow::sltCloseInfo()
{
    mInfoWindow->hide();
}

void MainWindow::sltCloseMemoryCopy()
{
    mMemoryCopyWindow->hide();
}

void MainWindow::sltCloseMixer()
{
    mMixerWindow->hide();
}

void MainWindow::sltCloseDelay()
{
    mDelayWindow->hide();
}

void MainWindow::sltCancelWarning()
{
    mWarningWindow->hide();

    int warningNum = CommonController::getInstance().mUiDataMap["warningNum"].toInt();
    switch (warningNum) {
    case 16:
    {
        qApp->quit();
        QProcess::startDetached(qApp->applicationFilePath(), QStringList());
        break;
    }
    default:
        break;
    }
}

void MainWindow::sltConfirmWarning()
{
    mWarningWindow->hide();

    int warningNum = CommonController::getInstance().mUiDataMap["warningNum"].toInt();
    switch (warningNum) {
    case 2:
    {
        uint8_t channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
        CommonController::getInstance().setOutputChannelEqType(channel, 1);
        break;
    }
    case 3:
    {
        uint8_t channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
        CommonController::getInstance().setOutputChannelEqSet(channel, 0);
        break;
    }
    case 4:
    {
        uint8_t channel = CommonController::getInstance().mUiDataMap["selectedChannel"].toUInt();
        CommonController::getInstance().setOutputChannelEqSet(channel, 0);
        break;
    }
    case 5:
    {
        QString fileName = QFileDialog::getOpenFileName(this, "", "", "Text files (*.bin)"/*, nullptr, QFileDialog::DontUseNativeDialog*/);
        if(!fileName.isEmpty())
        {
            QFile file(fileName);
            if(!file.open(QIODevice::ReadOnly))
            {
                QMessageBox::critical(this, "Error", QString("Failed to open file \"%1\" for save!").arg(fileName), QMessageBox::Ok);
            }
            mDspUpgradeFileStr = file.readAll();
            // CommonController::getInstance().upgradeDsp(mDspUpgradeFileStr);
            CommonController::getInstance().openWarningPopup(14);
        }
        break;
    }
    case 6:
    {
        QString fileName = QFileDialog::getOpenFileName(this, "", "", "Text files (*.bin)"/*, nullptr, QFileDialog::DontUseNativeDialog*/);
        if(!fileName.isEmpty())
        {
            QFile file(fileName);
            if(!file.open(QIODevice::ReadOnly))
            {
                QMessageBox::critical(this, "Error", QString("Failed to open file \"%1\" for save!").arg(fileName), QMessageBox::Ok);
            }
            QByteArray fileStr = file.readAll();
            CommonController::getInstance().upgradeRemote(fileStr);
        }
        break;
    }
    case 8:
    {
        CommonController::getInstance().setResetFactory();
        break;
    }
    case 10:
    {
        uint8_t deviceLevel = CommonController::getInstance().mUiDataMap["deviceLevel"].toUInt();
        QString saveName = QDateTime::currentDateTime().toString("-yyMMdd-hhmmss.bin");
        saveName = QString("/home/") + ((1 == deviceLevel) ? QString("2000A") : QString("7000A")) + saveName;
        QString fileName = QFileDialog::getSaveFileName(this, "", saveName, "Text files (*.bin)"/*, nullptr, QFileDialog::DontUseNativeDialog*/);
        // QString fileName = QFileDialog::getSaveFileName(this, "", "", "Text files (*.bin)");
        if(!fileName.isEmpty())
        {
            CommonController::getInstance().setSaveFile(fileName);
            CommonController::getInstance().querySaveData();
        }
        break;
    }
    case 11:
    {
        QString fileName = QFileDialog::getOpenFileName(this, "", "", "Text files (*.bin)"/*, nullptr, QFileDialog::DontUseNativeDialog*/);
        if(!fileName.isEmpty())
        {
            QFile file(fileName);
            if(!file.open(QIODevice::ReadOnly))
            {
                QMessageBox::critical(this, "Error", QString("Failed to open file \"%1\" for save!").arg(fileName), QMessageBox::Ok);
            }
            QByteArray fileStr = file.readAll();
            CommonController::getInstance().sendLoadData(fileStr);
        }
        break;
    }
    case 14:
    {
        CommonController::getInstance().upgradeDsp(mDspUpgradeFileStr);
        CommonController::getInstance().openWarningPopup(15);
        break;
    }
    case 16:
    {
        qApp->quit();
        QProcess::startDetached(qApp->applicationFilePath(), QStringList());
        break;
    }
    case 17:
    {

    }
    default:
        break;
    }
}

void MainWindow::sltTitleClicked(int btn)
{
    switch (btn) {
    case PageTitle::MIN:
        showMinimized();
        break;
    case PageTitle::MAX:
        btnMaxClicked();
        break;
    case PageTitle::CLOSE:
        QApplication::quit();
        break;
    // case PageTitle::GENERAL:
    //     generalSettingsPopup->show();
    //     break;
    // case PageTitle::MEMORY:
    case PageTitle::JAPANESE:
    {
        if(mTranslator->load(":/jp_JP.qm"))
        {
            CommonController::getInstance().mUiDataMap.insert("language", 1);
            qApp->installTranslator(mTranslator);
        }
        break;
    }
    case PageTitle::ENGLISH:
    {
        if(mTranslator->load(":/en_US.qm"))
        {
            CommonController::getInstance().mUiDataMap.insert("language", 0);
            qApp->installTranslator(mTranslator);
        }
        break;
    }
    case PageTitle::DSP_SETTINGS:
    {
        mDeviceSettingWindow->rootContext()->setContextProperty("windowWidth", width());
        mDeviceSettingWindow->rootContext()->setContextProperty("windowHeight", height());
        mDeviceSettingWindow->show();
        break;
    }
    case PageTitle::SOURCES:
    {
        mSourceLevelWindow->rootContext()->setContextProperty("windowWidth", width());
        mSourceLevelWindow->rootContext()->setContextProperty("windowHeight", height());
        mSourceLevelWindow->show();
        break;
    }
    // case PageTitle::UPDATE_PC:
    //     qInfo("PC工具升级");
    //     break;
    case PageTitle::UPDATE_DSP:
        CommonController::getInstance().openWarningPopup(5);
        break;
    // case PageTitle::UPDATE_REMOTE:
    //     CommonController::getInstance().openWarningPopup(6);
    //     break;
    case PageTitle::INFO:
    {
        mInfoWindow->rootContext()->setContextProperty("windowWidth", width());
        mInfoWindow->rootContext()->setContextProperty("windowHeight", height());
        mInfoWindow->show();
        break;
    }
    case PageTitle::RESET:
        CommonController::getInstance().openWarningPopup(8);
        break;
    case PageTitle::MEMORY:
    {
        mMemoryCopyWindow->rootContext()->setContextProperty("windowWidth", width());
        mMemoryCopyWindow->rootContext()->setContextProperty("windowHeight", height());
        mMemoryCopyWindow->show();
        break;
    }
    case PageTitle::SETTINGS_SAVE:
        CommonController::getInstance().openWarningPopup(10);
        break;
    case PageTitle::SETTINGS_LOAD:
        CommonController::getInstance().openWarningPopup(11);
        break;
    case PageTitle::MIX:
    {
        mMixerWindow->rootContext()->setContextProperty("windowWidth", width());
        mMixerWindow->rootContext()->setContextProperty("windowHeight", height());
        mMixerWindow->show();
        break;
    }
    case PageTitle::DELAY:
    {
        mDelayWindow->rootContext()->setContextProperty("windowWidth", width());
        mDelayWindow->rootContext()->setContextProperty("windowHeight", height());
        mDelayWindow->show();
        break;
    }
    default:
        break;
    }
}

// void MainWindow::sltGeneralSettingsClicked(int btn)
// {
//     generalSettingsPopup->hide();
// }

void MainWindow::btnMaxClicked()
{
    if(!ui->titleBar->isMaxChecked())   //判断是否最大化
    {
        showNormal();
    }
    else
    {
        mNormalRect.setRect(this->x(), this->y(), mNormalRect.width(), mNormalRect.height());
        showMaximized();
    }
}

void MainWindow::changeEvent(QEvent * event)
{
    if (NULL != event) {
        switch (event->type()) {
        case QEvent::LanguageChange:
            ui->retranslateUi(this);
            break;
        default:
            break;
        }
    }
    QMainWindow::changeEvent(event);
}

bool MainWindow::eventFilter(QObject *obj, QEvent *event)
{
    if(QEvent::WindowStateChange == event->type())
    {
        if(Qt::WindowNoState == windowState())
        {
            setGeometry(mNormalRect);
            if(ui->titleBar->isMaxChecked())
            {
                showMaximized();
            }
        }
    }
    // else if(QEvent::Resize == event->type())
    // {
    //     qInfo()<<ui->eq->width()<<ui->eq->height();
    //     if (mCurvesWidget) {
    //         mCurvesWidget->setFixedWidth(ui->eq->width());
    //         mCurvesWidget->setFixedHeight(ui->eq->height());
    //     }
    //     // 注意：QML中的CanvasView会自动适应父容器大小，不需要手动设置边距
    // }

    return QMainWindow::eventFilter(obj, event);
}

void MainWindow::initDataModel()
{
    QMap<int, bool> lineMap = CommonController::getInstance().mLineDisplayState;
    lineMap.insert(0, true);
    m_model->updateLineDisplayStatus(0, lineMap);
    m_model->updateRedPointLateralChangeState(0 == CommonController::getInstance().mChannelDataMaps[0]["eqType"].toUInt());

    int curveSelectPoint = m_model->getSelectedPointIndex();
    if(curveSelectPoint != 0)
    {
        m_model->setSelectedPoint(0, 0);
    }

    for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
    {
        m_model->setCurveHighpassFc(channel, static_cast<double>(CommonController::getInstance().mChannelDataMaps[channel]["hpFreq"].toUInt()));
        m_model->setCurveHighpassSlope(channel, (0 == CommonController::getInstance().mChannelDataMaps[channel]["hpEnable"].toUInt()) ?
                                                    0 : (CommonController::getInstance().mChannelDataMaps[channel]["hpSlope"].toUInt() * 6));
        FilterTypeUniversal hpType = FILTER_TYPE_BUTTERWORTH_HP;
        switch (CommonController::getInstance().mChannelDataMaps[channel]["hpType"].toUInt())
        {
        case 0:
            hpType = FILTER_TYPE_BUTTERWORTH_HP;
            break;
        case 1:
            hpType = FILTER_TYPE_BESSEL_HP;
            break;
        case 2:
            hpType = FILTER_TYPE_LINKWITZ_RILEY_HP;
            break;
        default:
            break;
        }
        m_model->setCurveHighpassType(channel, hpType);
        m_model->setCurveLowpassFc(channel, static_cast<double>(CommonController::getInstance().mChannelDataMaps[channel]["lpFreq"].toUInt()));
        m_model->setCurveLowpassSlope(channel, (0 == CommonController::getInstance().mChannelDataMaps[channel]["lpEnable"].toUInt()) ?
                                                    0 : (CommonController::getInstance().mChannelDataMaps[channel]["lpSlope"].toUInt() * 6));
        FilterTypeUniversal lpType = FILTER_TYPE_BUTTERWORTH_LP;
        switch (CommonController::getInstance().mChannelDataMaps[channel]["lpType"].toUInt())
        {
        case 0:
            lpType = FILTER_TYPE_BUTTERWORTH_LP;
            break;
        case 1:
            lpType = FILTER_TYPE_BESSEL_LP;
            break;
        case 2:
            lpType = FILTER_TYPE_LINKWITZ_RILEY_LP;
            break;
        default:
            break;
        }
        m_model->setCurveLowpassType(channel, lpType);

        for(int band = 0; band < EQ_BAND_MAX; band++)
        {
            m_model->setAdjustablePointParams(0, band,
                                              (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["freq"].toInt())),
                                              (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["qValue"].toInt()) / 1000),
                                              (static_cast<double>(CommonController::getInstance().mEqDataMaps[channel][band]["gain"].toInt() - 600) / 10),
                                              (static_cast<DataModel::EQType>(CommonController::getInstance().mEqDataMaps[channel][band]["type"].toInt())));
        }
    }
}
