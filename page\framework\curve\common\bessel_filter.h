#ifndef BESSEL_FILTER_H
#define BESSEL_FILTER_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

// 贝塞尔极点数据结构
typedef struct {
    double real;
    double imag;
} BesselPole;

// 声明极点表为外部变量，便于其他模块使用
extern const BesselPole bessel_poles[8][4];

/**
 * @brief 计算贝塞尔（Bessel-Thomson）滤波器在各频率点的复数响应
 * @param type 滤波器类型（3=低通，4=高通）
 * @param fc 截止频率
 * @param slope_rate 斜率（dB/oct），支持6/12/24/48等
 * @param freq_list 频率点数组
 * @param resp 输出复数响应（累乘）
 *
 * 采用标准贝塞尔多项式，双线性变换法离散化，级联二阶节，
 * 幅频/相位/群延迟与专业音频软件一致。
 */
void bessel_filter_optimized_response(int type, double fc, int slope_rate,
                                      const DoubleArray* freq_list,
                                      double_complex* resp);

/**
 * @brief 计算贝塞尔滤波器每个二阶节的系数（Bessel-Thomson，双线性变换法）
 * @param order 总阶数（偶数）
 * @param section 当前二阶节编号（0~order/2-1）
 * @param fs 采样率
 * @param fc 截止频率
 * @param coeffs 输出系数结构体
 * @param type 滤波器类型（3=低通，4=高通）
 *
 * 采用标准贝塞尔多项式，查表获得模拟域极点，双线性变换法离散化，
 * 支持多阶，低通/高通，系数与专业软件一致。
 */
void bessel_section_coeffs(int order, int section, double fs, double fc, BiquadCoeffs* coeffs, int type);

// 测试贝塞尔滤波器的函数
void test_bessel_filter(double min_freq, double max_freq,
                        int high_pass_type, double high_pass_fc, int high_pass_slope,
                        int low_pass_type, double low_pass_fc, int low_pass_slope,
                        int print_full_response);

#ifdef __cplusplus
}
#endif

#endif // BESSEL_FILTER_H