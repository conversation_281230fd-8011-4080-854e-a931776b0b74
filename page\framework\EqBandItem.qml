import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    id: root
    border.color: "red"
    color: (selectedBand === band) ? "#545860" : "transparent"

    readonly property int gainFromValue: decimalToInt(-18, gainInput.decimalFactor)// + 600
    readonly property int gainToValue: decimalToInt(18, gainInput.decimalFactor)// + 600
    readonly property int qFromValue: decimalToInt(0.404, qPeakApInput.decimalFactor)
    readonly property int qToValue: decimalToInt(28.852, qPeakApInput.decimalFactor)
    readonly property int hslsQFromValue: decimalToInt(0.404, qHsLsInput.decimalFactor)
    readonly property int hslsQToValue: decimalToInt(2, qHsLsInput.decimalFactor)

    readonly property var colorA: ["#00C8E8", "#00F090", "#D744D6", "#A870FF", "#E0A000",
        "#D0D000", "#0088FF", "#FF80C0", "#00B0A0", "#F05878"]
    readonly property var colorB: ["#00B0E0", "#00C078", "#C800C8", "#7860E8", "#C08800",
        "#A0A000", "#0088FF", "#E860B0", "#009888", "#E04868"]

    readonly property bool bypass: bypassBtn.checked

    property bool allBypass: (0 !== dataMap["uiDataMap"]["channel" + channel + "AllBypassState"])
    property int channel: dataMap["uiDataMap"]["selectedChannel"]
    property int memory: dataMap["currentMemory"]
    property color bandFocusColor: colorB[channel]
    property color focusBorderColor: colorA[channel]
    property int band: 0
    property int selectedBand: dataMap["uiDataMap"]["selectedBand"]
    property bool peqChecked: (0 === dataMap["channel" + channel + "DataMap"]["eqType"])

    property alias uiFreq: freqInput.value
    property int uiGain: 0
    property int uiQ: 4320
    property int uiType: type

    property int freq: dataMap["eqChannel" + channel + "Band" + band]["freq"]
    property int gain: dataMap["eqChannel" + channel + "Band" + band]["gain"] - 600
    property int q: dataMap["eqChannel" + channel + "Band" + band]["qValue"]
    property int type: dataMap["eqChannel" + channel + "Band" + band]["type"]

    onUiFreqChanged: {
        if(uiFreq !== freq)
        {
            commonCtrl.setEqFreq(channel, band, uiFreq)
        }
    }

    onUiGainChanged: {
        gainInput.value = uiGain
        gainSlider.value = uiGain
        bypassBtn.checked = (0 === uiGain) ? true : false

        if(uiGain !== gain)
        {
            commonCtrl.setEqGain(channel, band, (uiGain + 600))
        }
    }

    onUiQChanged: {
        console.log("uiQ",uiQ)
        console.log("q",q)
        if(qPeakApInput.visible)
        {
            qPeakApInput.value = uiQ
        }
        if(qHsLsInput.visible)
        {
            qHsLsInput.value = uiQ
        }

        if(uiQ !== q)
        {
            commonCtrl.setEqQvalue(channel, band, uiQ)
        }
    }

    onUiTypeChanged: {
        switch(uiType)
        {
        case 0x00:
            typeCombobox.currentIndex = 0
            break
        case 0x07:
            typeCombobox.currentIndex = 2
            break
        case 0x08:
            typeCombobox.currentIndex = 3
            break
        // case 0x0A:
        //     typeCombobox.currentIndex = 1
        //     break
        case 0x0B:
        {
            typeCombobox.currentIndex = 1
            uiGain = 0
            break
        }
        }

        if(uiType !== type)
        {
            commonCtrl.setEqType(channel, band, uiType)
        }
    }

    onFreqChanged: {
        if(uiFreq !== freq)
        {
            uiFreq = freq
        }
    }

    onGainChanged: {
        if(uiGain !== gain)
        {
            uiGain = gain
        }
    }

    onQChanged: {
        if(uiQ !== q)
        {
            uiQ = q
        }
    }

    onTypeChanged: {
        if(uiType !== type)
        {
            uiType = type
        }
    }

    function decimalToInt(realValue, factor) {
        return realValue * factor
    }

    function setBypass(enable) {
        if(enable)
        {
            // bypassBtn.checked = true
            dataMap["uiEqSaveGainMemory" + memory]["Channel" + channel + "Band" + band] = uiGain + 600
            uiGain = 0
        }
        else
        {
            if(600 !== dataMap["uiEqSaveGainMemory" + memory]["Channel" + channel + "Band" + band])
            {
                // bypassBtn.checked = false
                uiGain = dataMap["uiEqSaveGainMemory" + memory]["Channel" + channel + "Band" + band] - 600
            }
            else
            {
                // bypassBtn.checked = true
            }
        }
    }

    Column {
        anchors.fill: parent
        spacing: 0

        Rectangle {
            width: parent.width
            height: parent.height - 25 * 5
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            VerticalSlider {
                id: gainSlider
                anchors.centerIn: parent
                width: parent.width
                height: parent.height - 20
                orientation: Qt.Vertical
                text: band + 1
                bandFocusColor: root.bandFocusColor
                bandFocus: (selectedBand === band)
                // wheelEnabled: (selectedBand === band)
                enabled: ((1 !== typeCombobox.currentIndex) && (!allBypass))

                from: gainFromValue
                to: gainToValue
                value: uiGain
                stepSize: 1
                snapMode: Slider.SnapAlways

                onValueChanged: {
                    uiGain = Math.round(value)
                }
            }
        }

        Rectangle {
            width: parent.width
            height: 25
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            EqSpinBox {
                id: freqInput
                anchors.fill: parent
                focusBorderColor: root.focusBorderColor
                enabled: peqChecked
                // wheelEnabled: (selectedBand === band)

                from: 20
                to: 20000

                validator: RegularExpressionValidator { regularExpression: /(\d{0,5})?/ }

                textFromValue: function(value, locale) {
                    return value.toString()
                }

                valueFromText: function(text, locale) {
                    return Number(text)
                    // let re = /(\d{0,5})?/
                    // return Number(re.exec(text)[1])
                }
            }

        }

        Rectangle {
            width: parent.width
            height: 25
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            EqSpinBox {
                id: gainInput
                anchors.fill: parent
                font.bold: true
                focusBorderColor: root.focusBorderColor
                // wheelEnabled: (selectedBand === band)
                enabled: ((1 !== typeCombobox.currentIndex) && (!allBypass))

                from: gainFromValue
                to: gainToValue
                value: uiGain

                property int decimals: 1
                property real realValue: value / decimalFactor
                readonly property int decimalFactor: Math.pow(10, decimals)

                validator: RegularExpressionValidator { regularExpression: /-?(\d{0,2}(\.\d?)?)?/ }

                textFromValue: function(value, locale) {
                    // return Number(value).toLocaleString(locale, 'f', gainInput.decimals)
                    return (value / decimalFactor).toFixed(decimals).toString()
                }

                valueFromText: function(text, locale) {
                    // let re = /-?(\d{0,2}(\.\d?)?)?/
                    // return Number.fromLocaleString(locale, re.exec(text)[1]) * decimalFactor
                    // return Number(re.exec(text)[1]) * decimalFactor
                    return Number(text) * decimalFactor
                }

                onValueChanged: {
                    uiGain = value
                }
            }
        }

        Rectangle {
            width: parent.width
            height: 25
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            EqSpinBox {
                id: qPeakApInput
                anchors.fill: parent
                focusBorderColor: root.focusBorderColor
                enabled: peqChecked
                // wheelEnabled: (selectedBand === band)
                visible: (0x00 === uiType) || (0x0B === uiType)

                from: qFromValue
                to: qToValue
                value: 4320

                property int decimals: 3
                property real realValue: value / decimalFactor
                readonly property int decimalFactor: Math.pow(10, decimals)

                validator: RegularExpressionValidator { regularExpression: /(\d{0,2}(\.\d{0,3})?)?/ }

                textFromValue: function(value, locale) {
                    // return Number(value / decimalFactor).toLocaleString(locale, 'f', qInput.decimals)
                    return (value / decimalFactor).toFixed(decimals).toString()
                }

                valueFromText: function(text, locale) {
                    // let re = /(\d{0,2}(\.\d{0,3})?)?/
                    return Number(text) * decimalFactor
                }

                onValueChanged: {
                    uiQ = value
                }

                onVisibleChanged: {
                    if(visible)
                    {
                        uiQ = dataMap["uiEqSaveQMemory" + dataMap["currentMemory"]]["Channel" + channel + "Band" + band + "PeakAp"]
                    }
                }
            }

            EqSpinBox {
                id: qHsLsInput
                anchors.fill: parent
                focusBorderColor: root.focusBorderColor
                enabled: peqChecked
                // wheelEnabled: (selectedBand === band)
                visible: (0x07 === uiType) || (0x08 === uiType)

                from: hslsQFromValue
                to: hslsQToValue
                value: 2000

                property int decimals: 3
                property real realValue: value / decimalFactor
                readonly property int decimalFactor: Math.pow(10, decimals)

                validator: RegularExpressionValidator { regularExpression: /(\d{0,2}(\.\d{0,3})?)?/ }

                textFromValue: function(value, locale) {
                    // return Number(value / decimalFactor).toLocaleString(locale, 'f', qInput.decimals)
                    return (value / decimalFactor).toFixed(decimals).toString()
                }

                valueFromText: function(text, locale) {
                    // let re = /(\d{0,2}(\.\d{0,3})?)?/
                    return Number(text) * decimalFactor
                }

                onValueChanged: {
                    uiQ = value
                }

                onVisibleChanged: {
                    if(visible)
                    {
                        // if(2000 < uiQ)
                        // {
                            uiQ = dataMap["uiEqSaveQMemory" + dataMap["currentMemory"]]["Channel" + channel + "Band" + band + "HsLs"]
                        // }
                    }
                }
            }
        }

        Rectangle {
            width: parent.width
            height: 25
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            EqComboBox {
                id: typeCombobox
                anchors.fill: parent
                enabled: peqChecked
                model: ["PEAK", "AP", "LS", "HS"]
                currentIndex: 0

                property int preIndex: 0

                onCurrentIndexChanged: {
                    if(((0 === preIndex) || (1 === preIndex)) && ((2 === currentIndex) || (3 === currentIndex)))
                    {
                        dataMap["uiEqSaveQMemory" + dataMap["currentMemory"]]["Channel" + channel + "Band" + band + "PeakAp"] = uiQ
                    }
                    else if(((2 === preIndex) || (3 === preIndex)) && ((0 === currentIndex) || (1 === currentIndex)))
                    {
                        dataMap["uiEqSaveQMemory" + dataMap["currentMemory"]]["Channel" + channel + "Band" + band + "HsLs"] = uiQ
                    }
                    else
                    {

                    }

                    switch(currentIndex)
                    {
                    case 0:
                        uiType = 0x00
                        break
                    // case 1:
                    //     uiType = 0x0A
                    //     break
                    case 1:
                    {
                        uiType = 0x0B
                        dataMap["uiEqSaveGainMemory" + memory]["Channel" + channel + "Band" + band] = 600
                        break
                    }
                    case 2:
                        uiType = 0x07
                        break
                    case 3:
                        uiType = 0x08
                        break
                    }

                    preIndex = currentIndex
                }
            }
        }

        Rectangle {
            width: parent.width
            height: 25
            border.width: 0.5
            border.color: "#5C6068"
            color: "transparent"

            Button {
                id: bypassBtn
                anchors.centerIn: parent
                width: 24
                height: 18
                opacity: enabled ? 1 : 0.3
                checkable: true
                checked: (0 === uiGain)   //false: !bypass; true: bypass
                // visible: (600 !== dataMap["uiEqSaveGainMemory" + memory + "Channel" + channel + "Band" + band]["gain"]) || (0 !== uiGain)
                visible: (600 !== dataMap["uiEqSaveGainMemory" + memory]["Channel" + channel + "Band" + band]) || (0 !== uiGain)
                enabled: !allBypass
                // enabled: ((1 !== typeCombobox.currentIndex) && (!allBypass))

                contentItem: Item {
                    Image {
                        anchors.centerIn: parent
                        source: bypassBtn.checked ? "qrc:/Image/icn_byp_undo.png" : "qrc:/Image/icn_byp_reset.png"
                    }
                }

                background: Rectangle {
                    color: bypassBtn.hovered ? "#6C7078" : bypassBtn.pressed ? "#545860" : "#5C6068"
                    border.color: bypassBtn.hovered ? "#7C8088" : "#747880"
                    border.width: 1
                }

                onClicked: setBypass(bypassBtn.checked)
            }
        }
    }

    Item {
        anchors.fill: parent
        visible: (selectedBand !== band)

        MouseArea {
            anchors.fill: parent

            onPressed: function(mouse) {
                dataMap["uiDataMap"]["selectedBand"] = band
                focus = true
                mouse.accepted = true
            }
        }
    }
}
