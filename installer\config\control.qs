function Controller() { 
    installer.installationFinished.connect(this, Controller.prototype.installationFinished); 
    installer.uninstallationFinished.connect(this, Controller.prototype.uninstallationFinished); 
 
    // 完全禁用许可证相关页面 
    installer.setDefaultPageVisible(QInstaller.LicenseCheck, false); 
    installer.setDefaultPageVisible(QInstaller.Introduction, true); 
    installer.setDefaultPageVisible(QInstaller.TargetDirectory, true); 
    installer.setDefaultPageVisible(QInstaller.ComponentSelection, false); 
    installer.setDefaultPageVisible(QInstaller.ReadyForInstallation, true); 
    installer.setDefaultPageVisible(QInstaller.PerformInstallation, true); 
    installer.setDefaultPageVisible(QInstaller.InstallationFinished, true); 
} 
 
Controller.prototype.IntroductionPageCallback = function() { 
    // 确保跳过许可证页面 
    installer.setDefaultPageVisible(QInstaller.LicenseCheck, false); 
} 
 
Controller.prototype.TargetDirectoryPageCallback = function() { 
    // 强制允许覆盖安装，禁用目录检查 
    var page = gui.currentPageWidget(); 
    if (page = null) { 
        // 强制设置目录为有效 
        page.complete = true; 
        page.validatePage = function() { return true; }; 
        // 覆盖isComplete函数 
        page.isComplete = function() { return true; }; 
        // 强制启用下一步按钮 
        gui.findChild(page, "NextButton").enabled = true; 
    } 
    // 禁用内置的目录验证 
    installer.setValue("SkipTargetDirectoryPage", "false"); 
} 
 
Controller.prototype.ReadyForInstallationPageCallback = function() { 
    // 强制继续安装，忽略任何警告 
    installer.setValue("allowOverwrite", "true"); 
} 
 
Controller.prototype.installationFinished = function() { 
    // 安装完成后 
    if (systemInfo.productType === "windows") { 
        QMessageBox.information("install.finished", "Installation Completed", "DSP Controller has been successfully installed"); 
    } 
} 
 
Controller.prototype.uninstallationFinished = function() { 
    // 卸载完成后 
    if (systemInfo.productType === "windows") { 
        QMessageBox.information("uninstall.finished", "Uninstall Completed", "DSP Controller has been uninstalled"); 
    } 
} 
