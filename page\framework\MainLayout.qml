import QtQuick
import QtQuick.Layouts

ColumnLayout {
    anchors.fill: parent
    spacing: 10

    RowLayout {
        Layout.fillWidth: true
        height: 134
        Layout.leftMargin: 6
        Layout.rightMargin: 6
        spacing: 10
        
        ChannelItems {
            Layout.fillWidth: true
            height: 134
        }
        
        CrossOver {
            
        }
    }
    
    RowLayout {
        Layout.fillWidth: true
        Layout.fillHeight: true
        Layout.leftMargin: 6
        Layout.rightMargin: 6
        Layout.bottomMargin: 6
        spacing: 6
        
        ColumnLayout {
            width: 238
            Layout.fillHeight: true
            spacing: 8
            
            Speakers {
                Layout.fillHeight: true
            }

            MasterGain {
                
            }
        }
        
        ColumnLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 6

            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 6

                Curves {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                }

                ChannelList {
                    Layout.alignment: Qt.AlignTop
                }
            }

            EqConfig {
                Layout.fillWidth: true
            }
        }
    }
}
