@echo off
setlocal

:: 检查是否已编译
if not exist build\line_chart.exe (
    echo Error: Please build the project first by running build.bat
    exit /b 1
)

:: 创建发布目录
set DEPLOY_DIR=deploy
if exist %DEPLOY_DIR% rd /s /q %DEPLOY_DIR%
mkdir %DEPLOY_DIR%

:: 复制主程序
copy build\line_chart.exe %DEPLOY_DIR%\

:: 运行Qt部署工具
echo Deploying Qt dependencies...
C:\Qt\6.8.3\mingw_64\bin\windeployqt.exe --no-translations --no-system-d3d-compiler --no-opengl-sw %DEPLOY_DIR%\line_chart.exe

:: 复制额外的运行时库
copy C:\Qt\Tools\mingw1120_64\bin\libgcc_s_seh-1.dll %DEPLOY_DIR%\
copy C:\Qt\Tools\mingw1120_64\bin\libstdc++-6.dll %DEPLOY_DIR%\
copy C:\Qt\Tools\mingw1120_64\bin\libwinpthread-1.dll %DEPLOY_DIR%\

:: 复制说明文档
copy README.md %DEPLOY_DIR%\

echo.
echo Package created successfully in %DEPLOY_DIR% directory.
echo You can distribute the contents of this directory to end users. 