# 曲线显示系统（Qt6.8.3）

## 功能概述
本系统用于在Qt界面中动态显示多条滤波器曲线，所有曲线数据均通过`common.c`中的C接口动态生成，确保高性能和可扩展性。

## 主要模块
- `canvasview.cpp/h`：负责曲线的绘制，直接调用`DataModel`的滤波器参数，通过`common.c`接口生成频率点和响应。
- `datamodel.cpp/h`：管理每条曲线的滤波器类型、截止频率、斜率等参数，支持动态设置。
- `common.c/h`：提供频率点生成、滤波器复数响应计算等底层C接口。

## 主要接口说明
### common.c/h
- `DoubleArray get_oct_freq_list(double start, double end);`
  - 生成等倍频程频率点，返回DoubleArray结构体（含data指针和size）。
- `void complex_filter_combined_method(int filter_type, double fc, int slope, const DoubleArray* freq, double complex* resp);`
  - 计算滤波器在各频率点的复数响应，resp为输出数组。

### DataModel
- `void setCurveFilterParams(int curveIndex, int filterType, double fc, int slope);`
  - 设置指定曲线的滤波器类型、截止频率、斜率，自动刷新曲线。
- `int curveFilterType(int curveIndex) const;`
- `double curveFc(int curveIndex) const;`
- `int curveSlope(int curveIndex) const;`
  - 分别获取指定曲线的滤波器类型、截止频率、斜率。

### CanvasView
- `void paintEvent(QPaintEvent*);`
  - 直接调用DataModel的参数，通过common.c接口生成并绘制曲线。

## 使用说明
1. 通过DataModel设置每条曲线的参数。
2. CanvasView自动调用common.c接口生成并绘制曲线。
3. 支持多曲线、动态参数调整、实时刷新。

## 依赖
- Qt 6.8.3
- C99（用于common.c）

## 编码
- 全部源代码采用UTF-8编码。

## 联系方式
如有问题请联系开发者。

# PC



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022"
cmake --build . --config Release
```

## Integrate with your tools

- [ ] [Set up project integrations](http://home.spinach.cool:11991/xianfeng/amplifiertuning/pc/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Automatically merge when pipeline succeeds](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing(SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thank you to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README
Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

## Windows (MSVC 2022) 编译说明

- 直接用CMake生成工程，自动适配MSVC，无需手动链接m库。
- 已自动兼容MSVC下复数类型和相关函数，无需手动修改代码。
- 推荐使用Visual Studio 2022打开CMakeLists.txt，或命令行：

```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022"
cmake --build . --config Release
```

## 曲线操作说明

- CanvasView 组件已无活动曲线（activeCurve）概念，所有曲线相关操作均以 DataModel 的 m_selectedCurveIndex 为准。
- 选中曲线和点的管理全部通过 DataModel::getSelectedCurveIndex() 和 DataModel::getSelectedPointIndex() 实现。

# 项目说明

## 功能概述
本项目为专业音频曲线编辑与滤波器仿真工具，支持：
- 31个可调节点的EQ曲线编辑
- 高通/低通滤波器参数调节
- 曲线实时拖拽、交互
- 支持Butterworth、Bessel、Linkwitz-Riley等多种滤波器

## 算法说明
### 可调点增益叠加
- 每个可调点（F/Q/G）根据其EQ类型生成相应的滤波器：峰值滤波器（PEAK）、低架滤波器（LS）、高架滤波器（HS）等，所有点的频率响应在频域上**物理叠加**（复数相乘，dB上相加）。
- 多个可调点靠近时，增益会正确叠加，完全符合专业音频算法标准。
- 支持的EQ类型：PEAK（峰值）、LS（低架）、HS（高架）、AP（全通）等。
- 曲线最终响应 = 高低通滤波器响应 × 所有可调点滤波器响应。

### 主要接口
- `processLinePointsWithFilter`：负责所有滤波器与可调点的物理叠加。
- `calc_iir_filter_coeff`、`calc_single_biquad_resp`：用于生成每个点的滤波器系数与响应。

## 使用方法
- 拖动可调点即可实时调整EQ曲线，支持多点叠加。
- 拖动高通/低通点可调整滤波器参数。

## 版本
- Qt 6.8.3
- 支持Windows平台

## 编译
- 使用 `build-qt.bat` 编译

## 打包
- 使用 `packaging\make_installer_qt.bat` 打包

## 变更日志
- 2024-06：修复可调点增益叠加算法，支持物理叠加，多个点靠近时峰值会相加，符合专业音频标准。
- 2024-06-09：修复拖拽可调节点时的误操作bug：只有在按下选中点后才允许拖拽移动，防止在空白区域拖动导致点位置被错误更新。

# 滤波器算法说明与修正记录

## 功能简介
本项目支持贝塞尔、巴特沃斯、宁克-锐（Linkwitz-Riley）等多种高低通滤波器，所有算法已对齐专业调音软件标准。

## 主要修正内容
- 自动补全宁克-锐滤波器优化响应（linkwitz_riley_optimized_response），采用标准Butterworth二阶级联，支持多阶，幅频/相位与专业软件一致。
- 所有接口均带有详细doxygen注释，便于二次开发与自动化测试。
- 支持自动化测试与日志输出，便于与专业软件对比。

## 用法说明
- 主要接口：
  - `complex_filter_combined_method`：组合型滤波器响应计算
  - `linkwitz_riley_optimized_response`：宁克-锐滤波器响应
  - `bessel_filter_optimized_response`：贝塞尔滤波器响应
  - `butterworth_section_coeffs`：巴特沃斯二阶节系数
- 参数说明：
  - type：滤波器类型（详见common.h）
  - fc：截止频率
  - slope_rate：斜率（dB/oct）
  - freq_list：频率点数组
- 返回值：复数响应数组，可用于幅频/相频/群延迟等分析

## 验证方法
- 调用`test_filter`、`process_filter_points`等接口，自动输出关键频点响应。
- 日志输出每阶参数与最终响应，便于与专业软件对比。

## 其他说明
- 所有代码、注释、文档均为中文，编码UTF-8。
- 自动化修正与验证过程详见`修正记录.md`。

## 贝塞尔滤波器算法修正说明

### 主要修正内容
- 贝塞尔滤波器（Bessel-Thomson）已采用标准多项式和双线性变换法，支持多阶（6/12/24/48dB/oct），幅频/相位/群延迟与专业音频软件一致。
- 所有接口均带有详细doxygen注释，便于二次开发与自动化测试。
- 支持自动化测试与日志输出，便于与专业软件对比。

### 用法说明
- 主要接口：
  - `bessel_filter_optimized_response`：贝塞尔滤波器响应（标准化实现）
  - `bessel_section_coeffs`：贝塞尔二阶节系数（查表+双线性变换）
- 参数说明：
  - type：滤波器类型（3=低通，4=高通）
  - fc：截止频率
  - slope_rate：斜率（dB/oct）
  - freq_list：频率点数组
- 返回值：复数响应数组，可用于幅频/相频/群延迟等分析

### 验证方法
- 调用`test_bessel_filter`、`process_filter_points`等接口，自动输出关键频点响应。
- 日志输出每阶极点、系数与最终响应，便于与专业软件对比。

### 其他说明
- 所有代码、注释、文档均为中文，编码UTF-8。
- 自动化修正与验证过程详见`修正记录.md`。

## 巴特沃斯滤波器算法修正说明

### 主要修正内容
- 巴特沃斯滤波器已采用标准多阶二阶节级联算法，支持高通/低通、多阶（6/12/24/48dB/oct），幅频/相位与专业音频软件一致。
- 废弃原Q=4.32单节叠加方式，所有阶数均走标准级联。
- 所有接口均带有详细doxygen注释，便于二次开发与自动化测试。
- 支持自动化测试与日志输出，便于与专业软件对比。

### 用法说明
- 主要接口：
  - `butterworth_filter_optimized_response`：巴特沃斯滤波器响应（标准化实现）
  - `butterworth_section_coeffs`：巴特沃斯二阶节系数（标准级联）
- 参数说明：
  - type：滤波器类型（5=低通，6=高通）
  - fc：截止频率
  - slope_rate：斜率（dB/oct）
  - freq_list：频率点数组
- 返回值：复数响应数组，可用于幅频/相频/群延迟等分析

### 验证方法
- 调用`test_filter`、`process_filter_points`等接口，自动输出关键频点响应。
- 日志输出每阶系数与最终响应，便于与专业软件对比。

### 其他说明
- 所有代码、注释、文档均为中文，编码UTF-8。
- 自动化修正与验证过程详见`修正记录.md`。

## 曲线生成说明

- 所有通道的曲线点增益采用物理叠加方式：
  - 先计算高低通滤波器的复数响应；
  - 对每个可调点，生成PEQ滤波器响应并复数相乘叠加；
  - 最终幅度为20*log10(|总响应|)。
- 不再使用插值算法，所有点的增益均由滤波器物理叠加获得，完全对齐专业调音软件。

# 滤波器实现说明

## 一阶滤波器实现
- **Bessel一阶**与**Butterworth一阶**模拟极点相同，数字化后系数理论上一致，区别主要体现在高阶滤波器。
- **Linkwitz-Riley一阶**时，自动退化为Butterworth一阶滤波器。

## 主要修正点
- 修复了Linkwitz-Riley一阶高通/低通无效的问题。
- 完善了相关日志和注释。

## 使用说明
- 选择Linkwitz-Riley一阶时，实际效果等同于Butterworth一阶。
- Bessel与Butterworth一阶响应一致，二阶及以上才有区别。
