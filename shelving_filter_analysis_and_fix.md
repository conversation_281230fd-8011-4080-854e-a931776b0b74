# Shelving Filter 问题分析与修复报告

## 问题概述

当前系统中31个可调点的EQ_TYPE_LS（低架）和EQ_TYPE_HS（高架）类型没有正确影响整个曲线的低频和高频响应。

## 问题分析

### 1. 主要问题：EQ类型映射错误

**位置**: `page/framework/curve/datamodel.cpp:704`

**当前实现**:
```cpp
calc_iir_filter_coeff(FS, 0, pt.frequency, pt.gain, pt.qValue, &coeffs); // 0=PEQ_PEAKING_FILTER
```

**问题**: 所有可调点都被强制处理为`PEQ_PEAKING_FILTER`（类型0），完全忽略了点的实际EQ类型（`pt.type`）。

### 2. 次要问题：Shelving Filter公式不完全标准

**位置**: `page/framework/curve/common/common.cpp:272-293`

**当前实现问题**:
- `beta = sqrtf(A) / q_value` 应该是 `beta = 2 * sqrtf(A) * alpha`
- 缺少标准Audio EQ Cookbook中的因子`2`

## 标准算法参考

根据Robert Bristow-Johnson的Audio EQ Cookbook标准：

### Low Shelf Filter:
```
A = 10^(dBgain/40)
ω₀ = 2π * f₀ / fs
α = sin(ω₀) / (2*Q)
beta = 2*sqrt(A)*α

b0 = A*((A+1) - (A-1)*cos(ω₀) + beta)
b1 = 2*A*((A-1) - (A+1)*cos(ω₀))
b2 = A*((A+1) - (A-1)*cos(ω₀) - beta)
a0 = (A+1) + (A-1)*cos(ω₀) + beta
a1 = -2*((A-1) + (A+1)*cos(ω₀))
a2 = (A+1) + (A-1)*cos(ω₀) - beta
```

### High Shelf Filter:
```
b0 = A*((A+1) + (A-1)*cos(ω₀) + beta)
b1 = -2*A*((A-1) + (A+1)*cos(ω₀))
b2 = A*((A+1) + (A-1)*cos(ω₀) - beta)
a0 = (A+1) - (A-1)*cos(ω₀) + beta
a1 = 2*((A-1) - (A+1)*cos(ω₀))
a2 = (A+1) - (A-1)*cos(ω₀) - beta
```

## 修复方案

### 修复1: EQ类型正确映射

**文件**: `page/framework/curve/datamodel.cpp`
**行数**: 698-750

已修复：添加了EQ类型到滤波器类型的正确映射：
- `EQ_TYPE_PEAK` → `PEQ_PEAKING_FILTER` (0)
- `EQ_TYPE_LS1`, `EQ_TYPE_LS2` → `PEQ_LOW_SHELF_FILTER` (1)
- `EQ_TYPE_HS1`, `EQ_TYPE_HS2` → `PEQ_HIGH_SHELF_FILTER` (2)
- `EQ_TYPE_LP1`, `EQ_TYPE_LP2` → `PEQ_LOWPASS_FILTER` (3)
- `EQ_TYPE_HP2` → `PEQ_HIGHPASS_FILTER` (4)

### 修复2: Shelving Filter公式标准化

**文件**: `page/framework/curve/common/common.cpp`
**行数**: 272-295

已修复：
- 修正了`beta`计算公式：`beta = 2 * sqrtf(A) * alpha`
- 简化了系数计算，移除了多余的`sin_omega`项
- 确保与Audio EQ Cookbook标准一致

## 修复效果

### 修复前:
- 所有可调点都表现为峰值滤波器
- EQ_TYPE_LS和EQ_TYPE_HS点无法产生正确的架形响应
- Shelving filter响应可能不准确

### 修复后:
- EQ_TYPE_LS点将产生正确的低架响应，影响整个低频段
- EQ_TYPE_HS点将产生正确的高架响应，影响整个高频段
- Shelving filter响应符合专业音频标准
- 保持与高低通滤波器的兼容性

## UI层面的EQ类型选择

在`EqBandItem.qml`中，用户可以通过下拉框选择以下EQ类型：
- "PEAK" → 0x00 (EQ_TYPE_PEAK) - 峰值滤波器
- "AP" → 0x0B (EQ_TYPE_AP2) - 全通滤波器
- "LS" → 0x07 (EQ_TYPE_LS2) - 低架滤波器
- "HS" → 0x08 (EQ_TYPE_HS2) - 高架滤波器

**重要发现**: 用户界面已经支持shelving filter类型选择，但之前由于算法层面的bug，这些设置没有生效。

## 验证建议

1. **功能测试**:
   - 在UI中选择一个EQ点，设置类型为"LS"，增益+6dB，频率1kHz
   - 验证1kHz以下频率是否有架形提升（应该看到低频整体抬升）
   - 在UI中选择一个EQ点，设置类型为"HS"，增益+6dB，频率1kHz
   - 验证1kHz以上频率是否有架形提升（应该看到高频整体抬升）

2. **对比测试**:
   - 与专业音频软件（如Pro Tools、Logic Pro）的shelving EQ对比
   - 验证频率响应曲线的形状和增益分布
   - 确保shelving响应是平滑的架形，而不是峰值形状

3. **兼容性测试**:
   - 确保高低通滤波器功能不受影响
   - 验证峰值滤波器（"PEAK"类型）仍正常工作
   - 测试全通滤波器（"AP"类型）是否正常

## 技术细节

### EQ类型枚举对应关系:
```cpp
// DataModel::EQType → calc_iir_filter_coeff type parameter
EQ_TYPE_PEAK = 0x00 → PEQ_PEAKING_FILTER = 0
EQ_TYPE_LS1 = 0x03  → PEQ_LOW_SHELF_FILTER = 1
EQ_TYPE_HS1 = 0x04  → PEQ_HIGH_SHELF_FILTER = 2
EQ_TYPE_LS2 = 0x07  → PEQ_LOW_SHELF_FILTER = 1
EQ_TYPE_HS2 = 0x08  → PEQ_HIGH_SHELF_FILTER = 2
```

### 物理意义:
- **Peak Filter**: 在特定频率附近产生局部增益/衰减
- **Low Shelf**: 在截止频率以下的所有频率产生相同增益
- **High Shelf**: 在截止频率以上的所有频率产生相同增益

这个修复确保了EQ系统的行为符合专业音频处理的标准和用户期望。
