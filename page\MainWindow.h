﻿#ifndef MAINWINDOW_H
#define MAIN<PERSON>NDOW_H

#include <QMainWindow>
#include <QMouseEvent>
#include <QQuickWidget>
#include <QFileDialog>
#include <QMessageBox>
#include <QTranslator>
#include <QQmlEngine>
#include <QDateTime>
#include <QProcess>

// #include "framework/GeneralSettingsPopup.h"
#include "./page/framework/ChannelItem.h"
#include "CommonController.h"
#include "./page/framework/curve/datamodel.h"
#include <QWinEventNotifier>

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;

    void changeEvent(QEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void sltCloseEqLinkPopup();
    void sltCloseDeviceSetting();
    void sltCloseSourceLevel();
    void sltCloseInfo();
    void sltCloseMemoryCopy();
    void sltCloseMixer();
    void sltCloseDelay();
    void sltCancelWarning();
    void sltConfirmWarning();

    // void sltGeneralSettingsClicked(int btn);
    void sltTitleClicked(int btn);

private:
    void btnMaxClicked();
    void initDataModel();

private:
    Ui::MainWindow *ui;

    QTranslator* mTranslator;
    QRect mNormalRect;
    QByteArray mDspUpgradeFileStr;

    QQuickWidget* mInitWindow;
    QQuickWidget* mEqLinkWindow;
    QQuickWidget* mDeviceSettingWindow;
    QQuickWidget* mSourceLevelWindow;
    QQuickWidget* mInfoWindow;
    QQuickWidget* mMemoryCopyWindow;
    QQuickWidget* mMixerWindow;
    QQuickWidget* mDelayWindow;
    QQuickWidget* mWarningWindow;

    bool mIsMouseBtnLeftPressed;
    QPointF mMousePos;
    // GeneralSettingsPopup* generalSettingsPopup;
    ChannelItem* mChannelItems[OUTPUT_CHANNEL_MAX];

    DataModel* m_model;  // DataModel单例指针（不拥有所有权）
    QQuickWidget* mCurvesWidget;
};
#endif // MAINWINDOW_H
