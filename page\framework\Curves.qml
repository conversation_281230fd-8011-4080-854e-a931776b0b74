import QtQuick
import QtQuick.Layouts
import CanvasView 1.0

Item {
    width: 1058
    height: 224

    RowLayout {
        anchors.fill: parent
        spacing: 6

        ColumnLayout {
            width: 28
            Layout.fillHeight: true
            Layout.topMargin: 2
            Layout.bottomMargin: 18
            spacing: 0

            Repeater {
                model: ["20", "15", "10", "5", "0", "-5", "-10", "-15"]

                Text {
                    Layout.alignment: Qt.AlignRight
                    width: 28
                    Layout.minimumHeight: 15
                    Layout.fillHeight: true
                    color: "#C8C8C8"
                    font.family: "Segoe UI"
                    font.pixelSize: 12
                    horizontalAlignment: Text.AlignRight
                    verticalAlignment: Text.AlignTop
                    rightPadding: 2
                    text: modelData
                }
            }

            Text {
                Layout.alignment: Qt.AlignRight
                width: 28
                height: 15
                color: "#C8C8C8"
                font.family: "Segoe UI"
                font.pixelSize: 12
                horizontalAlignment: Text.AlignRight
                verticalAlignment: Text.AlignTop
                rightPadding: 2
                text: "-20"
            }
        }

        ColumnLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 0

            Image {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.leftMargin: 10
                Layout.rightMargin: 10
                Layout.topMargin: 10
                Layout.bottomMargin: 6
                source: "qrc:/Image/curveBg.png"
            }

            RowLayout {
                Layout.fillWidth: true
                height: 20
                spacing: 0

                Repeater {
                    model: ListModel {
                        ListElement {
                            itemWidth: 124
                            txt: "20"
                            factor: 124
                        }
                        ListElement {
                            itemWidth: 92
                            txt: "50"
                            factor: 92
                        }
                        ListElement {
                            itemWidth: 91
                            txt: "100"
                            factor: 91
                        }
                        ListElement {
                            itemWidth: 121
                            txt: "200"
                            factor: 121
                        }
                        ListElement {
                            itemWidth: 94
                            txt: "500"
                            factor: 94
                        }
                        ListElement {
                            itemWidth: 88
                            txt: "1K"
                            factor: 88
                        }
                        ListElement {
                            itemWidth: 127
                            txt: "2K"
                            factor: 127
                        }
                        ListElement {
                            itemWidth: 91
                            txt: "5K"
                            factor: 91
                        }
                        ListElement {
                            itemWidth: 92
                            txt: "10K"
                            factor: 92
                        }
                        ListElement {
                            itemWidth: 90
                            txt: "20K"
                            factor: 90
                        }
                        ListElement {
                            itemWidth: 20
                            txt: "40K"
                            factor: -1
                        }
                    }

                    Item {
                        Layout.fillWidth: true
                        Layout.horizontalStretchFactor: factor
                        width: itemWidth
                        height: 20

                        Text {
                            anchors.horizontalCenter: parent.left
                            anchors.horizontalCenterOffset: width / 2
                            anchors.verticalCenter: parent.verticalCenter
                            width: 20
                            height: 8
                            color: "#C8C8C8"
                            font.family: "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: txt
                        }
                    }
                }
            }
        }
    }

    CanvasView {
        id: canvasView
        // anchors.centerIn: parent
        anchors.fill: parent
        // dataModel会自动使用单例，不需要外部传入

        // 连接信号到外部处理
        onAdjustablePointChanged: function(pointIndex, frequency, qValue, gain) {
            // console.log("Adjustable point changed:", pointIndex, frequency, qValue, gain)
        }

        onHighpassPointChanged: function(frequency) {
            commonCtrl.setOutputChannelHpFreq(dataMap["uiDataMap"]["selectedChannel"], frequency);
        }

        onLowpassPointChanged: function(frequency) {
            commonCtrl.setOutputChannelLpFreq(dataMap["uiDataMap"]["selectedChannel"], frequency);
        }
    }
}
