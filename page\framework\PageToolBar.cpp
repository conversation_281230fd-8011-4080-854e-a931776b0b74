#include "PageToolBar.h"
#include "ui_PageToolBar.h"

#include <QListView>
#include <QCheckBox>
#include <QSpinBox>
#include <QStyleFactory>

PageToolBar::PageToolBar(QWidget *parent)
    : Q<PERSON>rame(parent)
    , ui(new Ui::PageToolBar)
    , mMusicSource(MUSICSOURCE_MAIN_UNIT)
    , mMemory(MEMORY_DEFAULT)
    , mDspMute(false)
    , mDSPVolumn(0)
    , mBassMute(false)
    , mBassLevel(-8)
    , mBtAudioStatus(false)
{
    ui->setupUi(this);

    ui->bassVolumeMute->setVisible(false);

    ui->volumnPlus->setAutoRepeat(true);
    ui->volumnPlus->setAutoRepeatInterval(200);
    ui->volumnMinus->setAutoRepeat(true);
    ui->volumnMinus->setAutoRepeatInterval(200);
    ui->bassPlus->setAutoRepeat(true);
    ui->bassPlus->setAutoRepeatInterval(200);
    ui->bassMinus->setAutoRepeat(true);
    ui->bassMinus->setAutoRepeatInterval(200);

    ui->DspVolumnSlider->setStyle(QStyleFactory::create("fusion"));
    ui->DspVolumnSlider->setPageStep(0);

    ui->DSPVolumn->installEventFilter(this);
    ui->bassLevel->installEventFilter(this);

    mSourceActions.clear();
    QMenu* sourceMenu = new QMenu(this);
    sourceMenu->setStyleSheet("QMenu { background-color: #484C54; color: #E8E8E8; border: 1px, solid, #646870; }"
                              "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                              "QMenu::item { background-color: #484C54; color: #E8E8E8; width: 80px; height: 24px; padding-left: 6px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                              "QMenu::item:disabled { background-color: #4D484C54; color: #4DE8E8E8; }"
                              "QMenu::item:selected { background-color: #646870; }");

    mSourceActions.insert(MUSICSOURCE_MAIN_UNIT, sourceMenu->addAction("Main Unit", [&](){setMusicSource(MUSICSOURCE_MAIN_UNIT);}));
    mSourceActions.insert(MUSICSOURCE_USB_AUDIO, sourceMenu->addAction("USB AUDIO", [&](){setMusicSource(MUSICSOURCE_USB_AUDIO);}));
    mSourceActions.insert(MUSICSOURCE_BT_AUDIO, sourceMenu->addAction("BT AUDIO", [&](){setMusicSource(MUSICSOURCE_BT_AUDIO);}));
    mSourceActions.insert(MUSICSOURCE_SPDIF, sourceMenu->addAction("SPDIF", [&](){setMusicSource(MUSICSOURCE_SPDIF);}));
    mSourceActions.insert(MUSICSOURCE_AUX, sourceMenu->addAction("AUX", [&](){setMusicSource(MUSICSOURCE_AUX);}));

    ui->musicSource->setMenu(sourceMenu);
    ui->musicSource->setStyleSheet("QPushButton{background-color: #5C6068; border: 1px solid #747880; border-radius: 2px;"
                                   "padding-left: 6px; padding-right: 24px; font-size: 12px; color: #E8E8E8; text-align: left;}"
                                   "QPushButton::menu-indicator { width: 24px; height: 24px; subcontrol-position: center right; image: url(:/Image/down.png); }");

    mMemoryActions.clear();
    QMenu* muMemoryMenu = new QMenu(this);
    muMemoryMenu->setStyleSheet("QMenu { background-color: #484C54; color: #E8E8E8; border: 1px, solid, #646870; }"
                                "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                                "QMenu::item { background-color: #484C54; color: #E8E8E8; width: 220; height: 24px; padding-left: 6px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                                "QMenu::item:disabled { background-color: #4D484C54; color: #4DE8E8E8; }"
                                "QMenu::item:selected { background-color: #646870; }");

    mMemoryActions.insert(MEMORY_M1, muMemoryMenu->addAction("M1 : ", [&]{setMemory(MEMORY_M1);}));
    mMemoryActions.insert(MEMORY_M2, muMemoryMenu->addAction("M2 : ", [&]{setMemory(MEMORY_M2);}));
    mMemoryActions.insert(MEMORY_M3, muMemoryMenu->addAction("M3 : ", [&]{setMemory(MEMORY_M3);}));
    mMemoryActions.insert(MEMORY_M4, muMemoryMenu->addAction("M4 : ", [&]{setMemory(MEMORY_M4);}));
    mMemoryActions.insert(MEMORY_M5, muMemoryMenu->addAction("M5 : ", [&]{setMemory(MEMORY_M5);}));
    mMemoryActions.insert(MEMORY_M6, muMemoryMenu->addAction("M6 : ", [&]{setMemory(MEMORY_M6);}));

    ui->muMemoryList->setMenu(muMemoryMenu);

    QMenu* dspMemoryMenu = new QMenu(this);
    dspMemoryMenu->setStyleSheet("QMenu { background-color: #484C54; color: #E8E8E8; border: 1px, solid, #646870; }"
                                 "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                                 "QMenu::item { background-color: #484C54; color: #E8E8E8; width: 220; height: 24px; padding-left: 6px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                                 "QMenu::item:disabled { background-color: #4D484C54; color: #4DE8E8E8; }"
                                 "QMenu::item:selected { background-color: #646870; }");

    mMemoryActions.insert(MEMORY_S1, dspMemoryMenu->addAction("S1 : ", [&]{setMemory(MEMORY_S1);}));
    mMemoryActions.insert(MEMORY_S2, dspMemoryMenu->addAction("S2 : ", [&]{setMemory(MEMORY_S2);}));
    mMemoryActions.insert(MEMORY_S3, dspMemoryMenu->addAction("S3 : ", [&]{setMemory(MEMORY_S3);}));
    mMemoryActions.insert(MEMORY_S4, dspMemoryMenu->addAction("S4 : ", [&]{setMemory(MEMORY_S4);}));
    mMemoryActions.insert(MEMORY_S5, dspMemoryMenu->addAction("S5 : ", [&]{setMemory(MEMORY_S5);}));
    mMemoryActions.insert(MEMORY_S6, dspMemoryMenu->addAction("S6 : ", [&]{setMemory(MEMORY_S6);}));

    ui->dspMemoryList->setMenu(dspMemoryMenu);

    ui->dspMemoryList->setVisible(false);
    ui->muMemoryList->setEnabled(true);

    mSourceActions[MUSICSOURCE_USB_AUDIO]->setEnabled(false);
    mSourceActions[MUSICSOURCE_BT_AUDIO]->setEnabled(true);
    mSourceActions[MUSICSOURCE_SPDIF]->setEnabled(false);
    mSourceActions[MUSICSOURCE_AUX]->setEnabled(false);

    // connect(ui->musicSource, QOverload<int>::of(&QComboBox::currentIndexChanged), [&](int index) {
    //     setMusicSource(static_cast<ENMusicSource>(index));
    // });

    // connect(ui->muMemoryList, QOverload<int>::of(&QComboBox::currentIndexChanged), [&](int index) {
    //     ENMemory memory;
    //     if(-1 == index)
    //     {
    //         memory = MEMORY_DEFAULT;
    //     }
    //     else
    //     {
    //         memory = (static_cast<ENMemory>(index + MEMORY_M1));
    //     }
    //     setMemory(memory);
    // });

    // connect(ui->dspMemoryList, QOverload<int>::of(&QComboBox::currentIndexChanged), [&](int index) {
    //     ENMemory memory;
    //     if(-1 == index)
    //     {
    //         memory = MEMORY_DEFAULT;
    //     }
    //     else
    //     {
    //         memory = (static_cast<ENMemory>(index + MEMORY_S1));
    //     }
    //     setMemory(memory);
    // });

    connect(ui->DSPVolumeMute, &QPushButton::clicked, [this](bool isMute) {
        setDspMute(isMute);
    });

    connect(ui->DSPVolumn, &QSpinBox::valueChanged, [this]() {
        if(mDSPVolumn != ui->DSPVolumn->value())
        {
            setDspVolumn(ui->DSPVolumn->value());

            qInfo("DSPVolumn valueChanged, mDSPVolumn: %d", mDSPVolumn);
        }
    });

    connect(ui->volumnPlus, &QPushButton::clicked, [this] {
        int volumn = mDSPVolumn + ui->DspVolumnSlider->singleStep();
        if(volumn <= ui->DSPVolumn->maximum())
        {
            setDspVolumn(volumn);

            qInfo("volumnPlus clicked, mDSPVolumn: %d", mDSPVolumn);
        }
    });

    connect(ui->volumnMinus, &QPushButton::clicked, [this] {
        int volumn = mDSPVolumn - ui->DspVolumnSlider->singleStep();
        if(volumn >= ui->DSPVolumn->minimum())
        {
            setDspVolumn(volumn);

            qInfo("volumnMinus clicked, mDSPVolumn: %d", mDSPVolumn);
        }
    });

    connect(ui->DspVolumnSlider, &QSlider::valueChanged, [this](int value) {
        if(mDSPVolumn != value)
        {
            setDspVolumn(value);

            qInfo("DspVolumnSlider valueChanged, mDSPVolumn: %d", mDSPVolumn);
        }
    });

    connect(ui->bassVolumeMute, &QPushButton::clicked, [this](bool isMute) {
        setBassMute(isMute);
    });

    connect(ui->bassLevel, &QSpinBox::valueChanged, [this]() {
        if(mBassLevel != (ui->bassLevel->value() + 8))
        {
            setBassLevel(ui->bassLevel->value() + 8);

            qInfo("bassLevel valueChanged, mBassLevel: %d", mBassLevel);
        }
    });

    connect(ui->bassPlus, &QPushButton::clicked, [this] {
        int level = mBassLevel + ui->bassLevel->singleStep();
        if(level <= (ui->bassLevel->maximum() + 8))
        {
            setBassLevel(level);

            qInfo("bassPlus clicked, mBassLevel: %d", mBassLevel);
        }
    });

    connect(ui->bassMinus, &QPushButton::clicked, [this] {
        int level = mBassLevel - ui->bassLevel->singleStep();
        if(level >= (ui->bassLevel->minimum() + 8))
        {
            setBassLevel(level);

            qInfo("bassMinus clicked, mBassLevel: %d", mBassLevel);
        }
    });

    connect(ui->bt, &QPushButton::clicked, [&]() {
        QString btOnQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btOn.png); }";
        QString btOffQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btOff.png); }";
        QString btConnectingQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btConnecting.png); }";
        switch (mBtAudioStatus) {
        case 0:
        case 1:
        {
            ui->bt->setStyleSheet(btConnectingQss);
            mBtAudioStatus = 2;
            emit btAudioStatusChanged(mBtAudioStatus);
            break;
        }
        default:
            break;
        }
    });
}

PageToolBar::~PageToolBar()
{
    delete ui;
}

void PageToolBar::setMusicSourceList(int deviceLevel)
{
    mDeviceLevel = deviceLevel;
    mSourceActions[MUSICSOURCE_USB_AUDIO]->setVisible((1 != deviceLevel));
    mSourceActions[MUSICSOURCE_SPDIF]->setVisible((1 != deviceLevel));
}

void PageToolBar::setMusicSourceEnabled(ENMusicSource source, bool isEnabled)
{
    // int role = isEnabled ? Qt::UserRole : (Qt::UserRole - 1);
    switch (source) {
    case MUSICSOURCE_USB_AUDIO:
    case MUSICSOURCE_BT_AUDIO:
    case MUSICSOURCE_SPDIF:
    case MUSICSOURCE_AUX:
        mSourceActions[source]->setEnabled(isEnabled);
        if((mMusicSource == source) && (!isEnabled))
        {
            setMusicSource(MUSICSOURCE_MAIN_UNIT);
        }
        break;
    default:
        break;
    }
}

void PageToolBar::setMusicSource(ENMusicSource source)
{
    if(mMusicSource != source)
    {
        mMusicSource = source;
        switch (mMusicSource) {
        case MUSICSOURCE_DEFAULT:
        {
            ui->musicSource->setText("");
            ui->muMemoryList->setVisible(true);
            ui->muMemoryList->setEnabled(false);
            ui->dspMemoryList->setVisible(false);
            ui->dspMemoryList->setEnabled(false);
            break;
        }
        case MUSICSOURCE_MAIN_UNIT:
        {
            ui->musicSource->setText(mSourceActions[source]->text());
            ui->muMemoryList->setVisible(true);
            ui->muMemoryList->setEnabled(true);
            ui->dspMemoryList->setVisible(false);
            ui->dspMemoryList->setEnabled(false);
            break;
        }
        default:
        {
            ui->musicSource->setText(mSourceActions[source]->text());
            ui->muMemoryList->setVisible(false);
            ui->muMemoryList->setEnabled(false);
            ui->dspMemoryList->setVisible(true);
            ui->dspMemoryList->setEnabled(true);
            break;
        }
        }
        emit musicSourceChanged(mMusicSource);
    }
}

void PageToolBar::setMemoryName(uint8_t type, uint8_t memoryId, QString name)
{
    if(0 == type)
    {
        mMemoryActions[static_cast<ENMemory>(memoryId)]->setText(QString("M%1 : %2").arg(memoryId + 1).arg(name));
    }
    else
    {
        mMemoryActions[static_cast<ENMemory>(memoryId + MEMORY_S1)]->setText(QString("S%1 : %2").arg(memoryId + 1).arg(name));
    }

    if(MEMORY_DEFAULT == mMemory)
    {
        if(MUSICSOURCE_MAIN_UNIT == mMusicSource)
        {
            ui->muMemoryList->setText("");
        }
        else
        {
            ui->dspMemoryList->setText("");
        }
    }
    else if(MEMORY_S1 > mMemory)
    {
        ui->muMemoryList->setText(mMemoryActions[mMemory]->text());
    }
    else
    {
        ui->dspMemoryList->setText(mMemoryActions[mMemory]->text());
    }
}

void PageToolBar::setMemoryEnabled(uint8_t type, uint8_t memoryId, bool enable)
{
    if(0 == type)
    {
        mMemoryActions[static_cast<ENMemory>(memoryId)]->setEnabled(enable);
    }
    else
    {
        mMemoryActions[static_cast<ENMemory>(memoryId + MEMORY_S1)]->setEnabled(enable);
    }
}

void PageToolBar::setMemory(ENMemory memory)
{
    if(mMemory != memory)
    {
        mMemory = memory;
        if(MEMORY_DEFAULT == mMemory)
        {
            if(MUSICSOURCE_MAIN_UNIT == mMusicSource)
            {
                ui->muMemoryList->setText("");
            }
            else
            {
                ui->dspMemoryList->setText("");
            }
        }
        else if(MEMORY_S1 > mMemory)
        {
            ui->muMemoryList->setText(mMemoryActions[memory]->text());
        }
        else
        {
            ui->dspMemoryList->setText(mMemoryActions[memory]->text());
        }
        emit memoryChanged(mMemory);
    }
}

void PageToolBar::setDspMute(bool isMute)
{
    if(mDspMute != isMute)
    {
        mDspMute = isMute;
        ui->DSPVolumeMute->setChecked(isMute);
        emit dspMuteChanged(isMute);
    }
}

void PageToolBar::setDspVolumn(int value)
{
    if(mDSPVolumn != value)
    {
        mDSPVolumn = value;
        ui->DspVolumnSlider->setValue(value);
        ui->DSPVolumn->setValue(value);
        emit dspVolumnChanged(mDSPVolumn);
    }
}

void PageToolBar::setBassMute(bool isMute)
{
    if(mBassMute != isMute)
    {
        mBassMute = isMute;
        ui->bassVolumeMute->setChecked(isMute);
        emit bassMuteChanged(isMute);
    }
}

void PageToolBar::setBassLevel(int value)
{
    if(mBassLevel != value)
    {
        mBassLevel = value;
        ui->bassLevel->setValue(value - 8);
        emit bassLevelChanged(mBassLevel);
    }
}

void PageToolBar::setBtAudioStatus(int status)
{
    if(mBtAudioStatus != status)
    {
        mBtAudioStatus = status;
        QString btOnQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btOn.png); }";
        QString btOffQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btOff.png); }";
        QString btConnectingQss = "QPushButton { border-radius: 13px; background-image: url(:/Image/btConnecting.png); }";
        switch (mBtAudioStatus) {
        case 0:
            ui->bt->setStyleSheet(btOffQss);
            break;
        case 1:
            ui->bt->setStyleSheet(btOnQss);
            break;
        case 2:
            ui->bt->setStyleSheet(btConnectingQss);
            break;
        default:
            break;
        }
    }
}

bool PageToolBar::eventFilter(QObject *target, QEvent *event)
{
    if((target == ui->DSPVolumn) || (target == ui->bassLevel))
    {
        if(event->type() == QEvent::Wheel)
        {
            return true;
        }
    }

    return QFrame::eventFilter(target, event);
}
