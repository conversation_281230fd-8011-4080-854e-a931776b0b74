#include "Mid06Handler.h"
#include "qdebug.h"

Mid06Handler::Mid06Handler()
{
    memset(&mRemoteOperation0102, 0, sizeof(mRemoteOperation0102));
    memset(&mRemoteOperation0304, 0, sizeof(mRemoteOperation0304));
    RemoteShortCutInfo info;
    mRemoteShortCuts.clear();
    mRemoteShortCuts.resize(REMOTE_SHORTCUT_MAX, info);
}

void Mid06Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(RemoteOperation::SID::BRIGHTNESS_QUERY):
    {
        parse02Data(data);
        break;
    }
    case static_cast<uint8_t>(RemoteOperation::SID::MODEL_QUERY):
    {
        parse04Data(data);
        break;
    }
    case static_cast<uint8_t>(RemoteOperation::SID::SHORTCUT_QUERY):
    {
        parse06Data(data);
        break;
    }
    default:
        break;
    }
}

void Mid06Handler::parse02Data(const QByteArray &data)
{
    // bool ret = false;
    RemoteOperation0102 temp02;
    memcpy(&temp02, data.data(), data.size());

    // if(temp02.brightness != mRemoteOperation0102.brightness)
    // {
    emit remoteBrightnessChanged(temp02.brightness);
    //     ret = true;
    // }
    // if(temp02.dimmer != mRemoteOperation0102.dimmer)
    // {
    emit remoteDimmerChanged(temp02.dimmer);
    //     ret = true;
    // }
    // if(temp02.polarity != mRemoteOperation0102.polarity)
    // {
    emit remotePolarityChanged(temp02.polarity);
    //     ret = true;
    // }
    // if(temp02.dimmerBrightness != mRemoteOperation0102.dimmerBrightness)
    // {
    emit remoteDimmerBrightnessChanged(temp02.dimmerBrightness);
    //     ret = true;
    // }

    // if(ret)
    // {
    memcpy(&mRemoteOperation0102, data.data(), data.size());
    // }
}

void Mid06Handler::parse04Data(const QByteArray &data)
{
    // bool ret = false;
    RemoteOperation0304 temp04;
    memcpy(&temp04, data.data(), data.size());

    // if(temp04.model != mRemoteOperation0304.model)
    // {
        emit remoteModelChanged(temp04.model);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mRemoteOperation0304, data.data(), data.size());
    // }
}

void Mid06Handler::parse06Data(const QByteArray &data)
{
    // uint8_t num = data.at(0);
    // QByteArray infos = data.mid(1, (data.size() - 1));
    QByteArray infos = data;
    uint8_t num = data.size() / 3;
    for(int index = 0; index < num; index++)
    {
        RemoteShortCutInfo info;
        memcpy(&info, infos.mid(index * sizeof(RemoteShortCutInfo), sizeof(RemoteShortCutInfo)), sizeof(RemoteShortCutInfo));
        int memoryId = (MEMORY_MAX > info.memory) ? info.memory : (info.memory - MEMORY_MAX);
        // if(mRemoteShortCuts[info.id].memory != info.memory)
        // {
        mRemoteShortCuts[info.id].type = info.type;
        mRemoteShortCuts[info.id].memory = memoryId;

        emit remoteShortCutChanged(info.id, info.type, memoryId);
        // }
    }
}

QByteArray Mid06Handler::send01Data(const RemoteOperation0102 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(RemoteOperation0102)));

    return frameData;
}

QByteArray Mid06Handler::send02Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid06Handler::send03Data(const RemoteOperation0304 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(RemoteOperation0304)));

    return frameData;
}

QByteArray Mid06Handler::send04Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid06Handler::send05Data(const RemoteOperation0506 &data)
{
    QByteArray frameData;
    frameData.append((char *)&data.num, sizeof(data.num));

    for(RemoteShortCutInfo info: data.infos) {
        frameData.append((char *)&info, sizeof(info));
    }

    return frameData;
}

QByteArray Mid06Handler::send06Data(uint8_t num)
{
    QByteArray frameData;
    frameData.append(num);

    return frameData;
}
