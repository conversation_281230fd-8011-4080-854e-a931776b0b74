<RCC>
    <qresource prefix="/qml">
        <file>page/framework/Speakers.qml</file>
        <file>page/framework/SpeakersItem.qml</file>
        <file>page/framework/MasterGain.qml</file>
        <file>page/framework/ComboBoxB.qml</file>
        <file>page/framework/ButtonA.qml</file>
        <file>page/framework/ButtonB.qml</file>
        <file>page/framework/ComboBoxA.qml</file>
        <file>page/framework/SpinBoxB.qml</file>
        <file>page/framework/SpinBoxA.qml</file>
        <file>page/framework/SliderB.qml</file>
        <file>page/framework/SliderA.qml</file>
        <file>page/framework/EqConfig.qml</file>
        <file>page/framework/MyCheckBox.qml</file>
        <file>page/framework/EqBandItem.qml</file>
        <file>page/framework/VerticalSlider.qml</file>
        <file>page/framework/EqSpinBox.qml</file>
        <file>page/subwindow/MixerSetting.qml</file>
        <file>page/subwindow/SubWindow.qml</file>
        <file>page/framework/MyTabButton.qml</file>
        <file>page/subwindow/MixerItem.qml</file>
        <file>page/subwindow/DelaySetting.qml</file>
        <file>page/framework/MyRadioButton.qml</file>
        <file>page/subwindow/DelaySpeakersItem.qml</file>
        <file>page/subwindow/DelayItem.qml</file>
        <file>page/subwindow/DeviceSetting.qml</file>
        <file>page/subwindow/Setting1.qml</file>
        <file>page/subwindow/Setting2.qml</file>
        <file>page/subwindow/Setting3.qml</file>
        <file>page/subwindow/MemorySettingItem.qml</file>
        <file>page/framework/CrossOver.qml</file>
        <file>page/subwindow/SourceLevel.qml</file>
        <file>page/framework/RcaAuxSpinBox.qml</file>
        <file>page/framework/EqComboBox.qml</file>
        <file>page/subwindow/Info.qml</file>
        <file>page/subwindow/EqLinkPopup.qml</file>
        <file>page/subwindow/MemoryCopy.qml</file>
        <file>page/subwindow/RemoteShortCutItem.qml</file>
        <file>page/framework/InitWindow.qml</file>
        <file>page/framework/ChannelList.qml</file>
        <file>page/subwindow/Warning.qml</file>
        <file>page/framework/MainLayout.qml</file>
        <file>page/framework/Curves.qml</file>
        <file>page/framework/ChannelItems.qml</file>
        <file>page/framework/ChannelBtn.qml</file>
        <file>page/framework/MyMenu.qml</file>
        <file>page/framework/CrossOverItem.qml</file>
    </qresource>
</RCC>
