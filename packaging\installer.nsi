; DSP Controller Installer Script
; 基本定义
!define PRODUCT_NAME "DSP Controller"
!define PRODUCT_VERSION "*******"
!define PRODUCT_PUBLISHER "Your Company Name"
!define PRODUCT_WEB_SITE "http://www.yourcompany.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\DSP_PC.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

SetCompressor lzma

; MUI 现代界面定义
!include "MUI2.nsh"

; MUI 预定义
!define MUI_ABORTWARNING
!define MUI_ICON "..\logo.ico"
!define MUI_UNICON "..\logo.ico"

; 许可协议按钮文本
!define MUI_LICENSEPAGE_BUTTON "我同意"
!define MUI_LICENSEPAGE_CHECKBOX_TEXT "我已阅读并同意软件许可协议中的条款"
!define MUI_LICENSEPAGE_CHECKBOX

; 欢迎页面
!insertmacro MUI_PAGE_WELCOME
; 许可协议页面
!insertmacro MUI_PAGE_LICENSE "License.txt"
; 安装目录选择页面
!insertmacro MUI_PAGE_DIRECTORY
; 安装过程页面
!insertmacro MUI_PAGE_INSTFILES
; 安装完成页面
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 安装界面包含的语言设置
!insertmacro MUI_LANGUAGE "SimpChinese"

; 安装包名称
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "..\DSP_Controller_Setup_${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES\DSP Controller"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

Section "MainSection" SEC01
    SetOutPath "$INSTDIR"
    SetOverwrite ifnewer
    
    ; 主程序文件
    File "..\release\DSP_PC.exe"
    
    ; Qt运行库
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Core.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Gui.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Widgets.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Quick.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6QuickWidgets.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Network.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6Qml.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\Qt6QmlModels.dll"
    
    ; MinGW运行库
    File "C:\Qt\6.5.2\mingw_64\bin\libgcc_s_seh-1.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\libstdc++-6.dll"
    File "C:\Qt\6.5.2\mingw_64\bin\libwinpthread-1.dll"
    
    ; 项目特定DLL
    File "..\communication\LibUsb\hidapi.dll"
    
    ; 复制许可证文件到安装目录
    File "License.txt"
    
    ; 创建开始菜单快捷方式
    CreateDirectory "$SMPROGRAMS\DSP Controller"
    CreateShortCut "$SMPROGRAMS\DSP Controller\DSP Controller.lnk" "$INSTDIR\DSP_PC.exe"
    CreateShortCut "$DESKTOP\DSP Controller.lnk" "$INSTDIR\DSP_PC.exe"
    
    ; 注册表
    WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\DSP_PC.exe"
    WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
    WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
    WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\DSP_PC.exe"
    WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
    WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
    
    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

; 卸载部分
Section Uninstall
    Delete "$INSTDIR\uninst.exe"
    Delete "$INSTDIR\DSP_PC.exe"
    Delete "$INSTDIR\Qt6Core.dll"
    Delete "$INSTDIR\Qt6Gui.dll"
    Delete "$INSTDIR\Qt6Widgets.dll"
    Delete "$INSTDIR\Qt6Quick.dll"
    Delete "$INSTDIR\Qt6QuickWidgets.dll"
    Delete "$INSTDIR\Qt6Network.dll"
    Delete "$INSTDIR\Qt6Qml.dll"
    Delete "$INSTDIR\Qt6QmlModels.dll"
    Delete "$INSTDIR\libgcc_s_seh-1.dll"
    Delete "$INSTDIR\libstdc++-6.dll"
    Delete "$INSTDIR\libwinpthread-1.dll"
    Delete "$INSTDIR\hidapi.dll"
    Delete "$INSTDIR\License.txt"
    
    Delete "$SMPROGRAMS\DSP Controller\DSP Controller.lnk"
    Delete "$DESKTOP\DSP Controller.lnk"
    RMDir "$SMPROGRAMS\DSP Controller"
    RMDir "$INSTDIR"
    
    DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
    DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
SectionEnd 