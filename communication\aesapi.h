#ifndef AESAPI_H
#define AESAPI_H

#include <QObject>


class AesApi : public QObject
{
    Q_OBJECT
public:
    explicit AesApi(QObject *parent = nullptr);
    void lsk_uuid(char uuid[], int len);
    int master_auth1(unsigned char output_str[32]);
    int master_auth2(unsigned char input_str[64], unsigned char output_str[64]);

    void setOutput_str1(unsigned char output_str1[32]);
    void setOutput_str2(unsigned char output_str1[64]);
    void setOutput_str3(unsigned char output_str1[64]);

    unsigned char *getMaster_random_1();
    unsigned char *getSlave_random_1();

    uint8_t crc8(const uint8_t *data, size_t length);

    int aesInit();
    void getAesInfo();
signals:

private:
    unsigned char moutput_str1[32] = {0};
    unsigned char moutput_str2[64] = {0};
    unsigned char moutput_str3[64] = {0};
    unsigned char master_random_1[16] = {0};
    unsigned char slave_random_1[16] = {0};
    uint8_t crc = 0x00; // 初始值，可以根据具体协议选择
};

#endif // AESAPI_H
