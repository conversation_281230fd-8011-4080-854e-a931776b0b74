import QtQuick
import QtQuick.Controls.Basic
import "../framework"

SubWindow {
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 480
    contentHeight: 360
    titleStr: qsTr("Source Level Adjuster")
    confirmVisible: false
    cancelStr: qsTr("Close")

    onClickCancel: clickClose()

    contentItem: Rectangle {
        id: root
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"

        property int uiHigh8Gain: high8Gain
        property int uiRcaGain: rcaGain
        property int uiUsbGain: usbGain
        property int uiBtGain: btGain
        property int uiSpdifGain: spdifGain
        // property int uiAuxGain: auxGain

        property int high8Gain: dataMap["highGain"] - 12
        property int rcaGain: dataMap["rcaGain"]
        property int usbGain: dataMap["usbGain"]
        property int btGain: dataMap["btGain"]
        property int spdifGain: dataMap["spdifGain"]
        // property int auxGain: dataMap["auxGain"]

        onUiHigh8GainChanged: {
            high8Input.value = uiHigh8Gain
            high8Slider.value = uiHigh8Gain

            if(uiHigh8Gain !== high8Gain)
            {
                commonCtrl.setHighGain(uiHigh8Gain + 12)
            }
        }

        onUiRcaGainChanged: {
            rcaInput.value = uiRcaGain
            rcaSlider.value = uiRcaGain

            if(uiRcaGain !== rcaGain)
            {
                commonCtrl.setRcaGain(uiRcaGain)
            }
        }

        onUiUsbGainChanged: {
            usbInput.value = uiUsbGain
            usbSlider.value = uiUsbGain

            if(uiUsbGain !== usbGain)
            {
                commonCtrl.setUsbGain(uiUsbGain)
            }
        }

        onUiBtGainChanged: {
            btInput.value = uiBtGain
            btSlider.value = uiBtGain

            if(uiBtGain !== btGain)
            {
                commonCtrl.setBtGain(uiBtGain)
            }
        }

        onUiSpdifGainChanged: {
            spdifInput.value = uiSpdifGain
            spdifSlider.value = uiSpdifGain

            if(uiSpdifGain !== spdifGain)
            {
                commonCtrl.setSpdifGain(uiSpdifGain)
            }
        }

        // onUiAuxGainChanged: {
        //     auxInput.value = uiAuxGain
        //     auxSlider.value = uiAuxGain

        //     if(uiAuxGain !== auxGain)
        //     {
        //         commonCtrl.setAuxGain(uiAuxGain)
        //     }
        // }

        onHigh8GainChanged: {
            if(uiHigh8Gain !== high8Gain)
            {
                uiHigh8Gain = high8Gain
            }
        }

        onRcaGainChanged: {
            if(uiRcaGain !== rcaGain)
            {
                uiRcaGain = rcaGain
            }
        }

        onUsbGainChanged: {
            if(uiUsbGain !== usbGain)
            {
                uiUsbGain = usbGain
            }
        }

        onBtGainChanged: {
            if(uiBtGain !== btGain)
            {
                uiBtGain = btGain
            }
        }

        onSpdifGainChanged: {
            if(uiSpdifGain !== spdifGain)
            {
                uiSpdifGain = spdifGain
            }
        }

        // onAuxGainChanged: {
        //     if(uiAuxGain !== auxGain)
        //     {
        //         uiAuxGain = auxGain
        //     }
        // }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 60
            anchors.top: parent.top
            anchors.topMargin: 40
            spacing: 16

            Column {
                id: mainUnitColumn
                spacing: 16

                Item {
                    width: 342
                    height: 24

                    Text {
                        id: high8Title
                        anchors.verticalCenter: parent.verticalCenter
                        width: 68
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "Speaker IN"
                    }

                    Item {
                        anchors.left: high8Title.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 274
                        height: 24

                        SpinBoxA {
                            id: high8Input
                            anchors.verticalCenter: parent.verticalCenter
                            width: 68

                            from: -12
                            to: 6

                            // textFromValue: function(value) {
                            //     return (value + 12).toString()
                            // }

                            // valueFromText: function(text) {
                            //     return Number(text) - 12
                            // }

                            // validator: RegularExpressionValidator { regularExpression: /(-?\d{1,2}?)(dB)?/ }
                            validator: RegularExpressionValidator { regularExpression: /-?(\d{0,2}?)?/ }
                            unit: "dB"

                            textFromValue: function(value, locale) {
                                // return Number(value / decimalFactor).toLocaleString(locale, 'f', decimals) + "dB"
                                // return value.toString() + "dB"
                                return value.toString()
                            }

                            valueFromText: function(text, locale) {
                                // let re = /(-?\d{1,2}?)(dB)?/
                                // return Number.fromLocaleString(locale, re.exec(text)[1]) * decimalFactor
                                return Number(text)
                            }

                            onValueChanged: {
                                root.uiHigh8Gain = value
                            }
                        }

                        ButtonA {
                            id: high8MinusBtn
                            anchors.left: high8Input.right
                            anchors.leftMargin: 20
                            anchors.verticalCenter: parent.verticalCenter
                            width: 24
                            height: 24
                            autoRepeat: true
                            autoRepeatInterval: 200

                            onClicked: {
                                high8Slider.decrease()
                            }

                            contentItem: Item{
                                Image {
                                    anchors.centerIn: parent
                                    source: "qrc:/Image/icn_btn_minus_bold.png"
                                }
                            }
                        }

                        SliderA {
                            id: high8Slider
                            anchors.left: high8MinusBtn.right
                            anchors.leftMargin: 3
                            anchors.verticalCenter: parent.verticalCenter
                            width: 132
                            height: 24

                            from: high8Input.from
                            to: high8Input.to
                            value: root.uiHigh8Gain
                            stepSize: 1
                            snapMode: Slider.SnapAlways

                            onValueChanged: {
                                root.uiHigh8Gain = Math.round(value)
                            }
                        }

                        ButtonA {
                            id: high8PlusBtn
                            anchors.left: high8Slider.right
                            anchors.leftMargin: 3
                            anchors.verticalCenter: parent.verticalCenter
                            width: 24
                            height: 24
                            autoRepeat: true
                            autoRepeatInterval: 200

                            onClicked: {
                                high8Slider.increase()
                            }

                            contentItem: Item{
                                Image {
                                    anchors.centerIn: parent
                                    source: "qrc:/Image/icn_btn_plus_bold.png"
                                }
                            }
                        }
                    }
                }

                Item {
                    width: 342
                    height: 24

                    Text {
                        id: rcaTitle
                        anchors.verticalCenter: parent.verticalCenter
                        width: 68
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "RCA IN"
                    }

                    Item {
                        anchors.left: rcaTitle.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 274
                        height: 24

                        RcaAuxSpinBox {
                            id: rcaInput
                            anchors.verticalCenter: parent.verticalCenter
                            width: 68

                            onValueChanged: {
                                root.uiRcaGain = value
                            }
                        }

                        ButtonA {
                            id: rcaMinusBtn
                            anchors.left: rcaInput.right
                            anchors.leftMargin: 20
                            anchors.verticalCenter: parent.verticalCenter
                            width: 24
                            height: 24
                            autoRepeat: true
                            autoRepeatInterval: 200

                            onClicked: {
                                rcaSlider.decrease()
                            }

                            contentItem: Item{
                                Image {
                                    anchors.centerIn: parent
                                    source: "qrc:/Image/icn_btn_minus_bold.png"
                                }
                            }
                        }

                        SliderA {
                            id: rcaSlider
                            anchors.left: rcaMinusBtn.right
                            anchors.leftMargin: 3
                            anchors.verticalCenter: parent.verticalCenter
                            width: 132
                            height: 24

                            from: rcaInput.from
                            to: rcaInput.to
                            value: root.uiRcaGain
                            stepSize: 1
                            snapMode: Slider.SnapAlways

                            onValueChanged: {
                                root.uiRcaGain = Math.round(value)
                            }
                        }

                        ButtonA {
                            id: rcaPlusBtn
                            anchors.left: rcaSlider.right
                            anchors.leftMargin: 3
                            anchors.verticalCenter: parent.verticalCenter
                            width: 24
                            height: 24
                            autoRepeat: true
                            autoRepeatInterval: 200

                            onClicked: {
                                rcaSlider.increase()
                            }

                            contentItem: Item{
                                Image {
                                    anchors.centerIn: parent
                                    source: "qrc:/Image/icn_btn_plus_bold.png"
                                }
                            }
                        }
                    }
                }
            }

            Rectangle {
                id: separator
                width: 342
                height: 1
                color: "#747880"
            }

            Column {
                id: dspColumn
                spacing: 16

                Item {
                    width: 342
                    height: 24
                    visible: (1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true

                    Text {
                        id: usbAudioTitle
                        anchors.verticalCenter: parent.verticalCenter
                        width: 96
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "USB AUDIO"
                    }

                    SpinBoxA {
                        id: usbInput
                        anchors.left: usbAudioTitle.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 40

                        from: 0
                        to: 100

                        validator: RegularExpressionValidator { regularExpression: /(\d{0,3})?/ }

                        textFromValue: function(value) {
                            return value.toString()
                        }

                        valueFromText: function(text) {
                            return Number(text)
                        }

                        onValueChanged: {
                            root.uiUsbGain = value
                        }
                    }

                    ButtonA {
                        id: usbMinusBtn
                        anchors.left: usbInput.right
                        anchors.leftMargin: 20
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            usbSlider.decrease()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_minus_bold.png"
                            }
                        }
                    }

                    SliderA {
                        id: usbSlider
                        anchors.left: usbMinusBtn.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 132
                        height: 24

                        from: usbInput.from
                        to: usbInput.to
                        value: root.uiUsbGain
                        stepSize: 1
                        snapMode: Slider.SnapAlways

                        onValueChanged: {
                            root.uiUsbGain = Math.round(value)
                        }
                    }

                    ButtonA {
                        id: usbPlusBtn
                        anchors.left: usbSlider.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            usbSlider.increase()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_plus_bold.png"
                            }
                        }
                    }
                }

                Item {
                    width: 342
                    height: 24

                    Text {
                        id: btAudioTitle
                        anchors.verticalCenter: parent.verticalCenter
                        width: 96
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "BT AUDIO"
                    }

                    SpinBoxA {
                        id: btInput
                        anchors.left: btAudioTitle.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 40

                        from: 0
                        to: 100

                        validator: RegularExpressionValidator { regularExpression: /(\d{0,3})?/ }

                        textFromValue: function(value) {
                            return value.toString()
                        }

                        valueFromText: function(text) {
                            return Number(text)
                        }

                        onValueChanged: {
                            root.uiBtGain = value
                        }
                    }

                    ButtonA {
                        id: btMinusBtn
                        anchors.left: btInput.right
                        anchors.leftMargin: 20
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            btSlider.decrease()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_minus_bold.png"
                            }
                        }
                    }

                    SliderA {
                        id: btSlider
                        anchors.left: btMinusBtn.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 132
                        height: 24

                        from: btInput.from
                        to: btInput.to
                        value: root.uiBtGain
                        stepSize: 1
                        snapMode: Slider.SnapAlways

                        onValueChanged: {
                            root.uiBtGain = Math.round(value)
                        }
                    }

                    ButtonA {
                        id: btPlusBtn
                        anchors.left: btSlider.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            btSlider.increase()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_plus_bold.png"
                            }
                        }
                    }
                }

                Item {
                    width: 342
                    height: 24
                    visible: (1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true

                    Text {
                        id: spdifTitle
                        anchors.verticalCenter: parent.verticalCenter
                        width: 96
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "SPDIF"
                    }

                    SpinBoxA {
                        id: spdifInput
                        anchors.left: spdifTitle.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 40

                        from: 0
                        to: 100

                        validator: RegularExpressionValidator { regularExpression: /(\d{0,3})?/ }

                        textFromValue: function(value) {
                            return value.toString()
                        }

                        valueFromText: function(text) {
                            return Number(text)
                        }

                        onValueChanged: {
                            root.uiSpdifGain = value
                        }
                    }

                    ButtonA {
                        id: spdifMinusBtn
                        anchors.left: spdifInput.right
                        anchors.leftMargin: 20
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            spdifSlider.decrease()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_minus_bold.png"
                            }
                        }
                    }

                    SliderA {
                        id: spdifSlider
                        anchors.left: spdifMinusBtn.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 132
                        height: 24

                        from: spdifInput.from
                        to: spdifInput.to
                        value: root.uiSpdifGain
                        stepSize: 1
                        snapMode: Slider.SnapAlways

                        onValueChanged: {
                            root.uiSpdifGain = Math.round(value)
                        }
                    }

                    ButtonA {
                        id: spdifPlusBtn
                        anchors.left: spdifSlider.right
                        anchors.leftMargin: 3
                        anchors.verticalCenter: parent.verticalCenter
                        width: 24
                        height: 24
                        autoRepeat: true

                        onClicked: {
                            spdifSlider.increase()
                        }

                        contentItem: Item{
                            Image {
                                anchors.centerIn: parent
                                source: "qrc:/Image/icn_btn_plus_bold.png"
                            }
                        }
                    }
                }
            }

            // Item {
            //     width: 342
            //     height: 24

            //     Text {
            //         id: auxTitle
            //         anchors.verticalCenter: parent.verticalCenter
            //         width: 96
            //         height: 8
            //         color: "#E8E8E8"
            //         font.family: "Segoe UI"
            //         font.pixelSize: 12
            //         verticalAlignment: Text.AlignVCenter
            //         text: "AUX"
            //     }

            //     RcaAuxSpinBox {
            //         id: auxInput
            //         anchors.left: auxTitle.right
            //         anchors.verticalCenter: parent.verticalCenter
            //         width: 40

            //         onValueChanged: {
            //             root.uiAuxGain = value
            //         }
            //     }

            //     ButtonA {
            //         id: auxMinusBtn
            //         anchors.left: auxInput.right
            //         anchors.leftMargin: 20
            //         anchors.verticalCenter: parent.verticalCenter
            //         width: 24
            //         height: 24

            //         onClicked: {
            //             auxSlider.decrease()
            //         }

            //         contentItem: Item{
            //             Image {
            //                 anchors.centerIn: parent
            //                 source: "qrc:/Image/icn_btn_minus_bold.png"
            //             }
            //         }
            //     }

            //     SliderA {
            //         id: auxSlider
            //         anchors.left: auxMinusBtn.right
            //         anchors.leftMargin: 3
            //         anchors.verticalCenter: parent.verticalCenter
            //         width: 132
            //         height: 24

            //         from: auxInput.from
            //         to: auxInput.to
            //         value: root.uiAuxGain
            //         stepSize: 1
            //         snapMode: Slider.SnapAlways

            //         onValueChanged: {
            //             root.uiAuxGain = Math.round(value)
            //         }
            //     }

            //     ButtonA {
            //         id: auxPlusBtn
            //         anchors.left: auxSlider.right
            //         anchors.leftMargin: 3
            //         anchors.verticalCenter: parent.verticalCenter
            //         width: 24
            //         height: 24

            //         onClicked: {
            //             auxSlider.increase()
            //         }

            //         contentItem: Item{
            //             Image {
            //                 anchors.centerIn: parent
            //                 source: "qrc:/Image/icn_btn_plus_bold.png"
            //             }
            //         }
            //     }
            // }
        }
    }
}
