@echo off
echo Creating Qt Installer Framework standard translation files...

REM Create translations directory
if not exist "translations" mkdir "translations"

REM Delete old files
del /f /q "translations\*.ts" 2>nul
del /f /q "translations\*.qm" 2>nul

REM Use PowerShell to create English translation file
echo Creating en_US.ts for English translations...
powershell -Command ^
"@'^
'<?xml version=\"1.0\" encoding=\"utf-8\"?>^
<!DOCTYPE TS>^
<TS version=\"2.1\" language=\"en_US\">^
<context>^
    <name>QInstaller::ComponentSelectionPage</name>^
    <message>^
        <source>Select Components</source>^
        <translation>Select Components</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::IntroductionPage</name>^
    <message>^
        <source>Welcome</source>^
        <translation>Welcome</translation>^
    </message>^
    <message>^
        <source>Welcome to the %1 Setup Wizard.</source>^
        <translation>Welcome to the %1 Setup Wizard.</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::TargetDirectoryPage</name>^
    <message>^
        <source>Installation Folder</source>^
        <translation>Installation Folder</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::StartMenuDirectoryPage</name>^
    <message>^
        <source>Start Menu shortcuts</source>^
        <translation>Start Menu shortcuts</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::ReadyForInstallationPage</name>^
    <message>^
        <source>Ready to Install</source>^
        <translation>Ready to Install</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::PerformInstallationPage</name>^
    <message>^
        <source>Installing</source>^
        <translation>Installing</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::FinishedPage</name>^
    <message>^
        <source>Completing the %1 Setup Wizard</source>^
        <translation>Completing the %1 Setup Wizard</translation>^
    </message>^
    <message>^
        <source>Finished</source>^
        <translation>Finished</translation>^
    </message>^
</context>^
</TS>^
'@ | Out-File -FilePath 'translations\en_US.ts' -Encoding UTF8"

REM Use PowerShell to create Japanese translation file
echo Creating ja_JP.ts for Japanese translations...
powershell -Command ^
"@'^
'<?xml version=\"1.0\" encoding=\"utf-8\"?>^
<!DOCTYPE TS>^
<TS version=\"2.1\" language=\"ja_JP\">^
<context>^
    <name>QInstaller::ComponentSelectionPage</name>^
    <message>^
        <source>Select Components</source>^
        <translation>コンポーネントの選択</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::IntroductionPage</name>^
    <message>^
        <source>Welcome</source>^
        <translation>ようこそ</translation>^
    </message>^
    <message>^
        <source>Welcome to the %1 Setup Wizard.</source>^
        <translation>%1 セットアップウィザードへようこそ。</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::TargetDirectoryPage</name>^
    <message>^
        <source>Installation Folder</source>^
        <translation>インストール先フォルダー</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::StartMenuDirectoryPage</name>^
    <message>^
        <source>Start Menu shortcuts</source>^
        <translation>スタートメニューのショートカット</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::ReadyForInstallationPage</name>^
    <message>^
        <source>Ready to Install</source>^
        <translation>インストールの準備完了</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::PerformInstallationPage</name>^
    <message>^
        <source>Installing</source>^
        <translation>インストール中</translation>^
    </message>^
</context>^
<context>^
    <name>QInstaller::FinishedPage</name>^
    <message>^
        <source>Completing the %1 Setup Wizard</source>^
        <translation>%1 セットアップウィザードの完了</translation>^
    </message>^
    <message>^
        <source>Finished</source>^
        <translation>完了</translation>^
    </message>^
</context>^
</TS>^
'@ | Out-File -FilePath 'translations\ja_JP.ts' -Encoding UTF8"

REM Compile translation files
echo Converting to .qm files...
lrelease translations\en_US.ts
if errorlevel 1 (
    echo [ERROR] Failed to compile en_US.ts
    exit /b 1
)

lrelease translations\ja_JP.ts
if errorlevel 1 (
    echo [ERROR] Failed to compile ja_JP.ts
    exit /b 1
)

echo.
echo Qt Installer Framework translation files created successfully!
echo Files created:
echo - translations\en_US.ts (English source)
echo - translations\en_US.qm (English binary)
echo - translations\ja_JP.ts (Japanese source)
echo - translations\ja_JP.qm (Japanese binary)

REM Check if running in automatic mode (via environment variable or parameter)
if "%1"=="auto" (
    echo [INFO] Running in automatic mode, skipping pause
) else (
    pause
)