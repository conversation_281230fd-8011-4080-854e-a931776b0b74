<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChannelItem</class>
 <widget class="QFrame" name="ChannelItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>162</width>
    <height>130</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="styleSheet">
   <string notr="true">font-family: &quot;Segoe UI&quot;;
font-size: 12px;
color: #E8E8E8;

QMenu {
	background-color: #383744;
	color: #FFFFFF;
	padding: 10px;
}
QMenu::item:selected {
	background-color: #80FFFFFF;
}
QMenu::separator {
	height: 1px;
	background-color: #D9D9D9;
	margin-left: 10px;
	margin-right: 10px;
}</string>
  </property>
  <widget class="QLabel" name="channelTitle">
   <property name="geometry">
    <rect>
     <x>8</x>
     <y>4</y>
     <width>40</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14px; font-weight:700;&quot;&gt;CH %1&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
   <property name="textFormat">
    <enum>Qt::TextFormat::RichText</enum>
   </property>
  </widget>
  <widget class="QLabel" name="highPass">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>112</x>
     <y>6</y>
     <width>16</width>
     <height>16</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
	image: url(:/Image/highPassOn.png);
}
QLabel::disabled {
	image: url(:/Image/highPassOff.png);
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLabel" name="lowPass">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>6</y>
     <width>16</width>
     <height>16</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
	image: url(:/Image/lowPassOn.png);
}
QLabel::disabled {
	image: url(:/Image/lowPassOff.png);
}</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="mute">
   <property name="geometry">
    <rect>
     <x>8</x>
     <y>32</y>
     <width>24</width>
     <height>24</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">#mute {
	image: url(:/Image/unmute.png);
	background-color: #5C6068;
	border: 1px solid #747880;
	border-radius: 2px;
}
#mute::checked {
	image: url(:/Image/mute.png);
}</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="sp">
   <property name="geometry">
    <rect>
     <x>36</x>
     <y>32</y>
     <width>118</width>
     <height>24</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton{
	background-color: #5C6068;
	border: 1px solid #747880;
	border-radius: 2px;
	padding-left: 6px;
	padding-right: 24px;
	font-size: 12px;
	color: #E8E8E8;
	text-align: left;
}
QPushButton::menu-indicator {
	width: 24;
	height: 24;
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}</string>
   </property>
   <property name="text">
    <string>---</string>
   </property>
  </widget>
  <widget class="QDoubleSpinBox" name="volumn">
   <property name="geometry">
    <rect>
     <x>8</x>
     <y>63</y>
     <width>63</width>
     <height>24</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QDoubleSpinBox {
	border: 1px solid #8E8E8E;
	border-radius: 2px;
	background-color: #000000;
	font-size: 12px;
	font-weight: 700;
	color: #FFFFFF;
}</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
   <property name="buttonSymbols">
    <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
   </property>
   <property name="suffix">
    <string>dB</string>
   </property>
   <property name="decimals">
    <number>1</number>
   </property>
   <property name="minimum">
    <double>-60.000000000000000</double>
   </property>
   <property name="maximum">
    <double>0.000000000000000</double>
   </property>
   <property name="singleStep">
    <double>0.100000000000000</double>
   </property>
  </widget>
  <widget class="QSlider" name="volumnSlider">
   <property name="geometry">
    <rect>
     <x>33</x>
     <y>101</y>
     <width>96</width>
     <height>18</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QSlider::groove {
    border: 1px solid #5C6068;
	border-radius: 2px;
    height: 4px;
	background-color: #181818;
}
QSlider::sub-page {
    height: 4px;
    border: 1px solid #5C6068;
	border-radius: 2px;
    background-color: #C8D8FF;
}
QSlider::handle {
	background-color: #A0A0A0;
    border: 0px;
    width: 18px;
	height: 18px;
	margin: -7px, -7px, 0px, 0px;
    border-radius: 9px;
}
QSlider::handle:pressed {
	background-color: #C0C0C0;
}</string>
   </property>
   <property name="minimum">
    <number>-600</number>
   </property>
   <property name="maximum">
    <number>0</number>
   </property>
   <property name="pageStep">
    <number>0</number>
   </property>
   <property name="orientation">
    <enum>Qt::Orientation::Horizontal</enum>
   </property>
  </widget>
  <widget class="QPushButton" name="volumnMinus">
   <property name="geometry">
    <rect>
     <x>8</x>
     <y>101</y>
     <width>17</width>
     <height>17</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">image: url(:/Image/icn_btn_minus.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
   </property>
   <property name="autoRepeat">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="volumnPlus">
   <property name="geometry">
    <rect>
     <x>137</x>
     <y>101</y>
     <width>17</width>
     <height>17</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">image: url(:/Image/icn_btn_plus.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
   </property>
   <property name="autoRepeat">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="link">
   <property name="geometry">
    <rect>
     <x>101</x>
     <y>63</y>
     <width>53</width>
     <height>24</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
	background-color: #484C54;
	border: 1px solid #8E8E8E;
	border-radius: 2px;
	font-size: 12px;
	color: #FFFFFF;
	background-image: url(:/Image/link.png);
	background-position: left center;
	background-repeat: no-repeat;
	image: url(:/Image/icn_chblk_normal_arrow.png);
}</string>
   </property>
  </widget>
  <widget class="QPushButton" name="phase">
   <property name="geometry">
    <rect>
     <x>74</x>
     <y>63</y>
     <width>24</width>
     <height>24</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QPushButton {
	background-color: #484C54;
	border: 1px solid #8E8E8E;
	border-radius: 2px;
	background-image: url(:/Image/phaseOn.png);
	background-position: center;
	background-repeat: no-repeat;
}
QPushButton::checked {
	background-image: url(:/Image/phaseOff.png);
}</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QWidget" name="maskItem" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>162</width>
     <height>130</height>
    </rect>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
