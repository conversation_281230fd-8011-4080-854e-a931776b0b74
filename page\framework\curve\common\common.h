#ifndef COMMON_H
#define COMMON_H

#include <stddef.h>
#ifdef __cplusplus
#include <complex>
typedef std::complex<double> double_complex;
#else
#include <complex.h>
typedef double _Complex double_complex;
#endif

// 每倍频程的步数
#define OCTAVE_STEPS 40
// 最大EQ滤波器数量
#define MAX_EQ_FILTERS 31
// 采样率
#define FS 96000.0
// 圆周率
#define PI 3.14159265358979323846
// 每阶斜率步进
#define SLOPE_STEP 12

// 用于标记未使用的参数，避免编译警告
#define _UNUSED_ __attribute__((unused))

// 日志输出宏，临时使用printf确保在Release模式下也能输出
#include <QDebug>
#include <cstdio>
#include <stdio.h>
// 临时使用printf来调试贝塞尔滤波器问题
#define CURVE_LOG(fmt, ...) printf(fmt, ##__VA_ARGS__)
// 原来的QDebug版本（在Release模式下会被禁用）
// #define CURVE_LOG(fmt, ...) QDebug(QtDebugMsg).nospace().noquote() << QString().asprintf(fmt, ##__VA_ARGS__)

#ifdef __cplusplus
extern "C" {
#endif

// 双二阶滤波器系数结构体
// b: 分子系数，a: 分母系数
// b1st, a1st: 一阶滤波器系数
typedef struct {
    double b[3];      // 二阶分子系数
    double a[3];      // 二阶分母系数
    double b1st[2];   // 一阶分子系数
    double a1st[2];   // 一阶分母系数
} BiquadCoeffs;

// 动态数组结构体，用于存储频率点
typedef struct {
    double* data;     // 数据指针
    size_t size;      // 数据长度
} DoubleArray;

// 通用滤波器类型枚举，与calc_iir_filter_coeff case值一一对应
typedef enum {
    FILTER_TYPE_PEQ = 0,              // PEQ
    FILTER_TYPE_LSF = 1,              // lsf（低通）
    FILTER_TYPE_HSF = 2,              // hsf（高通）
    FILTER_TYPE_BESSEL_LP = 3,        // bessel低通
    FILTER_TYPE_BESSEL_HP = 4,        // bessel高通
    FILTER_TYPE_BUTTERWORTH_LP = 5,   // Butterworth低通
    FILTER_TYPE_BUTTERWORTH_HP = 6,   // Butterworth高通
    FILTER_TYPE_LINKWITZ_RILEY_LP = 7,// 宁克-锐低通
    FILTER_TYPE_LINKWITZ_RILEY_HP = 8 // 宁克-锐高通
} FilterTypeUniversal;

// 通用滤波器参数结构体，适用于巴特沃斯、贝塞尔、宁克-锐
typedef struct {
    double fc;    // 频率
    FilterTypeUniversal type; // 滤波器类型（含高通/低通）
    int slope;    // 斜率（dB/oct）
} FilterParamsUniversal;

// 生成倍频程频率列表
// min_freq: 最小频率，max_freq: 最大频率
// 返回DoubleArray结构体，包含频率点
DoubleArray get_oct_freq_list(double min_freq, double max_freq);

// 计算单个双二阶滤波器在一组频率下的频率响应
// freq_list: 频率点数组
// fs: 采样率
// coeffs: 滤波器系数
// response: 输出复数响应
// order: 滤波器阶数（1或2）
void calc_single_biquad_resp(const DoubleArray* freq_list, double fs,
                            const BiquadCoeffs* coeffs,
                            double_complex* response, int order);

// 计算一阶滤波器系数
// pass_type: 1为低通，0为高通
// C: 预处理参数
// b, a: 输出系数
void complex_filter_1st_order_coeff(int pass_type, float C, double *b, double *a);

// 计算IIR滤波器系数
// type: 滤波器类型
// f0: 截止频率
// gain_db: 增益（dB）
// q_value: Q值
// coeff: 输出系数结构体
void calc_iir_filter_coeff(float fs, int type, float f0,
                          float gain_db, float q_value,
                          BiquadCoeffs *coeff);

// 组合型滤波器响应计算方法
// type: 滤波器类型
// fc: 截止频率
// slope_rate: 滤波器斜率
// freq_list: 频率点数组
// total_resp: 总响应（输入输出）
void complex_filter_combined_method(int type, double fc, int slope_rate,
                                   const DoubleArray* freq_list,
                                   double_complex* total_resp);

// 为datamodel获取滤波后数据点的通用接口
void process_filter_points(const FilterParamsUniversal* highpass, const FilterParamsUniversal* lowpass,
                          const DoubleArray* freq_list, double_complex* out_resp);

#ifdef __cplusplus
}
#endif

#endif // COMMON_H
