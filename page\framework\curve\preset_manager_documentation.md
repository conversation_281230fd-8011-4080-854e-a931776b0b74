# PresetManager 音效预设管理器文档

## 概述

`PresetManager` 是一个专门用于管理音效预设的 C++ 类，它将 `preset.txt` 文件中的预设数据结构化，并提供便捷的接口来查询和应用这些预设到 DataModel 的 32~36 号不可见点。

## 文件结构

```
page/framework/curve/
├── preset_manager.h              # 头文件，定义接口和数据结构
├── preset_manager.cpp            # 实现文件，包含所有功能实现
├── preset_usage_example.cpp      # 使用示例文件
└── preset_manager_documentation.md # 本文档文件
```

## 数据映射关系

### 预设文件数据格式
```
type      level    100Hz  315Hz  1250Hz  3150Hz  8000Hz
SuperBass Level5   15     -5     0       0       5
...
```

### 映射到不可见点
- **32号点**: 100Hz 频率点
- **33号点**: 315Hz 频率点
- **34号点**: 1250Hz 频率点
- **35号点**: 3150Hz 频率点
- **36号点**: 8000Hz 频率点

## 主要类和结构

### 1. PresetManager 类

主要的预设管理器类，继承自 QObject，支持信号槽机制。

#### 枚举类型

**PresetType (预设类型)**
```cpp
enum PresetType {
    SuperBass = 0,  // 超重低音
    Powerful = 1,   // 强劲
    Vocal = 2,      // 人声
    Natural = 3     // 自然
};
```

**PresetLevel (预设级别)**
```cpp
enum PresetLevel {
    Level1 = 1,     // 级别1 (最小效果)
    Level2 = 2,     // 级别2
    Level3 = 3,     // 级别3
    Level4 = 4,     // 级别4
    Level5 = 5      // 级别5 (最大效果)
};
```

#### 数据结构

**PresetData (预设数据)**
```cpp
struct PresetData {
    double gain100Hz;   // 100Hz增益值
    double gain315Hz;   // 315Hz增益值
    double gain1250Hz;  // 1250Hz增益值
    double gain3150Hz;  // 3150Hz增益值
    double gain8000Hz;  // 8000Hz增益值
};
```

## 主要接口函数

### 1. 数据查询接口

```cpp
// 获取指定类型和级别的预设数据
PresetData getPresetData(PresetType type, PresetLevel level) const;

// 获取所有可用的预设类型
static QVector<PresetType> getAllPresetTypes();

// 获取所有可用的预设级别
static QVector<PresetLevel> getAllPresetLevels();
```

### 2. 数据转换接口

```cpp
// 将预设数据转换为不可见点数据
QVector<DataModel::AdjustablePointData> convertToInvisiblePoints(const PresetData& presetData) const;
```

### 3. 预设应用接口

```cpp
// 应用预设到指定通道的32~36号不可见点
bool applyPreset(DataModel* dataModel, int curveIndex, PresetType type, PresetLevel level);
```

### 4. 字符串转换接口

```cpp
// 类型/级别枚举与字符串的相互转换
static QString getPresetTypeName(PresetType type);
static QString getPresetLevelName(PresetLevel level);
static PresetType getPresetTypeFromString(const QString& typeName);
static PresetLevel getPresetLevelFromString(const QString& levelName);
```

### 5. 信号

```cpp
// 预设应用成功信号
void presetApplied(int curveIndex, PresetType type, PresetLevel level);
```

## 使用方法

### 1. 基本使用流程

```cpp
// 1. 创建预设管理器
PresetManager* presetManager = new PresetManager();

// 2. 创建或获取数据模型
DataModel* dataModel = getDataModel();

// 3. 应用预设
bool success = presetManager->applyPreset(
    dataModel,                    // 数据模型
    0,                           // 通道索引 (0~13)
    PresetManager::SuperBass,    // 预设类型
    PresetManager::Level5        // 预设级别
);

// 4. 检查结果
if (success) {
    qDebug() << "预设应用成功";
} else {
    qDebug() << "预设应用失败";
}
```

### 2. 查询预设数据

```cpp
// 获取特定预设的数据
PresetManager::PresetData data = presetManager->getPresetData(
    PresetManager::Vocal, PresetManager::Level3);

qDebug() << "Vocal Level3 预设:";
qDebug() << "100Hz:" << data.gain100Hz << "dB";
qDebug() << "315Hz:" << data.gain315Hz << "dB";
qDebug() << "1250Hz:" << data.gain1250Hz << "dB";
qDebug() << "3150Hz:" << data.gain3150Hz << "dB";
qDebug() << "8000Hz:" << data.gain8000Hz << "dB";
```

### 3. 遍历所有预设

```cpp
QVector<PresetManager::PresetType> types = PresetManager::getAllPresetTypes();
QVector<PresetManager::PresetLevel> levels = PresetManager::getAllPresetLevels();

for (auto type : types) {
    QString typeName = PresetManager::getPresetTypeName(type);
    qDebug() << "预设类型:" << typeName;

    for (auto level : levels) {
        QString levelName = PresetManager::getPresetLevelName(level);
        PresetManager::PresetData data = presetManager->getPresetData(type, level);
        qDebug() << "  " << levelName << ": 增益值 ["
                 << data.gain100Hz << "," << data.gain315Hz << ","
                 << data.gain1250Hz << "," << data.gain3150Hz << ","
                 << data.gain8000Hz << "]";
    }
}
```

### 4. 信号连接

```cpp
// 连接预设应用成功信号
QObject::connect(presetManager, &PresetManager::presetApplied,
    [](int channel, PresetManager::PresetType type, PresetManager::PresetLevel level) {
        qDebug() << "预设应用成功: 通道" << channel
                 << ", 类型" << PresetManager::getPresetTypeName(type)
                 << ", 级别" << PresetManager::getPresetLevelName(level);
    });
```

## 预设数据详情

### SuperBass (超重低音)
专注于低频增强，适合重低音音乐。
- **特点**: 100Hz 显著增强，8000Hz 轻度增强
- **应用场景**: 电子音乐、Hip-Hop、重低音测试

| 级别 | 100Hz | 315Hz | 1250Hz | 3150Hz | 8000Hz |
|------|-------|-------|--------|--------|--------|
| Level1 | +3dB  | -1dB  | 0dB    | 0dB    | +1dB   |
| Level2 | +6dB  | -2dB  | 0dB    | 0dB    | +2dB   |
| Level3 | +9dB  | -3dB  | 0dB    | 0dB    | +3dB   |
| Level4 | +12dB | -4dB  | 0dB    | 0dB    | +4dB   |
| Level5 | +15dB | -5dB  | 0dB    | 0dB    | +5dB   |

### Powerful (强劲)
均衡的频率增强，营造强劲有力的音效。
- **特点**: 低频和高频双增强，中频轻度衰减
- **应用场景**: 摇滚音乐、金属音乐、动作电影

| 级别 | 100Hz | 315Hz | 1250Hz | 3150Hz | 8000Hz |
|------|-------|-------|--------|--------|--------|
| Level1 | +2dB  | -1dB  | +1dB   | -1dB   | +2dB   |
| Level2 | +4dB  | -1dB  | +1dB   | -1dB   | +4dB   |
| Level3 | +6dB  | -2dB  | +2dB   | -2dB   | +6dB   |
| Level4 | +8dB  | -2dB  | +2dB   | -2dB   | +8dB   |
| Level5 | +10dB | -3dB  | +3dB   | -3dB   | +10dB  |

### Vocal (人声)
专为人声优化，突出中高频区域。
- **特点**: 315Hz 和 8000Hz 显著增强，1250Hz 轻度增强
- **应用场景**: 流行音乐、人声、播客、语音通话

| 级别 | 100Hz | 315Hz | 1250Hz | 3150Hz | 8000Hz |
|------|-------|-------|--------|--------|--------|
| Level1 | 0dB   | +2dB  | +1dB   | 0dB    | +2dB   |
| Level2 | 0dB   | +3dB  | +1dB   | 0dB    | +3dB   |
| Level3 | 0dB   | +5dB  | +2dB   | 0dB    | +5dB   |
| Level4 | 0dB   | +6dB  | +2dB   | 0dB    | +6dB   |
| Level5 | 0dB   | +8dB  | +3dB   | 0dB    | +8dB   |

### Natural (自然)
轻度增强，保持自然音质。
- **特点**: 全频段轻度增强，保持平衡
- **应用场景**: 古典音乐、爵士乐、自然录音

| 级别 | 100Hz | 315Hz | 1250Hz | 3150Hz | 8000Hz |
|------|-------|-------|--------|--------|--------|
| Level1 | +1dB  | +1dB  | +1dB   | 0dB    | +1dB   |
| Level2 | +2dB  | +1dB  | +1dB   | 0dB    | +1dB   |
| Level3 | +3dB  | +2dB  | +2dB   | 0dB    | +2dB   |
| Level4 | +4dB  | +2dB  | +2dB   | 0dB    | +2dB   |
| Level5 | +5dB  | +3dB  | +3dB   | 0dB    | +3dB   |

## 技术细节

### 1. 频率映射
32~36号不可见点的频率设置为：
- 32号点: 100Hz (对应 x ≈ 2.41)
- 33号点: 315Hz (对应 x ≈ 6.89)
- 34号点: 1250Hz (对应 x ≈ 12.04)
- 35号点: 3150Hz (对应 x ≈ 17.19)
- 36号点: 8000Hz (对应 x ≈ 23.04)

### 2. 坐标转换公式
```cpp
// 频率到X坐标的转换
x = 30.0 * log10(frequency / 20.0) / log10(40000.0 / 20.0);

// 增益直接作为Y坐标
y = gain; // 单位: dB
```

### 3. 默认参数
- **Q值**: 4.32 (标准值)
- **EQ类型**: PEAK (峰值滤波器)
- **坐标范围**: x[0, 30], y[-10, 10]

## 错误处理

### 1. 常见错误类型
- **数据模型为空**: 传入 nullptr 的 DataModel 指针
- **预设不存在**: 查询不存在的类型/级别组合
- **通道索引越界**: curveIndex 超出有效范围 [0, 13]
- **不可见点数量不匹配**: 转换结果不是5个点

### 2. 错误处理策略
- 所有错误都会通过 qDebug() 输出详细的错误信息
- 查询不存在的预设会返回默认值（全0增益）
- 应用失败会返回 false，不会修改数据模型
- 字符串转换失败会使用默认值并输出警告

## 扩展功能

### 1. 添加新预设类型
1. 在 `PresetType` 枚举中添加新类型
2. 在 `initPresetData()` 中添加对应数据
3. 在 `getPresetTypeName()` 中添加名称映射
4. 在 `getPresetTypeFromString()` 中添加字符串转换

### 2. 添加新频率点
1. 修改 `INVISIBLE_POINTS_COUNT` 常量
2. 更新 `INVISIBLE_POINT_FREQUENCIES` 数组
3. 修改 `PresetData` 结构体，添加新的增益字段
4. 更新 `convertToInvisiblePoints()` 方法

### 3. 支持动态加载
可以扩展为从外部文件（JSON/XML）动态加载预设数据，而不是硬编码在源码中。

## 注意事项

1. **线程安全**: 当前实现不是线程安全的，如需多线程使用需要添加互斥锁
2. **内存管理**: 使用者需要负责 PresetManager 实例的生命周期管理
3. **数据一致性**: 修改预设数据后需要重新初始化 PresetManager
4. **兼容性**: 依赖 Qt 框架，确保项目中已正确配置 Qt 环境

## 完整示例

详细的使用示例请参考 `preset_usage_example.cpp` 文件，其中包含了所有主要功能的演示代码。