import QtQuick
import QtQuick.Controls.Basic

CheckBox {
    id: control
    checked: false
    spacing: 5
    font.family: "Segoe UI"
    font.pixelSize: 12
    opacity: enabled ? 1.0 : 0.3
    leftPadding: 0

    indicator: Rectangle {
        implicitWidth: 14
        implicitHeight: 14
        x: control.leftPadding
        y: parent.height / 2 - height / 2
        color: "#484C54"
        border.color: "#646870"
        border.width: 1

        Image {
            anchors.centerIn: parent
            source: "qrc:/Image/checked.png"
            visible: control.checked
        }
    }

    contentItem: Text {
        text: control.text
        font: control.font
        color: "#E8E8E8"
        verticalAlignment: Text.AlignVCenter
        leftPadding: control.indicator.width + control.spacing
    }
}
