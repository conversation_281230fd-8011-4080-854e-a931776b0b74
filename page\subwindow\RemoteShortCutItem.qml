import QtQuick
import "../framework"

Item {
    id: root
    width: 382
    height: 24

    property int shortCutId: 0

    property int m0Enabled: dataMap["memorySettingType0Index0"]["enable"]
    property int m1Enabled: dataMap["memorySettingType0Index1"]["enable"]
    property int m2Enabled: dataMap["memorySettingType0Index2"]["enable"]
    property int m3Enabled: dataMap["memorySettingType0Index3"]["enable"]
    property int m4Enabled: dataMap["memorySettingType0Index4"]["enable"]
    property int m5Enabled: dataMap["memorySettingType0Index5"]["enable"]

    property int s0Enabled: dataMap["memorySettingType1Index0"]["enable"]
    property int s1Enabled: dataMap["memorySettingType1Index1"]["enable"]
    property int s2Enabled: dataMap["memorySettingType1Index2"]["enable"]
    property int s3Enabled: dataMap["memorySettingType1Index3"]["enable"]
    property int s4Enabled: dataMap["memorySettingType1Index4"]["enable"]
    property int s5Enabled: dataMap["memorySettingType1Index5"]["enable"]

    property string m0Name: dataMap["memorySettingType0Index0"]["name"]
    property string m1Name: dataMap["memorySettingType0Index1"]["name"]
    property string m2Name: dataMap["memorySettingType0Index2"]["name"]
    property string m3Name: dataMap["memorySettingType0Index3"]["name"]
    property string m4Name: dataMap["memorySettingType0Index4"]["name"]
    property string m5Name: dataMap["memorySettingType0Index5"]["name"]

    property string s0Name: dataMap["memorySettingType1Index0"]["name"]
    property string s1Name: dataMap["memorySettingType1Index1"]["name"]
    property string s2Name: dataMap["memorySettingType1Index2"]["name"]
    property string s3Name: dataMap["memorySettingType1Index3"]["name"]
    property string s4Name: dataMap["memorySettingType1Index4"]["name"]
    property string s5Name: dataMap["memorySettingType1Index5"]["name"]

    property int shortCutType: dataMap["remoteShortCut" + shortCutId]["type"]
    property int shortCutMemory: dataMap["remoteShortCut" + shortCutId]["memory"]

    onM0EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(0, m0Enabled)
            if((0 === m0Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM1EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(1, m1Enabled)
            if((0 === m1Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM2EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(2, m2Enabled)
            if((0 === m2Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM3EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(3, m3Enabled)
            if((0 === m3Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM4EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(4, m4Enabled)
            if((0 === m4Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM5EnabledChanged: {
        if(0 !== shortCutId)
        {
            muMemoryComboBox.setItemEnabled(5, m5Enabled)
            if((0 === m5Enabled) && (1 === sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS0EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(0, s0Enabled)
            if((0 === s0Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS1EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(1, s1Enabled)
            if((0 === s1Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS2EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(2, s2Enabled)
            if((0 === s2Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS3EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(3, s3Enabled)
            if((0 === s3Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS4EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(4, s4Enabled)
            if((0 === s4Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onS5EnabledChanged: {
        if(0 !== shortCutId)
        {
            dspMemoryComboBox.setItemEnabled(5, s5Enabled)
            if((0 === s5Enabled) && (1 < sourceTypeToIndex(shortCutType)))
            {
                sourceComboBox.currentIndex = 0
            }
        }
    }

    onM0NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[0] = "M1:" + m0Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
        else
        {
            muMemoryComboBox.model[0] = "M1:" + m0Name
        }
    }

    onM1NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[1] = "M2:" + m1Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
    }

    onM2NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[2] = "M3:" + m2Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
    }

    onM3NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[3] = "M4:" + m3Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
    }

    onM4NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[4] = "M5:" + m4Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
    }

    onM5NameChanged: {
        if(0 !== shortCutId)
        {
            var tempIndex = muMemoryComboBox.currentIndex
            muMemoryComboBox.model[5] = "M6:" + m5Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))

            muMemoryComboBox.currentIndex = tempIndex
        }
    }

    onS0NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[0] = "S1:" + s0Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onS1NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[1] = "S2:" + s1Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onS2NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[2] = "S3:" + s2Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onS3NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[3] = "S4:" + s3Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onS4NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[4] = "S5:" + s4Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onS5NameChanged: {
        var tempIndex = dspMemoryComboBox.currentIndex
        dspMemoryComboBox.model[5] = "S6:" + s5Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        dspMemoryComboBox.currentIndex = tempIndex
    }

    onShortCutTypeChanged: {
        if(0 !== shortCutId)
        {
            switch(shortCutType)
            {
            case 1:     //speaker in
            case 2:     //rca
                sourceComboBox.currentIndex = 1
                muMemoryComboBox.currentIndex = shortCutMemory
                break
            case 3:     //aux
                sourceComboBox.currentIndex = 5
                dspMemoryComboBox.currentIndex = shortCutMemory
                break
            case 4:     //bt
                sourceComboBox.currentIndex = 3
                dspMemoryComboBox.currentIndex = shortCutMemory
                break
            case 5:     //spdif
                sourceComboBox.currentIndex = 4
                dspMemoryComboBox.currentIndex = shortCutMemory
                break
            case 6:     //usb
                sourceComboBox.currentIndex = 2
                dspMemoryComboBox.currentIndex = shortCutMemory
                break
            default:    //close
                sourceComboBox.currentIndex = 0
                break
            }
        }
    }

    onShortCutMemoryChanged: {
        if(0 !== shortCutId)
        {
            switch(shortCutType)
            {
            case 1:     //speaker in
            case 2:     //rca
                muMemoryComboBox.currentIndex = shortCutMemory
                break
            case 3:     //aux
            case 4:     //bt
            case 5:     //spdif
            case 6:     //usb
                dspMemoryComboBox.currentIndex = shortCutMemory
                break
            default:    //close
                sourceComboBox.currentIndex = 0
                break
            }
        }
    }

    Connections {
        target: commonCtrl
        function onDeviceConnection() {
            initItem()
        }
    }

    function sourceTypeToIndex(type) {
        var typeIndex = -1
        switch(type)
        {
        case 1:     //speaker in
        case 2:     //rca
            typeIndex = 1
            break
        case 3:     //aux
            typeIndex = 5
            break
        case 4:     //bt
            typeIndex = 3
            break
        case 5:     //spdif
            typeIndex = 4
            break
        case 6:     //usb
            typeIndex = 2
            break
        default:    //close
            typeIndex = 0
            break
        }
        return typeIndex
    }

    function sourceTypeFromIndex(index) {
        var type = 0
        switch(index)
        {
        case 1:     //mainUnitEnabledType: 1:speaker in; 2:rca
            type = (2 === dataMap["mainUnitEnabledType"]) ? 2 : 1
            break
        case 2:     //usb
            type = 6
            break
        case 3:     //bt
            type = 4
            break
        case 4:     //spdif
            type = 5
            break
        case 5:     //aux
            type = 3
            break
        default:    //close
            type = 0
            break
        }
        return type
    }

    function initItem() {
        // sourceComboBox.setItemEnabled(0, ((0 === shortCutId) ? false : true))
        sourceComboBox.setItemEnabled(2, (1 === sourceComboBox.usbEnabled))
        sourceComboBox.setItemEnabled(3, (1 === sourceComboBox.btEnabled))
        sourceComboBox.setItemEnabled(4, (1 === sourceComboBox.spdifEnabled))
        sourceComboBox.setItemEnabled(5, ((1 === sourceComboBox.mainUnitType)
                                          && (1 === sourceComboBox.auxEnabled)))

        if(0 !== shortCutId)
        {
            muMemoryComboBox.model[0] = "M1:" + m0Name
            muMemoryComboBox.model[1] = "M2:" + m1Name
            muMemoryComboBox.model[2] = "M3:" + m2Name
            muMemoryComboBox.model[3] = "M4:" + m3Name
            muMemoryComboBox.model[4] = "M5:" + m4Name
            muMemoryComboBox.model[5] = "M6:" + m5Name

            muMemoryComboBox.setItemEnabled(0, ((0 === m0Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(1, ((0 === m1Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(2, ((0 === m2Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(3, ((0 === m3Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(4, ((0 === m4Enabled) ? false : true))
            muMemoryComboBox.setItemEnabled(5, ((0 === m5Enabled) ? false : true))
        }

        dspMemoryComboBox.model[0] = "S1:" + s0Name
        dspMemoryComboBox.model[1] = "S2:" + s1Name
        dspMemoryComboBox.model[2] = "S3:" + s2Name
        dspMemoryComboBox.model[3] = "S4:" + s3Name
        dspMemoryComboBox.model[4] = "S5:" + s4Name
        dspMemoryComboBox.model[5] = "S6:" + s5Name

        dspMemoryComboBox.setItemEnabled(0, ((0 === s0Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(1, ((0 === s1Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(2, ((0 === s2Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(3, ((0 === s3Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(4, ((0 === s4Enabled) ? false : true))
        dspMemoryComboBox.setItemEnabled(5, ((0 === s5Enabled) ? false : true))

        var sourceBoxIndex = sourceTypeToIndex(shortCutType)
        sourceComboBox.currentIndex = (0 === shortCutId) ? 0 : sourceBoxIndex
        if(1 === sourceBoxIndex)
        {
            muMemoryComboBox.currentIndex = shortCutMemory
        }
        else if(0 !== sourceBoxIndex)
        {
            dspMemoryComboBox.currentIndex = shortCutMemory
        }
        else
        {

        }
    }

    Text {
        id: shortCutTitle
        anchors.verticalCenter: parent.verticalCenter
        width: 28
        height: 8
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: (shortCutId + 1)
    }

    ComboBoxA {
        id: sourceComboBox
        anchors.left: shortCutTitle.right
        anchors.verticalCenter: parent.verticalCenter
        width: 98
        height: 24
        model: (0 === shortCutId) ? ["Main unit"] : ["---", "Main unit", "USB AUDIO", "BT AUDIO", "SPDIF", "AUX"]

        property int mainUnitType: dataMap["mainUnitEnabledType"]
        property int usbEnabled: dataMap["usbEnabled"]
        property int btEnabled: dataMap["btEnabled"]
        property int spdifEnabled: dataMap["spdifEnabled"]
        property int auxEnabled: dataMap["auxEnabled"]

        onMainUnitTypeChanged: {
            if(0 !== shortCutId)
            {
                var enable = ((1 === mainUnitType) && (1 === auxEnabled))
                setItemEnabled(5, enable)
                if(!enable && (5 === sourceTypeToIndex(shortCutType)))
                {
                    currentIndex = 0
                }
            }
        }

        onUsbEnabledChanged: {
            if(0 !== shortCutId)
            {
                setItemEnabled(2, (1 === usbEnabled))
                if((0 === usbEnabled) && (2 === sourceTypeToIndex(shortCutType)))
                {
                    currentIndex = 0
                }
            }
        }

        onBtEnabledChanged: {
            if(0 !== shortCutId)
            {
                setItemEnabled(3, (1 === btEnabled))
                if((0 === btEnabled) && (3 === sourceTypeToIndex(shortCutType)))
                {
                    currentIndex = 0
                }
            }
        }

        onSpdifEnabledChanged: {
            if(0 !== shortCutId)
            {
                setItemEnabled(4, (1 === spdifEnabled))
                if((0 === spdifEnabled) && (4 === sourceTypeToIndex(shortCutType)))
                {
                    currentIndex = 0
                }
            }
        }

        onAuxEnabledChanged: {
            if(0 !== shortCutId)
            {
                var enable = ((1 === mainUnitType) && (1 === auxEnabled))
                setItemEnabled(5, enable)
                if(!enable && (5 === sourceTypeToIndex(shortCutType)))
                {
                    currentIndex = 0
                }
            }
        }

        onCurrentIndexChanged: {
            if(0 !== shortCutId)
            {
                var sourceBoxIndex = sourceTypeToIndex(shortCutType)
                if(sourceBoxIndex !== currentIndex)
                {
                    if(0 === currentIndex)
                    {
                        commonCtrl.setRemoteShortCut(shortCutId, sourceTypeFromIndex(currentIndex), 0xFF)
                    }
                    else if(1 === currentIndex)
                    {
                        commonCtrl.setRemoteShortCut(shortCutId, sourceTypeFromIndex(currentIndex), 0)
                    }
                    else
                    {
                        commonCtrl.setRemoteShortCut(shortCutId, sourceTypeFromIndex(currentIndex), 0)
                    }
                }
            }
        }
    }

    ComboBoxA {
        id: nullMemoryComboBox
        anchors.left: sourceComboBox.right
        anchors.leftMargin: 8
        anchors.verticalCenter: parent.verticalCenter
        width: 248
        height: 24
        model: []
        visible: ((0 !== shortCutId) && (0 === sourceComboBox.currentIndex))
    }

    ComboBoxA {
        id: muMemoryComboBox
        anchors.left: sourceComboBox.right
        anchors.leftMargin: 8
        anchors.verticalCenter: parent.verticalCenter
        width: 248
        height: 24
        model: (0 === shortCutId) ? ["M1"] : ["M1", "M2", "M3", "M4", "M5", "M6"]
        visible: (0 == shortCutId) || (1 === sourceComboBox.currentIndex)

        onCurrentIndexChanged: {
            if(0 !== shortCutId)
            {
                if((shortCutMemory !== currentIndex) && visible)
                {
                    commonCtrl.setRemoteShortCut(shortCutId, shortCutType, currentIndex)
                }
            }
        }
    }

    ComboBoxA {
        id: dspMemoryComboBox
        anchors.left: sourceComboBox.right
        anchors.leftMargin: 8
        anchors.verticalCenter: parent.verticalCenter
        width: 248
        height: 24
        model: ["S1", "S2", "S3", "S4", "S5", "S6"]
        visible: ((0 !== sourceComboBox.currentIndex) && (1 !== sourceComboBox.currentIndex))

        onCurrentIndexChanged: {
            if((shortCutMemory !== currentIndex) && visible)
            {
                commonCtrl.setRemoteShortCut(shortCutId, shortCutType, currentIndex)
            }
        }
    }
}
