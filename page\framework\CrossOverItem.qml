import QtQuick
import QtQuick.Controls.Basic

Item {
    id: root
    implicitWidth: 282
    implicitHeight: 134
    visible: channel === dataMap["uiDataMap"]["selectedChannel"]

    readonly property var colorB: ["#00B0E0", "#00C078", "#C800C8", "#7860E8", "#C08800",
        "#A0A000", "#0088FF", "#E860B0", "#009888", "#E04868"]

    property int channel: 0
    property alias uiHpEnable: hpEnableBtn.checked
    property alias uiLpEnable: lpEnableBtn.checked
    property alias uiHpType: hpTypeCombobox.currentIndex
    property alias uiLpType: lpTypeCombobox.currentIndex
    property alias uiHpFreq: hpFreqInput.value
    property alias uiLpFreq: lpFreqInput.value
    property alias uiHpSlope: hpSlopeCombobox.currentIndex
    property alias uiLpSlope: lpSlopeCombobox.currentIndex

    property bool hpEnable: dataMap["channel" + channel + "DataMap"]["hpEnable"]
    property bool lpEnable: dataMap["channel" + channel + "DataMap"]["lpEnable"]
    property int hpType: dataMap["channel" + channel + "DataMap"]["hpType"]
    property int lpType: dataMap["channel" + channel + "DataMap"]["lpType"]
    property int hpFreq: dataMap["channel" + channel + "DataMap"]["hpFreq"]
    property int lpFreq: dataMap["channel" + channel + "DataMap"]["lpFreq"]
    property int hpSlope: dataMap["channel" + channel + "DataMap"]["hpSlope"] - 1
    property int lpSlope: dataMap["channel" + channel + "DataMap"]["lpSlope"] - 1

    onUiHpEnableChanged: {
        if(uiHpEnable !== hpEnable)
        {
            commonCtrl.setOutputChannelHpEnable(channel, uiHpEnable)
        }
    }

    onUiLpEnableChanged: {
        if(uiLpEnable !== lpEnable)
        {
            commonCtrl.setOutputChannelLpEnable(channel, uiLpEnable)
        }
    }

    onUiHpTypeChanged: {
        if(uiHpType !== hpType)
        {
            commonCtrl.setOutputChannelHpType(channel, uiHpType)
        }
    }

    onUiLpTypeChanged: {
        if(uiLpType !== lpType)
        {
            commonCtrl.setOutputChannelLpType(channel, uiLpType)
        }
    }

    onUiHpFreqChanged: {
        if(uiHpFreq !== hpFreq)
        {
            commonCtrl.setOutputChannelHpFreq(channel, uiHpFreq)
        }
    }

    onUiLpFreqChanged: {
        if(uiLpFreq !== lpFreq)
        {
            commonCtrl.setOutputChannelLpFreq(channel, uiLpFreq)
        }
    }

    onUiHpSlopeChanged: {
        if(uiHpSlope !== hpSlope)
        {
            commonCtrl.setOutputChannelHpSlope(channel, (uiHpSlope + 1))
        }
    }

    onUiLpSlopeChanged: {
        if(uiLpSlope !== lpSlope)
        {
            commonCtrl.setOutputChannelLpSlope(channel, (uiLpSlope + 1))
        }
    }

    onHpEnableChanged: {
        if(uiHpEnable !== hpEnable)
        {
            uiHpEnable = hpEnable
        }
    }

    onLpEnableChanged: {
        if(uiLpEnable !== lpEnable)
        {
            uiLpEnable = lpEnable
        }
    }

    onHpTypeChanged: {
        if(uiHpType !== hpType)
        {
            uiHpType = hpType
        }
    }

    onLpTypeChanged: {
        if(uiLpType !== lpType)
        {
            uiLpType = lpType
        }
    }

    onHpFreqChanged: {
        if(uiHpFreq !== hpFreq)
        {
            uiHpFreq = hpFreq
        }
    }

    onLpFreqChanged: {
        if(uiLpFreq !== lpFreq)
        {
            uiLpFreq = lpFreq
        }
    }

    onHpSlopeChanged: {
        if(uiHpSlope !== hpSlope)
        {
            uiHpSlope = hpSlope
        }
    }

    onLpSlopeChanged: {
        if(uiLpSlope !== lpSlope)
        {
            uiLpSlope = lpSlope
        }
    }

    Text {
        id: title
        x: 8
        anchors.verticalCenter: subtitle.verticalCenter
        width: 66
        height: 8
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "Crossover"
    }

    Rectangle {
        id: subtitle
        anchors.left: title.right
        y: 6
        width: 36
        height: 14
        color: colorB[channel]

        Row{
            anchors.left: parent.left
            anchors.leftMargin: 4
            anchors.verticalCenter: parent.verticalCenter

            Text {
                anchors.bottom: titleNum.bottom
                width: 14
                height: 8
                color: "#FFFFFF"
                font.family: "Segoe UI"
                font.pixelSize: 10
                verticalAlignment: Text.AlignVCenter
                text: "CH"
            }

            Text {
                id: titleNum
                anchors.verticalCenter: parent.verticalCenter
                width: 14
                height: 10
                color: "#FFFFFF"
                font.family: "Segoe UI"
                font.pixelSize: 12
                font.bold: true
                verticalAlignment: Text.AlignVCenter
                text: channel + 1
            }
        }
    }

    MyCheckBox {
        id: hpEnableBtn
        x: 66
        anchors.top: subtitle.bottom
        anchors.topMargin: 5
        width: 14
        height: 14
        spacing: 4
        checked: false

        contentItem: Item {
            Row {
                anchors.left: parent.left
                anchors.leftMargin: hpEnableBtn.indicator.width + hpEnableBtn.spacing
                anchors.verticalCenter: parent.verticalCenter
                spacing: 3

                Image {
                    anchors.verticalCenter: parent.verticalCenter
                    source: "qrc:/Image/crossHighPass.png"
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    height: 8
                    color: "#FFFFFF"
                    font.family: "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "HPF"
                }
            }
        }
    }

    MyCheckBox {
        id: lpEnableBtn
        x: 172
        anchors.top: hpEnableBtn.top
        width: 14
        height: 14
        spacing: 4
        checked: false

        contentItem: Item {
            Row {
                anchors.left: parent.left
                anchors.leftMargin: hpEnableBtn.indicator.width + hpEnableBtn.spacing
                anchors.verticalCenter: parent.verticalCenter
                spacing: 3

                Image {
                    anchors.verticalCenter: parent.verticalCenter
                    source: "qrc:/Image/crossLowPass.png"
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    height: 8
                    color: "#FFFFFF"
                    font.family: "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "LPF"
                }
            }
        }
    }

    Column {
        anchors.left: parent.left
        anchors.leftMargin: 24
        anchors.top: parent.top
        anchors.topMargin: 53
        spacing: 20

        Text {
            width: 42
            height: 8
            color: "#E8E8E8"
            font.family: "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: "Type"
        }

        Text {
            width: 42
            height: 8
            color: "#E8E8E8"
            font.family: "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: "Freq"
        }

        Text {
            width: 42
            height: 8
            color: "#E8E8E8"
            font.family: "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: "Slope"
        }
    }

    Row {
        anchors.left: hpEnableBtn.left
        anchors.top: hpEnableBtn.bottom
        anchors.topMargin: 6
        spacing: 8

        Column {
            spacing: 4

            ComboBoxB {
                id: hpTypeCombobox
                width: 98
                height: 24
                model: ["Butter-W", "Bessel", "Link-Ril"]
            }

            SpinBoxB {
                id: hpFreqInput
                width: 98
                height: 24
                horizontalAlignment: Qt.AlignRight

                from: 20
                to: (lpFreqInput.value > 40000) ? 40000 : lpFreqInput.value

                // validator: RegularExpressionValidator { regularExpression: /(\d{1,5})(Hz)?/ }
                validator: RegularExpressionValidator { regularExpression: /(\d{0,5})?/ }
                unit: "Hz"

                textFromValue: function(value, locale) {
                    // return Number(value).toLocaleString(locale, 'f', 0) + "Hz"
                    // return value.toString() + "Hz"
                    return value.toString()
                }

                valueFromText: function(text, locale) {
                    // let re = /(\d{1,5})(Hz)?/
                    // return Number.fromLocaleString(locale, re.exec(text)[1])
                    // return Number(re.exec(text)[1])
                    return Number(text)
                }
            }

            ComboBoxB {
                id: hpSlopeCombobox
                width: 98
                height: 24
                model: ["-6 dB/Oct", "-12 dB/Oct", "-18 dB/Oct", "-24 dB/Oct", "-30 dB/Oct", "-36 dB/Oct", "-42 dB/Oct", "-48 dB/Oct"]
                currentIndex: 1
                visible: !hpLrSlopeCombobox.visible

                onCurrentIndexChanged: {
                    switch(currentIndex)
                    {
                    case 1:
                    case 3:
                    case 5:
                    case 7:
                        hpLrSlopeCombobox.currentIndex = (currentIndex - 1) / 2
                        break
                    default:
                        hpLrSlopeCombobox.currentIndex = -1
                        break
                    }
                }
            }

            ComboBoxB {
                id: hpLrSlopeCombobox
                width: 98
                height: 24
                model: ["-12 dB/Oct", "-24 dB/Oct", "-36 dB/Oct", "-48 dB/Oct"]
                visible: (2 === hpTypeCombobox.currentIndex)

                onCurrentIndexChanged: {
                    if(-1 !== currentIndex)
                    {
                        hpSlopeCombobox.currentIndex = currentIndex * 2 + 1
                    }
                }
            }
        }

        Column {
            spacing: 4

            ComboBoxB {
                id: lpTypeCombobox
                width: 98
                height: 24
                model: ["Butter-W", "Bessel", "Link-Ril"]
            }

            SpinBoxB {
                id: lpFreqInput
                width: 98
                height: 24
                horizontalAlignment: Qt.AlignRight

                from: (hpFreqInput.value < 20) ? 20 : hpFreqInput.value
                to: 40000

                // validator: RegularExpressionValidator { regularExpression: /(\d{1,5})(Hz)?/ }
                validator: RegularExpressionValidator { regularExpression: /(\d{0,5})?/ }
                unit: "Hz"

                textFromValue: function(value, locale) {
                    // return Number(value).toLocaleString(locale, 'f', 0) + "Hz"
                    // return value.toString() + "Hz"
                    return value.toString()
                }

                valueFromText: function(text, locale) {
                    // let re = /(\d{1,5})(Hz)?/
                    // return Number.fromLocaleString(locale, re.exec(text)[1])
                    // return Number(re.exec(text)[1])
                    return Number(text)
                }
            }

            ComboBoxB {
                id: lpSlopeCombobox
                width: 98
                height: 24
                model: ["-6 dB/Oct", "-12 dB/Oct", "-18 dB/Oct", "-24 dB/Oct", "-30 dB/Oct", "-36 dB/Oct", "-42 dB/Oct", "-48 dB/Oct"]
                currentIndex: 1
                visible: !lpLrSlopeCombobox.visible

                onCurrentIndexChanged: {
                    switch(currentIndex)
                    {
                    case 1:
                    case 3:
                    case 5:
                    case 7:
                        lpLrSlopeCombobox.currentIndex = (currentIndex - 1) / 2
                        break
                    default:
                        lpLrSlopeCombobox.currentIndex = -1
                        break
                    }
                }
            }

            ComboBoxB {
                id: lpLrSlopeCombobox
                width: 98
                height: 24
                model: ["-12 dB/Oct", "-24 dB/Oct", "-36 dB/Oct", "-48 dB/Oct"]
                visible: (2 === lpTypeCombobox.currentIndex)

                onCurrentIndexChanged: {
                    if(-1 !== currentIndex)
                    {
                        lpSlopeCombobox.currentIndex = currentIndex * 2 + 1
                    }
                }
            }
        }
    }
}
