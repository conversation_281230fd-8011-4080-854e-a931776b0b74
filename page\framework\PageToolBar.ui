<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PageToolBar</class>
 <widget class="QFrame" name="PageToolBar">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>46</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1280</width>
    <height>46</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#PageToolBar {
	background-color: #50545C;
	font-family: &quot;Segoe UI&quot;;
}

QLabel {
	font-size: 12px;
	color: #E8E8E8;
}

Line {
	color: #8E8E8E;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="musicSourceTitle">
     <property name="minimumSize">
      <size>
       <width>88</width>
       <height>16</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>88</width>
       <height>16</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
       <pointsize>-1</pointsize>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>Music Source</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="musicSource">
     <property name="minimumSize">
      <size>
       <width>110</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>110</width>
       <height>24</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: #5C6068;
	border: 1px solid #747880;
	padding-left: 6px;
	padding-right: 24px;
	font-size: 12px;
	color: #E8E8E8;
	text-align: left;
}
QPushButton::menu-indicator {
	width: 24px;
	height: 24px;
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>32</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="memoryTitle">
     <property name="minimumSize">
      <size>
       <width>58</width>
       <height>16</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>58</width>
       <height>16</height>
      </size>
     </property>
     <property name="text">
      <string>Memory</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="muMemoryList">
     <property name="minimumSize">
      <size>
       <width>250</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>250</width>
       <height>24</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: #5C6068;
	border: 1px solid #747880;
	padding-left: 6px;
	padding-right: 24px;
	font-size: 12px;
	color: #E8E8E8;
	text-align: left;
}
QPushButton::menu-indicator {
	width: 24px;
	height: 24px;
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="dspMemoryList">
     <property name="minimumSize">
      <size>
       <width>250</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>250</width>
       <height>24</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: #5C6068;
	border: 1px solid #747880;
	padding-left: 6px;
	padding-right: 24px;
	font-size: 12px;
	color: #E8E8E8;
	text-align: left;
}
QPushButton::menu-indicator {
	width: 24px;
	height: 24px;
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_3">
     <property name="minimumSize">
      <size>
       <width>33</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>33</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #8e8e8e;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="DSPVolumeTitle">
     <property name="minimumSize">
      <size>
       <width>86</width>
       <height>16</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>86</width>
       <height>16</height>
      </size>
     </property>
     <property name="text">
      <string>DSP Volume</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="DSPVolumeMute">
     <property name="minimumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">#DSPVolumeMute {
	image: url(:/Image/unmute.png);
	border: 1px solid #484c54;
	border-radius: 2px;
	background-color: #484c54;
}
#DSPVolumeMute::checked {
	image: url(:/Image/mute.png);
}</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QSpinBox" name="DSPVolumn">
     <property name="minimumSize">
      <size>
       <width>40</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>40</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QSpinBox {
	border: 1px solid #747880;
	background-color: #282828;
	font: 12px bold;
	color: #E8E8E8;
}
QSpinBox:hover {
	border-color: #7C8088;
}
QSpinBox:pressed {
	border-color: #CCD0D8;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
     <property name="buttonSymbols">
      <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
     </property>
     <property name="maximum">
      <number>62</number>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_4">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>10</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="volumnMinus">
     <property name="minimumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/icn_btn_minus_bold.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
     </property>
     <property name="autoRepeat">
      <bool>true</bool>
     </property>
     <property name="autoRepeatInterval">
      <number>200</number>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_5">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QSlider" name="DspVolumnSlider">
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>46</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>120</width>
       <height>46</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QSlider::groove {
    border: 1px solid #5C6068;
	border-radius: 2px;
    height: 4px;
	background-color: #181818;
}
QSlider::sub-page {
    height: 4px;
    border: 1px solid #5C6068;
	border-radius: 2px;
    background-color: #C8D8FF;
}
QSlider::handle {
	background-color: #A0A0A0;
    border: 0px;
    width: 18px;
	height: 18px;
	margin: -7px, -7px, 0px, 0px;
    border-radius: 9px;
}
QSlider::handle:pressed {
	background-color: #C0C0C0;
}</string>
     </property>
     <property name="maximum">
      <number>62</number>
     </property>
     <property name="pageStep">
      <number>0</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_6">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="volumnPlus">
     <property name="minimumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/icn_btn_plus_bold.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
     </property>
     <property name="autoRepeat">
      <bool>true</bool>
     </property>
     <property name="autoRepeatInterval">
      <number>200</number>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_4">
     <property name="minimumSize">
      <size>
       <width>33</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>33</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #8e8e8e;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="bassLevelTitle">
     <property name="minimumSize">
      <size>
       <width>76</width>
       <height>16</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>76</width>
       <height>16</height>
      </size>
     </property>
     <property name="text">
      <string>Bass Level</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="bassVolumeMute">
     <property name="minimumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">#bassVolumeMute {
	image: url(:/Image/unmute.png);
	border: 1px solid #484c54;
	border-radius: 2px;
	background-color: #484c54;
}
#bassVolumeMute::checked {
	image: url(:/Image/mute.png);
}</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_7">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="bassMinus">
     <property name="minimumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/icn_btn_minus_bold.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
     </property>
     <property name="autoRepeat">
      <bool>true</bool>
     </property>
     <property name="autoRepeatInterval">
      <number>200</number>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_9">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QSpinBox" name="bassLevel">
     <property name="minimumSize">
      <size>
       <width>32</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>32</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QSpinBox {
	border: 1px solid #747880;
	background-color: #282828;
	font: 12px bold;
	color: #E8E8E8;
}
QSpinBox:hover {
	border-color: #7C8088;
}
QSpinBox:pressed {
	border-color: #CCD0D8;
}</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
     <property name="buttonSymbols">
      <enum>QAbstractSpinBox::ButtonSymbols::NoButtons</enum>
     </property>
     <property name="minimum">
      <number>-8</number>
     </property>
     <property name="maximum">
      <number>8</number>
     </property>
     <property name="value">
      <number>-8</number>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_8">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="bassPlus">
     <property name="minimumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>14</width>
       <height>14</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/icn_btn_plus_bold.png);
border: 1px solid #8E8E8E;
border-radius: 2px;
background: #484C54;</string>
     </property>
     <property name="autoRepeat">
      <bool>true</bool>
     </property>
     <property name="autoRepeatInterval">
      <number>200</number>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_10">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>4</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="Line" name="line_">
     <property name="minimumSize">
      <size>
       <width>32</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>32</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #8e8e8e;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="bt">
     <property name="minimumSize">
      <size>
       <width>96</width>
       <height>26</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>96</width>
       <height>26</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
	border-radius: 13px;
	background-image: url(:/Image/btOff.png);
}</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
