#include "DataCenterController.h"
#include "qdebug.h"

DataCenterController::DataCenterController(QObject *parent)
    : QObject{parent}
    , mLoadPackageCount(0)
{
    mDataHandlers.insert(DeviceOperation::MID, new Mid01Handler);
    mDataHandlers.insert(ChannelOperation::MID, new Mid02Handler);
    mDataHandlers.insert(AudioOperation::MID, new Mid03Handler);
    mDataHandlers.insert(SystemOperation::MID, new Mid04Handler);
    mDataHandlers.insert(OTAOperation::MID, new Mid05Handler);
    mDataHandlers.insert(RemoteOperation::MID, new Mid06Handler);

    connect(&USBManager::getInstance(), &USBManager::receiveMessageSig, this, &DataCenterController::onReceiveMessage);
    // USBManager::getInstance().connectDevice();

    mHeartBeatTimer = new QTimer(this);
    connect(mHeartBeatTimer, &QTimer::timeout, [this](){ heartBeat0103(); });

    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::deviceTypeChanged, this, &DataCenterController::deviceTypeChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::mainVersionChanged, this, &DataCenterController::mainVersionChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::subVersionChanged, this, &DataCenterController::subVersionChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::mainGainChanged, this, &DataCenterController::mainGainChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::mainMuteChanged, this, &DataCenterController::mainMuteChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::usbConnectedChanged, this, &DataCenterController::usbConnectedChanged);
    // connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::musicSourceChanged, this, &DataCenterController::musicSourceChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::btConnectedChanged, this, &DataCenterController::btConnectedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::aptxConnectedChanged, this, &DataCenterController::aptxConnectedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::uacConnectedChanged, this, &DataCenterController::uacConnectedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::mainUnitSupportedChanged, this, &DataCenterController::mainUnitSupportedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::auxSupportedChanged, this, &DataCenterController::auxSupportedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::btSupportedChanged, this, &DataCenterController::btSupportedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::spdifSupportedChanged, this, &DataCenterController::spdifSupportedChanged);
    connect(dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID]), &Mid01Handler::usbAudioSupportedChanged, this, &DataCenterController::usbAudioSupportedChanged);

    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelTypeChanged, this, &DataCenterController::outputChannelTypeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelGainChanged, this, &DataCenterController::outputChannelGainChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelPositiveNegativeChanged, this, &DataCenterController::outputChannelPositiveNegativeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelMuteChanged, this, &DataCenterController::outputChannelMuteChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelDelayChanged, this, &DataCenterController::outputChannelDelayChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelDelayGroupChanged, this, &DataCenterController::outputChannelDelayGroupChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelEqSetChanged, this, &DataCenterController::outputChannelEqSetChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelEqTypeChanged, this, &DataCenterController::outputChannelEqTypeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLinkTypeChanged, this, &DataCenterController::outputChannelLinkTypeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLinkChannelChanged, this, &DataCenterController::outputChannelLinkChannelChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelHpFreqChanged, this, &DataCenterController::outputChannelHpFreqChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelHpSlopeChanged, this, &DataCenterController::outputChannelHpSlopeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelHpTypeChanged, this, &DataCenterController::outputChannelHpTypeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelHpEnableChanged, this, &DataCenterController::outputChannelHpEnableChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLpFreqChanged, this, &DataCenterController::outputChannelLpFreqChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLpSlopeChanged, this, &DataCenterController::outputChannelLpSlopeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLpTypeChanged, this, &DataCenterController::outputChannelLpTypeChanged);
    connect(dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID]), &Mid02Handler::outputChannelLpEnableChanged, this, &DataCenterController::outputChannelLpEnableChanged);

    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::mainGainChanged, this, &DataCenterController::mainGainChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::mainMuteChanged, this, &DataCenterController::mainMuteChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::dspGainChanged, this, &DataCenterController::dspGainChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::dspMuteChanged, this, &DataCenterController::dspMuteChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::bassLevelChanged, this, &DataCenterController::bassLevelChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::bassMuteChanged, this, &DataCenterController::bassMuteChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::currentSourceChanged, this, &DataCenterController::currentSourceChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::currentMemoryChanged, this, &DataCenterController::currentMemoryChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::mixGainChanged, this, &DataCenterController::mixGainChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::mixEnableChanged, this, &DataCenterController::mixEnableChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::eqFreqChanged, this, &DataCenterController::eqFreqChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::eqQValueChanged, this, &DataCenterController::eqQValueChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::eqGainChanged, this, &DataCenterController::eqGainChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::eqTypeChanged, this, &DataCenterController::eqTypeChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::eqEnableChanged, this, &DataCenterController::eqEnableChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::memoryEnableChanged, this, &DataCenterController::memoryEnableChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::memoryNameChanged, this, &DataCenterController::memoryNameChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::presetModeChanged, this, &DataCenterController::presetModeChanged);
    connect(dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID]), &Mid03Handler::presetLevelChanged, this, &DataCenterController::presetLevelChanged);

    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::naviMixEnableChanged, this, &DataCenterController::naviMixEnableChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::naviSensitivityChanged, this, &DataCenterController::naviSensitivityChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::naviAttenuationChanged, this, &DataCenterController::naviAttenuationChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::naviDurationChanged, this, &DataCenterController::naviDurationChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::externalAttenuationChanged, this, &DataCenterController::externalAttenuationChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::externalPolarityChanged, this, &DataCenterController::externalPolarityChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::externalDspAttenuationChanged, this, &DataCenterController::externalDspAttenuationChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::delayUnitChanged, this, &DataCenterController::delayUnitChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::highGainChanged, this, &DataCenterController::highGainChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::rcaGainChanged, this, &DataCenterController::rcaGainChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::auxGainChanged, this, &DataCenterController::auxGainChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::btGainChanged, this, &DataCenterController::btGainChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::spdifGainChanged, this, &DataCenterController::spdifGainChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::usbGainChanged, this, &DataCenterController::usbGainChanged);

    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::savePackageCountChanged, this, &DataCenterController::savePackageCountChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::savePackageDataChanged, this, &DataCenterController::savePackageDataChanged);
    connect(dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID]), &Mid04Handler::loadPackageReplyIndex, [&](uint16_t index){
        qDebug() << "index " << index << " mLoadPackageCount " << mLoadPackageCount;
        if(mLoadPackageCount == index)
        {
            mLoadPackageCount = -1;
            emit loadPackageFinished();
        }
    });

    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::dspMainVersionChanged, this, &DataCenterController::dspMainVersionChanged);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::dspSubVersionChanged, this, &DataCenterController::dspSubVersionChanged);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::remoteMainVersionChanged, this, &DataCenterController::remoteMainVersionChanged);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::remoteSubVersionChanged, this, &DataCenterController::remoteSubVersionChanged);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::upgradeError, this, &DataCenterController::upgradeError);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::upgradeProgress, this, &DataCenterController::upgradeProgress);
    connect(dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID]), &Mid05Handler::upgradeStatus, this, &DataCenterController::upgradeStatus);

    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remoteBrightnessChanged, this, &DataCenterController::remoteBrightnessChanged);
    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remoteDimmerChanged, this, &DataCenterController::remoteDimmerChanged);
    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remotePolarityChanged, this, &DataCenterController::remotePolarityChanged);
    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remoteDimmerBrightnessChanged, this, &DataCenterController::remoteDimmerBrightnessChanged);
    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remoteModelChanged, this, &DataCenterController::remoteModelChanged);
    connect(dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID]), &Mid06Handler::remoteShortCutChanged, this, &DataCenterController::remoteShortCutChanged);
}

void DataCenterController::initData(int deviceLevel)
{
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;
    int mixHighChannelMax = (1 == deviceLevel) ? MIX_HIGH_CHANNEL_L_MAX : MIX_HIGH_CHANNEL_MAX;

    dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->onInit(deviceLevel);

    getMusicSourceList0109();

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getOutputChannel0202(channelId);
        getCrossOver0208(channelId);
    }

    getMainGain0302();
    getChannelMix0306(static_cast<uint8_t>(MixTypeEnum::HIGH), 0x00, mixHighChannelMax);
    getChannelMix0306(static_cast<uint8_t>(MixTypeEnum::RCA), 0x00, MIX_RCA_CHANNEL_MAX);
    getChannelMix0306(static_cast<uint8_t>(MixTypeEnum::DSP), 0x00, MIX_DSP_CHANNEL_MAX);
    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getEQ0308(channelId);
    }
    getMemory030b(static_cast<uint8_t>(MemoryTypeEnum::MAIN_UNIT), 0x00, MEMORY_MAX);
    getMemory030b(static_cast<uint8_t>(MemoryTypeEnum::DSP), MEMORY_MAX, MEMORY_MAX);
    getMusicSource0304();
    getPreset030f();

    getSystemConfig0404();
    getInputGain0408();

    if(1 != deviceLevel)
    {
        getRemoteBrightness0602();
        getRemoteModel0604();
        getRemoteShortCut0606();
    }

    //最后查询，影响开机画面退出时刻
    getVersion0501();

    if(!mHeartBeatTimer->isActive())
    {
        mHeartBeatTimer->start(5000);
    }
}

void DataCenterController::updateDataByMemoryChange(int deviceLevel)
{
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getOutputChannel0202(channelId);
    }

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getEQ0308(channelId);
    }
}

void DataCenterController::updateDataByEqTypeChange(int deviceLevel)
{
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getEQ0308(channelId);
    }
}

void DataCenterController::updateDataByEqResetChange(int deviceLevel)
{
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getOutputChannel0202(channelId);
    }

    for(uint8_t channelId = 0x00; channelId < outputChannelMax; channelId++)
    {
        getEQ0308(channelId);
    }
}

uint8_t DataCenterController::calCrc8(const QByteArray &data) {
    uint8_t buf;
    uint8_t crc8 = 0x00;

    for(auto i = 0; i < data.size(); i++)
    {
        buf = data.at(i) ^ crc8;
        crc8 = caCrc8Data[buf];
    }
    return crc8;
}

QByteArray DataCenterController::packFrame(uint8_t mid, uint8_t sid, OptCode opt, QByteArray data)
{
    FrameHead frameHead;
    memset(&frameHead, 0, sizeof(FrameHead));
    frameHead.headerH = FrameHeadER_H;
    frameHead.headerL = FrameHeadER_L;
    frameHead.version = CURRENT_VERSION;
    frameHead.optcode.value = opt.value;
    frameHead.dataLen = data.size();
    frameHead.mid = mid;
    frameHead.sid = sid;

    QByteArray sendFrame;
    sendFrame.append(QByteArray((char *)&frameHead, sizeof(FrameHead)));
    sendFrame.append(data);

    uint8_t crc8 = calCrc8(sendFrame);
    sendFrame.append(crc8);

    return sendFrame;
}

int DataCenterController::unpackFrame(const QByteArray &data, FrameHead &header, QByteArray &detailData) {
    // 检查最小长度
    if (data.size() < MIN_FRAME_LENGTH) {
        qDebug("data size is not enough");
        return -1;
    }

    // 检查帧头
    if ((FrameHeadER_H != static_cast<uint8_t>(data.at(0)))
        || (FrameHeadER_L != static_cast<uint8_t>(data.at(1)))) {
        qDebug("header is not 0xAA55");
        return -2;
    }

    // 检查CRC8
    if(static_cast<uint8_t>(data.back()) != calCrc8(data.chopped(1)))
    {
        qDebug("crc8 check failed");
        return -3;
    }

    // 解析帧结构
    header.headerH = data.at(0);
    header.headerL = data.at(1);
    header.version = data.at(2);
    header.optcode.value = data.at(3);
    header.dataLen = (static_cast<uint8_t>(data.at(5)) << 8) | static_cast<uint8_t>(data.at(4));
    header.mid = data.at(6);
    header.sid = data.at(7);

    detailData = data.mid(sizeof(header)).chopped(1);

    return 0;
}

void DataCenterController::parseData(const FrameHead &header, const QByteArray &data)
{
    ErrorCode errorcode = static_cast<ErrorCode>(header.optcode.bits.errcode);
    if(ErrorCode::SUCCESS != errorcode)
    {
        emit receivedErrorSig(errorcode);
        return;
    }

    if(data.size() != header.dataLen)
    {
        qDebug("datalen error, data size: %llx, datalen: %x", data.size(), header.dataLen);
        return;
    }

    mDataHandlers[header.mid]->parseReceivedData(header.sid, data);
}

void DataCenterController::onReceiveMessage(const QByteArray &data)
{
    FrameHead header;
    QByteArray detailData;
    memset(&header, 0, sizeof(FrameHead));
    int ret = unpackFrame(data, header, detailData);
    if(0 != ret)
    {
        return;
    }

    parseData(header, detailData);
}

void DataCenterController::connectRequest0101()
{
    QByteArray frameData = dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->send01Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(DeviceOperation::MID, static_cast<uint8_t>(DeviceOperation::SID::CONNECT_REQUEST), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::heartBeat0103()
{
    QByteArray frameData = dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->send03Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(DeviceOperation::MID, static_cast<uint8_t>(DeviceOperation::SID::HEARTBEAT), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setMusicSourceList0108(const DeviceOperation0809 &data)
{
    QByteArray frameData = dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->send08Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(DeviceOperation::MID, static_cast<uint8_t>(DeviceOperation::SID::SOURCE_ENABLE_SETTING), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getMusicSourceList0109()
{
    QByteArray frameData = dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->send09Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(DeviceOperation::MID, static_cast<uint8_t>(DeviceOperation::SID::SOURCE_ENABLE_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setOutputChannel0201(const ChannelOperation0102 &data)
{
    QByteArray frameData = dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->send01Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(ChannelOperation::MID, static_cast<uint8_t>(ChannelOperation::SID::OUTPUT_CHANNEL_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getOutputChannel0202(uint8_t channel)
{
    QByteArray frameData = dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->send02Data(channel);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(ChannelOperation::MID, static_cast<uint8_t>(ChannelOperation::SID::OUTPUT_CHANNEL_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setCrossOver0207(const ChannelOperation0708 &data)
{
    QByteArray frameData = dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->send07Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(ChannelOperation::MID, static_cast<uint8_t>(ChannelOperation::SID::CHANNEL_CROSSOVER_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getCrossOver0208(uint8_t channel)
{
    QByteArray frameData = dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->send08Data(channel);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(ChannelOperation::MID, static_cast<uint8_t>(ChannelOperation::SID::CHANNEL_CROSSOVER_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setMainGain0301(const AudioOperation0102 &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send01Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MAIN_VOLUME_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getMainGain0302()
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send02Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MAIN_VOLUME_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setMusicSource0303(const AudioOperation0304 &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send03Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MUSIC_SOURCE_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getMusicSource0304()
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send04Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MUSIC_SOURCE_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setChannelMix0305(const AudioOperation0506 &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send05Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::CHANNEL_MIX_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getChannelMix0306(uint8_t source, uint8_t channel, uint8_t num)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send06Data(source, channel, num);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::CHANNEL_MIX_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setEQ0307(const AudioOperation0708 &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send07Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::EQ_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getEQ0308(uint8_t channel, uint8_t band, uint8_t num)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send08Data(channel, band, num);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::EQ_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setEqLink0309(const AudioOperation09 &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send09Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::EQ_LINK_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setMemory030a(const AudioOperation0A0B &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0AData(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MEMORY_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getMemory030b(uint8_t type, uint8_t memory, uint8_t num)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0BData(type, memory, num);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MEMORY_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setMemoryCopy030c(const AudioOperation0C &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0CData(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::MEMORY_COPY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setDelayGroup030d(const AudioOperation0D &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0DData(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::DELAY_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setPreset030e(const AudioOperation0E0F &data)
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0EData(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::PRESET_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getPreset030f()
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send0FData();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::PRESET_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setDisconnectBt0310()
{
    QByteArray frameData = dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->send10Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(AudioOperation::MID, static_cast<uint8_t>(AudioOperation::SID::DISCONNECT_BT), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setSystemConfig0403(const SystemOperation0304 &data)
{
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send03Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::SYSTEM_CONFIG1_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getSystemConfig0404()
{
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send04Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::SYSTEM_CONFIG1_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setSystemConfig0407(const SystemOperation0708 &data)
{
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send07Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::INPUT_CONFIG_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getInputGain0408()
{
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send08Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::INPUT_CONFIG_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setInitialization0409()
{
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send09Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::RESTORE_FACTORY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::querySaveData(uint16_t index)
{
    qDebug() << "querySaveData index " << index;
    QByteArray frameData = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send0AData(index);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::SAVE_ELECTRONIC), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

int DataCenterController::sendLoadData(const QByteArray &data, int deviceLevel)
{
    QByteArray dataPart = data.sliced(15);

    QByteArray header = data.first(10);
    if(header != "PioneerDSP")
    {
        //非配置文件
        return -2;
    }

    QByteArray dataLengthArray = data.mid(10, 4);
    uint dataLength = 0;
    memcpy(&dataLength, dataLengthArray.data(), sizeof(dataLength));
    if(dataPart.length() != dataLength)
    {
        //非配置文件
        return -2;
    }

    QByteArray crcArray = data.mid(14, 1);
    uint8_t crcCode = crcArray.at(0);
    if(calCrc8(dataPart) != crcCode)
    {
        //非配置文件
        return -2;
    }

    QVector<QByteArray> frameDatas;
    // mLoadPackageCount = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send0BData(data, frameDatas);
    mLoadPackageCount = dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->send0BData(dataPart, frameDatas);

    if(mLoadPackageCount < (frameDatas.count() - 1))
    {
        qDebug() << "data package count > mLoadPackageCount " << mLoadPackageCount;
    }

    uint8_t packageDeviceLevel = frameDatas[0].at(7);
    uint8_t currentDeviceLevel = (1 == deviceLevel) ? 0 : 1;

    if(packageDeviceLevel != currentDeviceLevel)
    {
        if((0 == packageDeviceLevel) || (1 == packageDeviceLevel))
        {
            //高低配不匹配
            return -1;
        }
        else
        {
            //非配置文件
            return -2;
        }
    }

    OptCode opt = MakeOptCode(true, false);

    for (int index = 0; index < (mLoadPackageCount + 1); ++index) {
        QByteArray sendFrame = packFrame(SystemOperation::MID, static_cast<uint8_t>(SystemOperation::SID::LOAD_ELECTRONIC),
                                         opt, frameDatas[index]);
        USBManager::getInstance().sendMessage(sendFrame);
    }

    return 0;
}

void DataCenterController::getVersion0501()
{
    QByteArray frameData = dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID])->send01Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(OTAOperation::MID, static_cast<uint8_t>(OTAOperation::SID::SYSTEM_VERSION_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::upgradeDspRemote0502(uint8_t type, const QByteArray &data)
{
    QList<QByteArray> frame02Datas;
    dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID])->send02Data(type, data, frame02Datas);

    OptCode opt = MakeOptCode(true, false);

    for (QByteArray frameData: frame02Datas) {
        QByteArray send02Frame = packFrame(OTAOperation::MID, static_cast<uint8_t>(OTAOperation::SID::UPGRADE_DATA), opt, frameData);
        USBManager::getInstance().sendMessage(send02Frame);
    }

    getUpgradeDspRemoteRet0503();
}

void DataCenterController::getUpgradeDspRemoteRet0503()
{
    QByteArray frame03Data = dynamic_cast<Mid05Handler*>(mDataHandlers[OTAOperation::MID])->send03Data();

    OptCode opt = MakeOptCode(true, false);

    QByteArray send03Frame = packFrame(OTAOperation::MID, static_cast<uint8_t>(OTAOperation::SID::UPGRADE_RESULT), opt, frame03Data);
    USBManager::getInstance().sendMessage(send03Frame);
}

void DataCenterController::setRemoteBrightness0601(const RemoteOperation0102 &data)
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send01Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::BRIGHTNESS_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getRemoteBrightness0602()
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send02Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::BRIGHTNESS_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setRemoteModel0603(const RemoteOperation0304 &data)
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send03Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::MODEL_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getRemoteModel0604()
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send04Data();

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::MODEL_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::setRemoteShortCut0605(const RemoteOperation0506 &data)
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send05Data(data);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::SHORTCUT_SET), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}

void DataCenterController::getRemoteShortCut0606()
{
    QByteArray frameData = dynamic_cast<Mid06Handler*>(mDataHandlers[RemoteOperation::MID])->send06Data(REMOTE_SHORTCUT_MAX);

    OptCode opt = MakeOptCode(true, false);
    QByteArray sendFrame = packFrame(RemoteOperation::MID, static_cast<uint8_t>(RemoteOperation::SID::SHORTCUT_QUERY), opt, frameData);

    USBManager::getInstance().sendMessage(sendFrame);
}


const DeviceOperation01& DataCenterController::getDeviceOperation01()
{
    return dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->getDeviceOperation01();
}

const DeviceOperation03& DataCenterController::getDeviceOperation03()
{
    return dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->getDeviceOperation03();
}

const DeviceOperation0809& DataCenterController::getDeviceOperation0809()
{
    return dynamic_cast<Mid01Handler*>(mDataHandlers[DeviceOperation::MID])->getDeviceOperation0809();
}

const QVector<ChannelOperation0102>& DataCenterController::getChannelOperation0102Vector()
{
    return dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->getChannelOperation0102Vector();
}

const QVector<ChannelOperation0708>& DataCenterController::getChannelOperation0708Vector()
{
    return dynamic_cast<Mid02Handler*>(mDataHandlers[ChannelOperation::MID])->getChannelOperation0708Vector();
}

const AudioOperation0102& DataCenterController::getAudioOperation0102()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0102();
}

const AudioOperation0304& DataCenterController::getAudioOperation0304()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0304();
}

const QMap<uint8_t, QMap<uint8_t, QVector<Mix>>>& DataCenterController::getAudioOperation0506Map()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0506Map();
}

const QMap<uint8_t, QVector<EqParm>>& DataCenterController::getAudioOperation0708Map()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0708Map();
}

const QMap<uint8_t, QVector<MemorySetting>>& DataCenterController::getAudioOperation0A0B()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0A0B();
}

const AudioOperation0E0F& DataCenterController::getAudioOperation0E0F()
{
    return dynamic_cast<Mid03Handler*>(mDataHandlers[AudioOperation::MID])->getAudioOperation0E0F();
}

const SystemOperation0304& DataCenterController::getSystemOperation0304()
{
    return dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->getSystemOperation0304();
}

const SystemOperation0708& DataCenterController::getSystemOperation0708()
{
    return dynamic_cast<Mid04Handler*>(mDataHandlers[SystemOperation::MID])->getSystemOperation0708();
}
