#include "Mid04Handler.h"
#include "qdebug.h"

Mid04Handler::Mid04<PERSON>andler()
{
    memset(&mSystemOperation0304, 0, sizeof(mSystemOperation0304));
    memset(&mSystemOperation0708, 0, sizeof(mSystemOperation0708));
}

void Mid04Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(SystemOperation::SID::SYSTEM_CONFIG1_QUERY):
    {
        parse04Data(data);
        break;
    }
    case static_cast<uint8_t>(SystemOperation::SID::INPUT_CONFIG_QUERY):
    {
        parse08Data(data);
        break;
    }
    case static_cast<uint8_t>(SystemOperation::SID::SAVE_ELECTRONIC):
    {
        parse0AData(data);
        break;
    }
    case static_cast<uint8_t>(SystemOperation::SID::LOAD_ELECTRONIC):
    {
        parse0BData(data);
        break;
    }
    default:
        break;
    }
}

void Mid04Handler::parse04Data(const QByteArray &data)
{
    // bool ret = false;
    SystemOperation0304 temp04;
    memcpy(&temp04, data.data(), data.size());

    // if(temp04.naviMixEnable != mSystemOperation0304.naviMixEnable)
    // {
        emit naviMixEnableChanged(temp04.naviMixEnable);
    //     ret = true;
    // }
    // if(temp04.naviSensitivity != mSystemOperation0304.naviSensitivity)
    // {
        emit naviSensitivityChanged(temp04.naviSensitivity);
    //     ret = true;
    // }
    // if(temp04.naviAttenuation != mSystemOperation0304.naviAttenuation)
    // {
        emit naviAttenuationChanged(temp04.naviAttenuation);
    //     ret = true;
    // }
    // if(temp04.naviDuration != mSystemOperation0304.naviDuration)
    // {
        emit naviDurationChanged(temp04.naviDuration);
    //     ret = true;
    // }
    // if(temp04.externalAttenuation != mSystemOperation0304.externalAttenuation)
    // {
        emit externalAttenuationChanged(temp04.externalAttenuation);
    //     ret = true;
    // }
    // if(temp04.externalPolarity != mSystemOperation0304.externalPolarity)
    // {
        emit externalPolarityChanged(temp04.externalPolarity);
    //     ret = true;
    // }
    // if(temp04.externalDspAttenuation != mSystemOperation0304.externalDspAttenuation)
    // {
        emit externalDspAttenuationChanged(temp04.externalDspAttenuation);
    //     ret = true;
    // }
    // if(temp04.delayUnit != mSystemOperation0304.delayUnit)
    // {
        emit delayUnitChanged(temp04.delayUnit);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mSystemOperation0304, data.data(), data.size());
    // }
}

void Mid04Handler::parse08Data(const QByteArray &data)
{
    // bool ret = false;
    SystemOperation0708 temp08;
    memcpy(&temp08, data.data(), data.size());

    // if(temp08.highGain != mSystemOperation0708.highGain)
    // {
        emit highGainChanged(temp08.highGain);
    //     ret = true;
    // }
    // if(temp08.rcaGain != mSystemOperation0708.rcaGain)
    // {
        emit rcaGainChanged(temp08.rcaGain);
    //     ret = true;
    // }
    // if(temp08.auxGain != mSystemOperation0708.auxGain)
    // {
        emit auxGainChanged(temp08.auxGain);
    //     ret = true;
    // }
    // if(temp08.btGain != mSystemOperation0708.btGain)
    // {
        emit btGainChanged(temp08.btGain);
    //     ret = true;
    // }
    // if(temp08.spdifGain != mSystemOperation0708.spdifGain)
    // {
        emit spdifGainChanged(temp08.spdifGain);
    //     ret = true;
    // }
    // if(temp08.usbGain != mSystemOperation0708.usbGain)
    // {
        emit usbGainChanged(temp08.usbGain);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mSystemOperation0708, data.data(), data.size());
    // }
}

void Mid04Handler::parse0AData(const QByteArray &data)
{
    uint16_t dataIndex = (((data.at(1) << 8) & 0xFF00) | (data.at(0) & 0x00FF));
    if(0 == dataIndex)
    {
        int saveCount = (((data.at(4) << 8) & 0xFF00) | (data.at(3) & 0x00FF)) + (((data.at(6) << 8) & 0xFF00) | (data.at(5) & 0x00FF));
        emit savePackageCountChanged(saveCount, data);
    }
    else
    {
        emit savePackageDataChanged(dataIndex, data);
    }
}

void Mid04Handler::parse0BData(const QByteArray &data)
{
    uint16_t dataIndex = (((data.at(1) << 8) & 0xFF00) | (data.at(0) & 0x00FF));
    emit loadPackageReplyIndex(dataIndex);
}

QByteArray Mid04Handler::send03Data(const SystemOperation0304 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(SystemOperation0304)));

    return frameData;
}

QByteArray Mid04Handler::send04Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid04Handler::send07Data(const SystemOperation0708 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(SystemOperation0708)));

    return frameData;
}

QByteArray Mid04Handler::send08Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid04Handler::send09Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid04Handler::send0AData(uint16_t index)
{
    QByteArray frameData;
    frameData.append((uint8_t)(index & 0x00FF));
    frameData.append((uint8_t)((index & 0xFF00) >> 8));

    return frameData;
}

int Mid04Handler::send0BData(const QByteArray &data, QVector<QByteArray> &frameDatas)
{
    if(data.size() < 8)
    {
        qDebug() << "data size < 8";
        return -1;
    }

    frameDatas.clear();
    QByteArray sendData = data;

    QByteArray frame0 = sendData.first(8);
    uint16_t maxPackCount = (((frame0.at(4) << 8) & 0xFF00) | (frame0.at(3) & 0x00FF)) + (((frame0.at(6) << 8) & 0xFF00) | (frame0.at(5) & 0x00FF));

    frameDatas.append(frame0);
    sendData.slice(8);

    while(!sendData.isEmpty())
    {
        if(sendData.size() <= 131)
        {
            sendData.resize(131, 0x00);
            frameDatas.append(sendData);
            break;
        }
        else
        {
            QByteArray frame = sendData.first(131);
            frameDatas.append(frame);
            sendData.slice(131);
        }
    }

    return maxPackCount;
}

const SystemOperation0304& Mid04Handler::getSystemOperation0304()
{
    return mSystemOperation0304;
}

const SystemOperation0708& Mid04Handler::getSystemOperation0708()
{
    return mSystemOperation0708;
}
