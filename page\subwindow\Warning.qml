import QtQuick
import QtQuick.Controls
import "../framework"

SubWindow {
    id: root
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 480
    contentHeight: 200
    titleStr: qsTr("Warning")
    exitVisible: {
        switch(warningNum)
        {
        case 15:
            return false
        default:
            return true
        }
    }
    confirmVisible: {
        switch(warningNum)
        {
        case 12:
        case 13:
        case 15:
        case 17:
            return false
        default:
            return true
        }
    }
    confirmStr: {
        switch(warningNum)
        {
        case 14:
            return qsTr("Start Update")
        default:
            return qsTr("OK")
        }
    }
    cancelVisible: {
        switch(warningNum)
        {
        case 15:
        case 16:
            return false
        default:
            return true
        }
    }
    cancelStr: {
        switch(warningNum)
        {
        case 12:
        case 13:
        case 17:
            return qsTr("Close")
        default:
            return qsTr("Cancel")
        }
    }

    property int warningNum: dataMap["uiDataMap"]["warningNum"]
    property bool iconVisible: {
        switch(warningNum)
        {
        case 15:
            return true
        default:
            return false
        }
    }

    property string warningText: {
        switch(warningNum)
        {
        case 1:
            return qsTr("Current setting will be discarded and the Linked EQ will be copied.
Will it continue?")
        case 2:
            return qsTr("If you change EQ type to GEQ, parameters will be reseted.
Will it continue?")
        case 3:
            return qsTr("EQ settings will be reseted and the EQ link will be released.
Will it continue?")
        case 4:
            return qsTr("The current EQ settings will be discarded.")
        case 5:
            return qsTr("Performing Device Update. Please select the downloaded update file.")
        case 6:
            return qsTr("Do not turn off and disconnect until the update is completed.
Will it continue?")
        case 8:
            return qsTr("Restore a device to factory setting.
Current setting will be erased.
Will it continue?")
        case 10:
            return qsTr("All device settings is saved to your equipment.
Will it continue?")
        case 11:
            return qsTr("All device settings is loaded based on your saved data, and all current settings is discarded.
Will it continue?")
        case 12:
            return qsTr("File reading aborted: This is not %1 configuration data").arg(dataMap["deviceType"])
        case 13:
            return qsTr("File reading aborted: This is not the product's configuration data.‌")
        case 14:
            return qsTr("Device Firmware Update in Progress.
Do not turn off the power during the update.
Do not disconnect from the PC.
")
        case 15:
            return qsTr("Updating... Please wait.")
        case 16:
            return qsTr("The update is now complete.
Configure your DSP sound settings.
The PC tool will now restart.")
        case 17:
            return qsTr("Firmware is broken ，Please Check.")
        default:
            return ""
        }
    }

    contentItem: Rectangle {
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"

        // 中心旋转的Vector图片
        property real vectorAngle: 0
        Image {
            id: vectorImg
            source: "qrc:/Image/Vector.png"
            anchors.verticalCenter: parent.verticalCenter
            anchors.left: parent.left
            anchors.leftMargin: 24
            visible: iconVisible
            fillMode: Image.PreserveAspectFit

            RotationAnimation on rotation {
                loops: Animation.Infinite
                from: 0
                to: 360
                duration: 1000
                easing.type: Easing.Linear
            }
        }

        Text {
            anchors.left: parent.left
            anchors.leftMargin: iconVisible ? 96 : 34
            anchors.verticalCenter: parent.verticalCenter
            width: (parent.width - (34 + (iconVisible ? 96 : 34)))
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            color: "#FFFFFF"
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignLeft
            verticalAlignment: Text.AlignVCenter
            lineHeight: 20
            lineHeightMode: Text.FixedHeight
            text: warningText
        }
    }

    onClickCancel: clickClose()
}
