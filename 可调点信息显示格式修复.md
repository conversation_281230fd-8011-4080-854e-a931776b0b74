# 可调点信息显示格式修复

## 问题描述

在CanvasView中，当选中可调点时，会显示点的参数信息。原来的显示格式为单行：
```
F:xx; Q: x.xx; G:xx.x;
```

需要调整为两行显示格式：
- 第一行：`Freq xx`
- 第二行：`Gxx.x/Qx.xxx`（Q显示到小数点后三位）

## 修复内容

### 修改位置
**文件**：`page/framework/curve/canvasview.cpp`
**函数**：`drawSelectedPointInfo` (第848-884行)

### 修复前的代码
```cpp
// 格式化显示信息，使用";"分隔，":"连接名称和值
QString infoText = QString("F:%1; Q:%2; G:%3").arg(frequency, 0, 'f', 0)
                    .arg(qValue, 0, 'f', 2)
                    .arg(gain, 0, 'f', 1);

// 设置文本样式
QFont font = painter.font();
font.setBold(true);
painter.setFont(font);

// 绘制背景
QFontMetrics fm(font);
QRect textRect = fm.boundingRect(infoText);
textRect.adjust(-4, -2, 4, 2);
textRect.moveCenter(QPoint(wpt.x(), wpt.y() + 25));

painter.setPen(Qt::NoPen);
painter.setBrush(QColor(0, 0, 0, 180));
painter.drawRoundedRect(textRect, 4, 4);

// 绘制文本
painter.setPen(Qt::white);
painter.drawText(textRect, Qt::AlignCenter, infoText);
```

### 修复后的代码
```cpp
// 格式化显示信息为两行
// 第一行：Freq xx
QString line1 = QString("Freq %1").arg(frequency, 0, 'f', 0);
// 第二行：Gxx.x/Qx.xxx（Q显示到小数点后三位）
QString line2 = QString("G%1/Q%2").arg(gain, 0, 'f', 1)
                                  .arg(qValue, 0, 'f', 3);

// 设置文本样式
QFont font = painter.font();
font.setBold(true);
painter.setFont(font);

// 计算两行文本的尺寸
QFontMetrics fm(font);
QRect line1Rect = fm.boundingRect(line1);
QRect line2Rect = fm.boundingRect(line2);

// 计算总的文本区域（取较宽的一行作为宽度）
int maxWidth = qMax(line1Rect.width(), line2Rect.width());
int totalHeight = line1Rect.height() + line2Rect.height() + 2; // 2像素行间距

QRect totalRect(0, 0, maxWidth + 8, totalHeight + 4); // 添加边距
totalRect.moveCenter(QPoint(wpt.x(), wpt.y() + 30)); // 稍微向下移动

// 绘制背景
painter.setPen(Qt::NoPen);
painter.setBrush(QColor(0, 0, 0, 180));
painter.drawRoundedRect(totalRect, 4, 4);

// 绘制第一行文本
painter.setPen(Qt::white);
QRect line1DrawRect(totalRect.left(), totalRect.top() + 2, totalRect.width(), line1Rect.height());
painter.drawText(line1DrawRect, Qt::AlignCenter, line1);

// 绘制第二行文本
QRect line2DrawRect(totalRect.left(), totalRect.top() + line1Rect.height() + 4, totalRect.width(), line2Rect.height());
painter.drawText(line2DrawRect, Qt::AlignCenter, line2);
```

## 修复详情

### 显示格式变化

#### 修复前
- **单行显示**：`F:1000; Q: 2.20; G:6.0;`
- **格式特点**：使用分号分隔，冒号连接
- **Q值精度**：小数点后2位

#### 修复后
- **第一行**：`Freq 1000`
- **第二行**：`G6.0/Q2.200`
- **格式特点**：两行显示，斜杠分隔G和Q值
- **Q值精度**：小数点后3位

### 技术改进

#### 1. 文本布局优化
- **动态宽度计算**：根据两行文本中较宽的一行确定背景宽度
- **精确高度计算**：考虑两行文本高度和行间距
- **居中对齐**：每行文本在各自区域内居中显示

#### 2. 视觉效果改进
- **背景尺寸**：自动适应文本内容大小
- **行间距**：在两行之间添加2像素间距
- **边距**：背景四周添加适当边距
- **位置调整**：信息框稍微向下移动（y+30）

#### 3. 数值精度调整
- **频率**：整数显示（无小数点）
- **增益**：小数点后1位
- **Q值**：小数点后3位（提高精度）

## 显示效果对比

### 示例1：普通EQ点
**修复前**：`F:1000; Q: 2.20; G:6.0;`
**修复后**：
```
Freq 1000
G6.0/Q2.200
```

### 示例2：高频EQ点
**修复前**：`F:8000; Q: 0.71; G:-3.5;`
**修复后**：
```
Freq 8000
G-3.5/Q0.710
```

### 示例3：低频EQ点
**修复前**：`F:100; Q: 4.32; G:12.0;`
**修复后**：
```
Freq 100
G12.0/Q4.320
```

## 用户体验改进

### 优势
1. **信息更清晰**：两行显示避免了单行过长
2. **层次分明**：频率和增益/Q值分开显示
3. **精度提升**：Q值显示到小数点后3位，便于精确调整
4. **视觉简洁**：去除了多余的标点符号

### 兼容性
- ✅ **功能不变**：显示的信息内容完全一致
- ✅ **位置合理**：信息框位置经过优化调整
- ✅ **样式一致**：保持原有的字体和颜色风格
- ✅ **性能无影响**：绘制效率基本相同

## 总结

这个修复改进了可调点信息的显示格式，从单行显示改为更清晰的两行显示：

1. **第一行显示频率**：`Freq xx` 格式，简洁明了
2. **第二行显示增益和Q值**：`Gxx.x/Qx.xxx` 格式，信息紧凑
3. **提高Q值精度**：从2位小数提升到3位小数
4. **优化视觉布局**：动态计算尺寸，确保显示效果最佳

用户现在可以更清晰地查看选中点的参数信息，特别是Q值的精确数值，这对于精细的EQ调整非常有帮助。
