import QtQuick
import QtQuick.Controls.Basic
import Qt5Compat.GraphicalEffects

Item {
    width: 46
    height: 198

    Column {
        spacing: 2

        Repeater {
            model: 10

            CheckBox {
                id: control
                width: 46
                height: 18
                padding: 0
                checked: false
                opacity: enabled ? 1.0 : 0.3
                visible: (6 > modelData) || (1 !== dataMap["uiDataMap"]["deviceLevel"])

                readonly property var colorA: ["#00C8E8", "#00F090", "#D744D6", "#A870FF", "#E0A000",
                    "#D0D000", "#0088FF", "#FF80C0", "#00B0A0", "#F05878"]

                indicator: Item {
                    x: 3
                    y: 4
                    implicitWidth: 10
                    implicitHeight: 10
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/Image/checked.png"
                        ColorOverlay {
                            anchors.fill: parent
                            source: parent
                            color: colorA[modelData]
                        }
                        visible: control.checked
                    }
                }

                contentItem: Item {
                    Row{
                        anchors.left: parent.left
                        anchors.leftMargin: 15
                        anchors.verticalCenter: parent.verticalCenter
                        spacing: 0

                        Text {
                            anchors.bottom: titleNum.bottom
                            width: 14
                            height: 8
                            color: "#E8E8E8"
                            font.family: "Segoe UI"
                            font.pixelSize: 10
                            verticalAlignment: Text.AlignVCenter
                            text: "CH"
                        }

                        Text {
                            id: titleNum
                            anchors.verticalCenter: parent.verticalCenter
                            width: 14
                            height: 10
                            color: "#E8E8E8"
                            font.family: "Segoe UI"
                            font.pixelSize: 12
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: modelData + 1
                        }
                    }
                }

                background: Rectangle {
                    color: "#2C3038"
                    border.width: 1
                    border.color: (control.checked || (modelData === dataMap["uiDataMap"]["selectedChannel"])) ?
                                      colorA[modelData] : "#747880"
                }

                onClicked: commonCtrl.channelListClicked(modelData, checked)
            }
        }
    }
}
