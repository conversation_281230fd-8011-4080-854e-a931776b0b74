#include "HidSender.h"
#include "qdebug.h"

HidSender::HidSender(QObject *parent)
    : QObject{parent},
    mHidHandle(nullptr)
{

}

HidSender::~HidSender()
{

}

void HidSender::setHidHandle(hid_device* handle)
{
    mHidHandle = handle;
}

int HidSender::sendMessage(const QByteArray &data)
{
    if(mHidHandle == nullptr) {
        QByteArray sendData = data;
        qInfo() << "there is no device connected, data " << sendData.toHex();
        return -1;
    }
    
    uint8_t writeBuf[MAX_FRAME_LENGTH] = {0};
    
    QByteArray sendData = data;
    int dataSize = sendData.size();
    int ret = 0;

    while (dataSize > (MAX_FRAME_LENGTH - 2)) {
        sendData.prepend((uint8_t)0x3E);
        sendData.prepend((uint8_t)0x00);
        qInfo() << "sendData " << sendData.toHex();

        for(int i = 0; i < MAX_FRAME_LENGTH; ++i) {
            writeBuf[i] = sendData.at(i);
        }

        ret = hid_write(mHidHandle, writeBuf, MAX_FRAME_LENGTH);

        if(ret < 0)
        {
            qInfo("hid write interface_number: %ls", hid_error(mHidHandle));
            // qInfo() << "sendData " << sendData.toHex();
            return -2;
        }

        sendData.slice(MAX_FRAME_LENGTH);
        dataSize = sendData.size();
    }

    if(0 != dataSize)
    {
        sendData.prepend(dataSize & 0xFF);
        sendData.prepend(dataSize >> 8 & 0xFF);
        qInfo() << "sendData " << sendData.toHex();

        for(int i = 0; i < sendData.size(); ++i) {
            writeBuf[i] = sendData.at(i);
        }
        for(int i = sendData.size(); i < MAX_FRAME_LENGTH; ++i) {
            writeBuf[i] = 0x00;
        }

        ret = hid_write(mHidHandle, writeBuf, MAX_FRAME_LENGTH);

        if(ret < 0)
        {
            qInfo("hid write interface_number: %ls", hid_error(mHidHandle));
            // qInfo() << "sendData " << sendData.toHex();
            return -2;
        }
    }

    QByteArray readData;
    ret = receiveMessage(readData);
    if(0 != ret)
    {
        return ret;
    }

    int dataLength = static_cast<uint8_t>(readData.at(5)) << 8
                     | static_cast<uint8_t>(readData.at(4));
    dataLength -= (MAX_FRAME_LENGTH - MIN_FRAME_LENGTH);
    while(dataLength > 0)
    {
        QByteArray receiveData;
        ret = receiveMessage(receiveData);
        if(0 != ret)
        {
            return ret;
        }
        readData += receiveData;
        dataLength -= (MAX_FRAME_LENGTH - 1);
    }

    emit receivedMessageSig(readData);

    return 0;
}

int HidSender::receiveMessage(QByteArray &data)
{
    uint8_t readBuf[MAX_FRAME_LENGTH] = {0};
    int ret = hid_read_timeout(mHidHandle, readBuf, MAX_FRAME_LENGTH, 4000);

    if(ret < 0)
    {
        qInfo("hid read interface_number: %ls", hid_error(mHidHandle));
        return -3;
    }
    else if(0 == ret)
    {
        qInfo("hid read timeout");
        return -4;
    }
    else
    {
        QByteArray readData(const_cast<const char*>(reinterpret_cast<char*>(readBuf)),
                            readBuf[0]+1/*MAX_FRAME_LENGTH*/);
        // qInfo("hid read: %s", readData.toHex().data());
        qInfo() << "readData " << readData.toHex();
        readData.remove(0, 1);
        data = readData;
    }

    return 0;
}

void HidSender::onSendMessage(const QByteArray &data)
{
    int ret = sendMessage(data);
    if(0 != ret)
    {
        emit receivedErrorSig(ret);
    }
}
