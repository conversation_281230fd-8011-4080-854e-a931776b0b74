@echo off
setlocal enabledelayedexpansion

REM Generate version.h from git tag information
REM Usage: generate_version.bat [output_directory]

set "OUTPUT_DIR=%~1"
if "%OUTPUT_DIR%"=="" set "OUTPUT_DIR=page"

set "VERSION_FILE=%OUTPUT_DIR%\version.h"

echo [INFO] Generating version.h...

REM Get git tag version
git describe --tags --abbrev=0 > "%TEMP%\git_version.txt" 2>nul
if errorlevel 1 (
    REM No tags found, use default version
    set "VERSION_TAG=v1.0.0"
    set "VERSION_MAJOR=1"
    set "VERSION_MINOR=0"
    set "VERSION_PATCH=0"
    echo [WARNING] No git tags found, using default version: 1.0.0
) else (
    REM Read version from temp file
    set /p VERSION_TAG=<"%TEMP%\git_version.txt"
    REM Remove v or V prefix if present
    set "VERSION_CLEAN=!VERSION_TAG!"
    if "!VERSION_TAG:~0,1!"=="v" set "VERSION_CLEAN=!VERSION_TAG:~1!"
    if "!VERSION_TAG:~0,1!"=="V" set "VERSION_CLEAN=!VERSION_TAG:~1!"
    
    REM Parse version components (assuming format: major.minor.patch)
    for /f "tokens=1,2,3 delims=." %%a in ("!VERSION_CLEAN!") do (
        set "VERSION_MAJOR=%%a"
        set "VERSION_MINOR=%%b"
        set "VERSION_PATCH=%%c"
    )
    
    REM Handle cases where patch version might be missing
    if "!VERSION_PATCH!"=="" set "VERSION_PATCH=0"
    if "!VERSION_MINOR!"=="" set "VERSION_MINOR=0"
    
    echo [INFO] Using git tag version: !VERSION_TAG! ^(!VERSION_MAJOR!.!VERSION_MINOR!.!VERSION_PATCH!^)
    REM Clean up temp file
    del "%TEMP%\git_version.txt" >nul 2>nul
)

REM Get git commit hash
git rev-parse --short HEAD > "%TEMP%\git_commit.txt" 2>nul
if errorlevel 1 (
    set "GIT_COMMIT=unknown"
    echo [WARNING] Could not get git commit hash
) else (
    set /p GIT_COMMIT=<"%TEMP%\git_commit.txt"
    del "%TEMP%\git_commit.txt" >nul 2>nul
)

REM Get git branch name
git rev-parse --abbrev-ref HEAD > "%TEMP%\git_branch.txt" 2>nul
if errorlevel 1 (
    set "GIT_BRANCH=unknown"
    echo [WARNING] Could not get git branch name
) else (
    set /p GIT_BRANCH=<"%TEMP%\git_branch.txt"
    del "%TEMP%\git_branch.txt" >nul 2>nul
)

REM Get build timestamp
for /f "tokens=1,2,3,4 delims=/ " %%a in ('date /t') do (
    set "BUILD_DATE=%%a/%%b/%%c"
)
for /f "tokens=1,2 delims=: " %%a in ('time /t') do (
    set "BUILD_TIME=%%a:%%b"
)
set "BUILD_TIMESTAMP=%BUILD_DATE% %BUILD_TIME%"

REM Ensure output directory exists
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Generate version.h file
echo // Auto-generated version header file > "%VERSION_FILE%"
echo // Generated on: %BUILD_TIMESTAMP% >> "%VERSION_FILE%"
echo // Do not edit this file manually! >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo #ifndef VERSION_H >> "%VERSION_FILE%"
echo #define VERSION_H >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo // Version information from git tag >> "%VERSION_FILE%"
echo #define VERSION_MAJOR %VERSION_MAJOR% >> "%VERSION_FILE%"
echo #define VERSION_MINOR %VERSION_MINOR% >> "%VERSION_FILE%"
echo #define VERSION_PATCH %VERSION_PATCH% >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo // Version strings >> "%VERSION_FILE%"
echo #define VERSION_STRING "%VERSION_MAJOR%.%VERSION_MINOR%.%VERSION_PATCH%" >> "%VERSION_FILE%"
echo #define VERSION_TAG "%VERSION_TAG%" >> "%VERSION_FILE%"
echo #define VERSION_FULL "%VERSION_TAG% (%GIT_COMMIT%)" >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo // Git information >> "%VERSION_FILE%"
echo #define GIT_COMMIT "%GIT_COMMIT%" >> "%VERSION_FILE%"
echo #define GIT_BRANCH "%GIT_BRANCH%" >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo // Build information >> "%VERSION_FILE%"
echo #define BUILD_TIMESTAMP "%BUILD_TIMESTAMP%" >> "%VERSION_FILE%"
echo #define BUILD_DATE "%BUILD_DATE%" >> "%VERSION_FILE%"
echo #define BUILD_TIME "%BUILD_TIME%" >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo // Convenience macros >> "%VERSION_FILE%"
echo #define VERSION_AT_LEAST(major, minor, patch) ^( ^\ >> "%VERSION_FILE%"
echo     ^(VERSION_MAJOR ^> ^(major^)^) ^|^| ^\ >> "%VERSION_FILE%"
echo     ^(VERSION_MAJOR == ^(major^) ^&^& VERSION_MINOR ^> ^(minor^)^) ^|^| ^\ >> "%VERSION_FILE%"
echo     ^(VERSION_MAJOR == ^(major^) ^&^& VERSION_MINOR == ^(minor^) ^&^& VERSION_PATCH ^>= ^(patch^)^) ^\ >> "%VERSION_FILE%"
echo ^) >> "%VERSION_FILE%"
echo. >> "%VERSION_FILE%"
echo #endif // VERSION_H >> "%VERSION_FILE%"

echo [INFO] Version header generated: %VERSION_FILE%
echo [INFO] Version: %VERSION_MAJOR%.%VERSION_MINOR%.%VERSION_PATCH%
echo [INFO] Git commit: %GIT_COMMIT%
echo [INFO] Git branch: %GIT_BRANCH%
echo [INFO] Build time: %BUILD_TIMESTAMP%

endlocal
