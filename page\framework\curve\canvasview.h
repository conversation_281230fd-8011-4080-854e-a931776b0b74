/*** 
 * Copyright (c) 2025 , NIO , LTD 
 * All Rights Reserved.
 * Product      : 
 * Component ID : 
 * File Name    : 
 * Description  : 
 * History      : 
 * Version		date		author		context
 * v1.0.0 		<date>		<user>		<context>
 */
#pragma once
#include <QQuickPaintedItem>
#include <QQmlEngine>
#include "datamodel.h"
#include <QMap>
#include <QElapsedTimer>
#include <QTimer>

/**
 * @brief 曲线绘制和交互控件
 * 负责曲线的显示、拖拽和编辑功能
 */
class CanvasView : public QQuickPaintedItem {
    Q_OBJECT
    Q_PROPERTY(DataModel* dataModel READ dataModel WRITE setDataModel NOTIFY dataModelChanged)

public:
    explicit CanvasView(QQuickItem* parent = nullptr);

    // DataModel属性的getter和setter
    DataModel* dataModel() const { return m_model; }
    void setDataModel(DataModel* model);

    // 新增：设置重绘频率
    void setRefreshRate(int hz);

signals:
    // DataModel属性变化信号
    void dataModelChanged();

    // 可调节点位置变化信号
    void adjustablePointChanged(int pointIndex, double frequency, double qValue, double gain);

    // 高通滤波点位置变化信号
    void highpassPointChanged(double frequency);

    // 低通滤波点位置变化信号
    void lowpassPointChanged(double frequency);

private slots:
    // 处理可调节点位置变化
    void onAdjustablePointChanged(int pointIndex, double frequency, double qValue, double gain);

    // 处理高通滤波点位置变化
    void onHighpassPointChanged(double frequency);

    // 处理低通滤波点位置变化
    void onLowpassPointChanged(double frequency);

    // 新增：数据变化处理
    void onDataChanged();

    // 新增：可见性变化处理
    void onVisibilityChanged();

    // 新增：点位置变化处理
    void onPointPositionChanged(int curveIndex, int pointIndex, const QPointF& position);

    // 新增：选中点变化处理
    void onSelectedPointChanged(int curveIndex, int pointIndex);

    // 新增：执行实际重绘
    void performUpdate();

    // 新增：高低通滤波点显示状态变化处理
    void onFilterPointsVisibilityChanged(bool visible);
    void onHighpassPointVisibilityChanged(bool visible);
    void onLowpassPointVisibilityChanged(bool visible);

    // 新增：预设变化处理
    void onCurvePresetChanged(int curveIndex, int presetType, int presetLevel);
    // 注意：不再需要onCurvePresetCleared，因为清除操作现在使用Flat预设

protected:
    // 重写QQuickPaintedItem的绘制方法
    void paint(QPainter* painter) override;
    // 重写鼠标按下事件
    void mousePressEvent(QMouseEvent* event) override;
    // 重写鼠标移动事件
    void mouseMoveEvent(QMouseEvent* event) override;
    // 重写鼠标释放事件
    void mouseReleaseEvent(QMouseEvent* event) override;
    // 重写悬停进入事件
    void hoverEnterEvent(QHoverEvent* event) override;
    // 重写悬停移动事件
    void hoverMoveEvent(QHoverEvent* event) override;
    // 重写悬停离开事件
    void hoverLeaveEvent(QHoverEvent* event) override;

// private:
public:
    DataModel* m_model;                     // 数据模型指针
    QRectF m_dataRect;
    int m_marginLeft;   // 左边距
    int m_marginRight;  // 右边距
    int m_marginTop;    // 上边距
    int m_marginBottom; // 下边距

    bool m_isDraggingHighpass = false;      // 是否正在拖动高通点
    bool m_isDraggingLowpass = false;       // 是否正在拖动低通点
    int m_draggingFilterCurve = -1;         // 正在拖动滤波点的曲线索引
    bool m_isDraggingAdjustablePoint = false; // 是否正在拖动可调节点

    // 新增：悬停状态变量
    int m_hoveredPointIndex = -1;            // 当前悬停的可调节点索引，-1表示无悬停

    // 新增：重绘控制变量
    bool m_needFullRedraw = false;          // 是否需要完全重绘
    int m_lastUpdatedCurve = -1;            // 最近更新的曲线索引
    int m_lastUpdatedPoint = -1;            // 最近更新的点索引

    // 新增：节流重绘相关变量
    QElapsedTimer m_updateTimer;            // 计时器，用于控制重绘频率
    QTimer m_throttleTimer;                 // 定时器，用于延迟重绘
    bool m_updatePending = false;           // 是否有待处理的重绘请求
    int m_refreshRate = 60;                 // 重绘频率，默认60Hz
    int m_refreshInterval = 16;             // 重绘间隔，默认16ms (约1000/60)

    // 数据坐标转换为窗口坐标
    QPointF dataToWidget(const QPointF& point) const;
    // 窗口坐标转换为数据坐标
    QPointF widgetToData(const QPointF& point) const;
    // 查找最近的点
    int findNearestPoint(int curveIndex, const QPointF& targetPoint) const;
    // 计算影响力度
    double calculateInfluence(double distance) const;

    // 新增：更新指定点附近区域
    void updatePointArea(int curveIndex, int pointIndex);

    // 新增：节流更新方法
    void throttledUpdate(const QRect& rect = QRect());

    // 新增：绘制31个可选点
    void drawAdjustablePoints(QPainter& painter);
    // 新增：绘制选中点的F、Q、G信息
    void drawSelectedPointInfo(QPainter& painter);
    // 新增：绘制高低通滤波点
    void drawFilterPoints(QPainter& painter);
    // 新增：处理可选点的点击
    bool handleAdjustablePointClick(const QPointF& clickPos);
    // 新增：处理高低通滤波点的点击
    bool handleFilterPointClick(const QPointF& clickPos);
    // 新增：更新悬停状态
    void updateHoverState(const QPointF& hoverPos);
};
