import QtQuick
import QtQuick.Controls.Basic

SpinBox {
    id: control
    implicitHeight: 24
    editable: true
    // wheelEnabled: true
    leftPadding: 6
    rightPadding: 6
    inputMethodHints: Qt.ImhFormattedNumbersOnly
    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
    font.pixelSize: 12
    font.weight: 700
    opacity: enabled ? 1 : 0.3

    property var values: [4.00, 3.55, 3.20, 2.85, 2.50, 2.25, 2.00, 1.80, 1.60, 1.45, 1.25,
        1.15, 1.00, 0.90, 0.80, 0.70, 0.60, 0.55, 0.50, 0.45, 0.40, 0.36, 0.32, 0.29, 0.27, 0.23, 0.20]
    property var maxValues: [6.50, 3.79, 3.37, 3.09, 2.67, 2.38, 2.12, 1.89, 1.68, 1.50, 1.33,
        1.19, 1.06, 0.95, 0.84, 0.75, 0.66, 0.59, 0.52, 0.47, 0.42, 0.37, 0.33, 0.30, 0.27, 0.24, 0.21]
    property var minValues: [3.80, 3.38, 3.10, 2.68, 2.39, 2.13, 1.90, 1.69, 1.51, 1.34, 1.20,
        1.07, 0.96, 0.85, 0.76, 0.67, 0.60, 0.53, 0.48, 0.43, 0.38, 0.34, 0.31, 0.28, 0.25, 0.22, 0.00]

    onValueChanged: {
        contentInput.text = textFromValue(value, locale)
    }

    from: 0
    to: 26

    validator: RegularExpressionValidator { regularExpression: /(\d?(\.\d{0,2})?)?/ }

    textFromValue: function(value, locale) {
        return control.values[value].toFixed(2).toString()
    }

    valueFromText: function(text, locale) {
        var txtValue = Number(text)
        if(txtValue > control.maxValues[0])
        {
            return 0;
        }
        if(txtValue < control.minValues[26])
        {
            return 26;
        }

        for(var index = 0; index < 27; index++)
        {
            if((txtValue <= control.maxValues[index]) &&
                    (txtValue >= control.minValues[index]))
            {
                return index;
            }
        }
    }

    contentItem: Item {
        property alias text: contentInput.text

        TextInput {
            id: contentInput
            anchors.fill: parent
            color: "#E8E8E8"
            font: control.font
            horizontalAlignment: Qt.AlignHCenter
            verticalAlignment: Qt.AlignVCenter
            readOnly: !control.editable
            validator: control.validator
            inputMethodHints: control.inputMethodHints
            rightPadding: unitTxt.width

            text: control.textFromValue(control.value, control.locale)

            onEditingFinished: {
                // onTextEdited: {
                var modifyText = (("-" === contentInput.text) || ("" === contentInput.text)) ?
                            control.textFromValue(control.value, control.locale) : contentInput.text
                var val = control.valueFromText(modifyText, control.locale)
                if (val < Math.min(control.from, control.to)) {
                    contentInput.text = control.textFromValue(Math.min(control.from, control.to), control.locale)
                }
                else if(val > Math.max(control.from, control.to)) {
                    contentInput.text = control.textFromValue(Math.max(control.from, control.to), control.locale)
                }
                else {
                    contentInput.text = control.textFromValue(val, control.locale)
                }
            }
        }

        Text {
            id: unitTxt
            anchors.left: contentInput.horizontalCenter
            anchors.leftMargin: (contentInput.contentWidth - unitTxt.width) / 2
            anchors.verticalCenter: parent.verticalCenter
            color: "#E8E8E8"
            font: control.font
            verticalAlignment: Qt.AlignVCenter
            text: "V"
        }
    }

    background: Rectangle {
        color: "#282828"
        border.width: 1
        border.color: control.hovered ? "#7C8088" : control.pressed ? "#C4C8D0" : "#646870"
    }

    up.indicator: Item {}
    down.indicator: Item {}
}
