#ifndef HIDSENDER_H
#define HIDSENDER_H

#include <QObject>

#include "LibUsb/include/hidapi.h"
#include "HidDef.h"

class HidSender : public QObject
{
    Q_OBJECT

public:
    explicit HidSender(QObject *parent = nullptr);
    ~HidSender();

    void setHidHandle(hid_device* handle);
    virtual int sendMessage(const QByteArray &data);
    virtual int receiveMessage(QByteArray &data);

public slots:
    void onSendMessage(const QByteArray &data);

signals:
    void receivedMessageSig(const QByteArray &data);
    void receivedErrorSig(int error);

private:
    hid_device* mHidHandle;
};

#endif // HIDSENDER_H
