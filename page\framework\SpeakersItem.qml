import QtQuick
import QtQuick.Controls.Basic
import Qt5Compat.GraphicalEffects

Item {
    width: 222
    height: 290

    property var colorA: ["#00C8E8", "#00F090", "#D744D6", "#A870FF", "#E0A000",
        "#D0D000", "#0088FF", "#FF80C0", "#00B0A0", "#F05878"]
    property int speakId: commonCtrl.getSpeakerId(dataMap["channel" + dataMap["uiDataMap"]["selectedChannel"] + "DataMap"]["type"])

    Image {
        anchors.centerIn: parent
        z: 0
        source: "qrc:/Image/car.png"
    }

    Item {
        anchors.centerIn: parent
        width: 222
        height: 284

        Repeater {
            model: ListModel {
                ListElement {
                    itemX: 0
                    itemY: 42
                    speaker: 0      //fl tweeter / M&T
                }
                ListElement {
                    itemX: 0
                    itemY: 76
                    speaker: 1      //fl midrange / full
                }
                ListElement {
                    itemX: 0
                    itemY: 110
                    speaker: 2      //fl woofer / M&WF
                }
                ListElement {
                    itemX: 0
                    itemY: 144
                    speaker: 3      //rl tweeter / M&T
                }
                ListElement {
                    itemX: 0
                    itemY: 178
                    speaker: 4      //rl midrange / full
                }
                ListElement {
                    itemX: 0
                    itemY: 212
                    speaker: 5      //rl woofer / M&WF
                }
                ListElement {
                    itemX: 122
                    itemY: 0
                    speaker: 17     //user config 1
                    direction: 4
                }
            }

            delegate: Button {
                id: leftSpeaker
                x: itemX
                y: itemY
                z: 1
                width: 46
                height: 30
                checkable: true
                checked: (speaker === speakId)
                autoExclusive: true
                visible: (-1 !== channel)

                property int channel: dataMap["uiDataMap"]["speaker" + speaker + "Channel"]
                property color checkedColor: (-1 !== channel) ? colorA[channel] : "transparent"

                onClicked: {
                    // speakId = speaker
                    dataMap["uiDataMap"]["selectedChannel"] = channel
                }

                background: Rectangle {
                    color: "#343840"
                    border.width: 1
                    border.color: leftSpeaker.checked ? leftSpeaker.checkedColor : (leftSpeaker.hovered ? "#5C6068" : "#9498A0")
                    radius: 4

                    Text {
                        id: leftTitle
                        anchors.left: parent.left
                        anchors.leftMargin: 5
                        y:4
                        width: 14
                        height: 8
                        color: "#C8C8C8"
                        font.family: "Segoe UI"
                        font.pixelSize: 10
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: "CH"
                    }

                    Text {
                        anchors.horizontalCenter: leftTitle.horizontalCenter
                        anchors.top: leftTitle.bottom
                        anchors.topMargin: 2
                        width: 14
                        height: 10
                        color: "#E8E8E8"
                        font.family: "Segoe UI"
                        font.pixelSize: 12
                        font.weight: 700
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: (channel + 1)
                    }

                    Image {
                        anchors.right: parent.right
                        anchors.rightMargin: 3
                        y: 5
                        source: ((0 === speaker) || (3 === speaker)) ? "qrc:/Image/speakerLeftH.svg" : "qrc:/Image/speakerLeft.svg"
                        ColorOverlay {
                            anchors.fill: parent
                            source: parent
                            color: leftSpeaker.checked ? leftSpeaker.checkedColor : "#7C8088"
                        }
                    }
                }
            }
        }

        Repeater {
            model: ListModel {
                ListElement {
                    itemX: 176
                    itemY: 42
                    speaker: 6      //fr tweeter / M&T
                }
                ListElement {
                    itemX: 176
                    itemY: 76
                    speaker: 7      //fr midrange / full
                }
                ListElement {
                    itemX: 176
                    itemY: 110
                    speaker: 8      //fr woofer / M&WF
                }
                ListElement {
                    itemX: 176
                    itemY: 144
                    speaker: 9      //rr tweeter / M&T
                }
                ListElement {
                    itemX: 176
                    itemY: 178
                    speaker: 10     //rr midrange / full
                }
                ListElement {
                    itemX: 176
                    itemY: 212
                    speaker: 11     //rr woofer / M&WF
                }
                ListElement {
                    itemX: 176
                    itemY: 0
                    speaker: 18     //user config 2
                }
            }

            delegate: Button {
                id: rightSpeaker
                x: itemX
                y: itemY
                z: 1
                width: 46
                height: 30
                checkable: true
                checked: (speaker === speakId)
                autoExclusive: true
                visible: (-1 !== channel)

                property int channel: dataMap["uiDataMap"]["speaker" + speaker + "Channel"]
                property color checkedColor: (-1 !== channel) ? colorA[channel] : "transparent"

                onClicked: {
                    // speakId = speaker
                    dataMap["uiDataMap"]["selectedChannel"] = channel
                }

                background: Rectangle {
                    color: "#343840"
                    border.width: 1
                    border.color: rightSpeaker.checked ? rightSpeaker.checkedColor : (rightSpeaker.hovered ? "#5C6068" : "#9498A0")
                    radius: 4

                    Text {
                        id: rightTitle
                        anchors.right: parent.right
                        anchors.rightMargin: 5
                        y:4
                        width: 14
                        height: 8
                        color: "#C8C8C8"
                        font.family: "Segoe UI"
                        font.pixelSize: 10
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: "CH"
                    }

                    Text {
                        anchors.horizontalCenter: rightTitle.horizontalCenter
                        anchors.top: rightTitle.bottom
                        anchors.topMargin: 2
                        width: 14
                        height: 10
                        color: "#E8E8E8"
                        font.family: "Segoe UI"
                        font.pixelSize: 12
                        font.weight: 700
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: (channel + 1)
                    }

                    Image {
                        anchors.left: parent.left
                        anchors.leftMargin: 3
                        y: 5
                        source: ((6 === speaker) || (9 === speaker)) ? "qrc:/Image/speakerRightH.svg" : "qrc:/Image/speakerRight.svg"
                        ColorOverlay {
                            anchors.fill: parent
                            source: parent
                            color: rightSpeaker.checked ? rightSpeaker.checkedColor : "#7C8088"
                        }
                    }
                }
            }
        }

        Repeater {
            model: ListModel {
                ListElement {
                    itemX: 28
                    itemY: 254
                    speaker: 12     //l subwoofer
                }
                ListElement {
                    itemX: 86
                    itemY: 254
                    speaker: 13     //subwoofer
                }
                ListElement {
                    itemX: 144
                    itemY: 254
                    speaker: 14     //r subwoofer
                }
                ListElement {
                    itemX: 86
                    itemY: 76
                    speaker: 15     //c full / woofer
                }
                ListElement {
                    itemX: 86
                    itemY: 42
                    speaker: 16     //c tweeter
                }
            }

            delegate: Button {
                id: frLeftSpeaker
                x: itemX
                y: itemY
                z: 1
                width: 50
                height: 30
                checkable: true
                checked: (speaker === speakId)
                autoExclusive: true
                visible: (-1 !== channel)

                property int channel: dataMap["uiDataMap"]["speaker" + speaker + "Channel"]
                property color checkedColor: (-1 !== channel) ? colorA[channel] : "transparent"

                onClicked: {
                    // speakId = speaker
                    dataMap["uiDataMap"]["selectedChannel"] = channel
                }

                background: Rectangle {
                    color: "#343840"
                    border.width: 1
                    border.color: frLeftSpeaker.checked ? frLeftSpeaker.checkedColor : (frLeftSpeaker.hovered ? "#5C6068" : "#9498A0")
                    radius: 4

                    Text {
                        id: frLeftTitle
                        anchors.left: parent.left
                        anchors.leftMargin: 5
                        y:4
                        width: 14
                        height: 8
                        color: "#C8C8C8"
                        font.family: "Segoe UI"
                        font.pixelSize: 10
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: "CH"
                    }

                    Text {
                        anchors.horizontalCenter: frLeftTitle.horizontalCenter
                        anchors.top: frLeftTitle.bottom
                        anchors.topMargin: 2
                        width: 14
                        height: 10
                        color: "#E8E8E8"
                        font.family: "Segoe UI"
                        font.pixelSize: 12
                        font.weight: 700
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: (channel + 1)
                    }

                    Image {
                        anchors.left: frLeftTitle.right
                        anchors.leftMargin: 2
                        y: 5
                        source: (15 === speaker) ? "qrc:/Image/speakerFront.svg" :
                                                   ((16 === speaker) ? "qrc:/Image/speakerFrontH.svg" : "qrc:/Image/speakerRear.svg")
                        ColorOverlay {
                            anchors.fill: parent
                            source: parent
                            color: frLeftSpeaker.checked ? frLeftSpeaker.checkedColor : "#7C8088"
                        }
                    }
                }
            }
        }

        // Repeater {
        //     model: ListModel {
        //     }

        //     delegate: Button {
        //         id: frRightSpeaker
        //         x: itemX
        //         y: itemY
        //         z: 1
        //         width: 50
        //         height: 30
        //         checkable: true
        //         checked: (speaker === speakId)
        //         autoExclusive: true
        //         visible: (-1 !== channel)

        //         property int channel: dataMap["uiDataMap"]["speaker" + speaker + "Channel"]
        //         property color checkedColor: (-1 !== channel) ? colorA[channel] : "transparent"

        //         onClicked: {
        //             // speakId = speaker
        //             dataMap["uiDataMap"]["selectedChannel"] = channel
        //         }

        //         background: Rectangle {
        //             color: "#343840"
        //             border.width: 1
        //             border.color: frRightSpeaker.checked ? frRightSpeaker.checkedColor : (frRightSpeaker.hovered ? "#5C6068" : "#9498A0")
        //             radius: 4

        //             Text {
        //                 id: frRightTitle
        //                 anchors.left: frRightIcon.right
        //                 anchors.leftMargin: 2
        //                 y:4
        //                 width: 14
        //                 height: 8
        //                 color: "#C8C8C8"
        //                 font.family: "Segoe UI"
        //                 font.pixelSize: 10
        //                 horizontalAlignment: Text.AlignHCenter
        //                 verticalAlignment: Text.AlignVCenter
        //                 text: "CH"
        //             }

        //             Text {
        //                 anchors.horizontalCenter: frRightTitle.horizontalCenter
        //                 anchors.top: frRightTitle.bottom
        //                 anchors.topMargin: 2
        //                 width: 14
        //                 height: 10
        //                 color: "#E8E8E8"
        //                 font.family: "Segoe UI"
        //                 font.pixelSize: 12
        //                 font.weight: 700
        //                 horizontalAlignment: Text.AlignHCenter
        //                 verticalAlignment: Text.AlignVCenter
        //                 text: (channel + 1)
        //             }

        //             Image {
        //                 id: frRightIcon
        //                 anchors.left: parent.left
        //                 anchors.leftMargin: 3
        //                 y: 5
        //                 source: (16 === speaker) ? "qrc:/Image/speakerFrontH.svg" : "qrc:/Image/speakerRear.svg"
        //                 ColorOverlay {
        //                     anchors.fill: parent
        //                     source: parent
        //                     color: frRightSpeaker.checked ? frRightSpeaker.checkedColor : "#7C8088"
        //                 }
        //             }
        //         }
        //     }
        // }
    }
}
