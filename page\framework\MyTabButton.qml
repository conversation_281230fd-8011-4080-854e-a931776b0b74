import QtQuick
import QtQuick.Controls

TabButton {
    id: control

    property int index: 0
    property int currentIndex: bar.currentIndex
    property bool selected: (index === currentIndex)

    contentItem: Item {
        Text {
            anchors.centerIn: parent
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            text: control.text
        }
    }

    background: Rectangle {
        color: checked ? "#3C4048" : "#484C54"
    }
}
