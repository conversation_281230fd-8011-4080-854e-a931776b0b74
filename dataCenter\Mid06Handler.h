#ifndef MID06HANDLER_H
#define MID06HANDLER_H

#include "DataHandlerAbstract.h"

class Mid06Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid06Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    QByteArray send01Data(const RemoteOperation0102 &data);
    QByteArray send02Data();
    QByteArray send03Data(const RemoteOperation0304 &data);
    QByteArray send04Data();
    QByteArray send05Data(const RemoteOperation0506 &data);
    QByteArray send06Data(uint8_t num);

signals:
    void remoteBrightnessChanged(uint8_t value);
    void remoteDimmerChanged(uint8_t value);
    void remotePolarityChanged(uint8_t value);
    void remoteDimmerBrightnessChanged(uint8_t value);

    void remoteModelChanged(uint8_t value);

    void remoteShortCutChanged(uint8_t id, uint8_t type, uint8_t memory);

private:
    void parse02Data(const QByteArray &data);
    void parse04Data(const QByteArray &data);
    void parse06Data(const QByteArray &data);

private:
    RemoteOperation0102 mRemoteOperation0102;
    RemoteOperation0304 mRemoteOperation0304;
    QVector<RemoteShortCutInfo> mRemoteShortCuts;
};

#endif // MID06HANDLER_H
