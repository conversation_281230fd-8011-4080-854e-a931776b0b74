import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "../framework"

SubWindow {
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 1120
    contentHeight: 640
    titleStr: qsTr("Device Setting")
    confirmVisible: false
    cancelStr: qsTr("Close")

    onClickCancel: clickClose()

    contentItem: Item {
        id: root
        property int itemWidth: 86
        property int padding: 1

        Rectangle {
            id: barBg
            width: ((root.itemWidth + root.padding) * ((1 !== dataMap["uiDataMap"]["deviceLevel"]) ? 3 : 2) + root.padding)
            height: 26
            color: "#5C6068"
        }

        TabBar {
            id: bar
            width: ((root.itemWidth + padding) * 3 + padding)
            height: 26
            contentWidth: (width - padding * 2)
            contentHeight: height - padding
            padding: root.padding
            currentIndex: 0

            background: Rectangle {
                color: "transparent"
            }

            Repeater {
                model: 3

                MyTabButton {
                    text: qsTr("Setting") + (modelData + 1).toString()
                    selected: (modelData === bar.currentIndex)
                    visible: ((1 !== dataMap["uiDataMap"]["deviceLevel"]) || (2 > modelData))
                }
            }
        }

        StackLayout {
            id: settings
            anchors.left: bar.left
            anchors.top: bar.bottom
            width: parent.width
            currentIndex: bar.currentIndex

            Rectangle {
                id: setting1
                width: parent.width
                height: 520
                color: "#3C4048"
                border.color: "#5C6068"
                border.width: 1

                Rectangle {
                    x: root.padding
                    width: root.itemWidth
                    height: parent.border.width
                    color: "#3C4048"
                    visible: (0 === settings.currentIndex)
                }

                Setting1 {

                }
            }

            Rectangle {
                id: setting2
                width: parent.width
                height: 520
                color: "#3C4048"
                border.color: "#5C6068"
                border.width: 1

                Rectangle {
                    x: root.padding + root.itemWidth + root.padding
                    width: root.itemWidth
                    height: parent.border.width
                    color: "#3C4048"
                    visible: (1 === settings.currentIndex)
                }

                Setting2 {

                }
            }

            Rectangle {
                id: setting3
                width: parent.width
                height: 520
                color: "#3C4048"
                border.color: "#5C6068"
                border.width: 1
                visible: ((1 !== dataMap["uiDataMap"]["deviceLevel"]) && (2 === settings.currentIndex))

                Rectangle {
                    x: root.padding + (root.itemWidth + root.padding) * 2
                    width: root.itemWidth
                    height: parent.border.width
                    color: "#3C4048"
                    visible: (2 === settings.currentIndex)
                }

                Setting3 {

                }
            }
        }
    }
}
