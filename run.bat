@echo off
setlocal

:: 检查build目录是否存在
if not exist "build" (
    echo Building project...
    call build.bat
    if errorlevel 1 (
        echo Build failed!
        pause
        exit /b 1
    )
)

:: 检查可执行文件是否存在
if not exist "build\line_chart.exe" (
    echo Building project...
    call build.bat
    if errorlevel 1 (
        echo Build failed!
        pause
        exit /b 1
    )
)

:: 运行程序
echo Running line_chart...
cd build
start line_chart.exe
cd .. 