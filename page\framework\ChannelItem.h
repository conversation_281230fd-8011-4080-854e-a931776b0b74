#ifndef CHANNELITEM_H
#define CHANNELITEM_H

#include <QFrame>
#include "../CommonController.h"
#include <../../dataCenter/DataHandlerDef.h>

namespace Ui {
class ChannelItem;
}

class ChannelItem : public QFrame
{
    Q_OBJECT

public:
    explicit ChannelItem(QWidget *parent = nullptr, int channel = 0);
    ~ChannelItem();

    int channel();

    bool highPassEnabled();
    void setHighPassEnabled(bool isEnabled);

    bool lowPassEnabled();
    void setLowPassEnabled(bool isEnabled);

    bool isMuted();
    void setMuted(bool isMuted);

    SpeakerEnum sp();
    void setSp(SpeakerEnum value);

    int volumn10();
    void setVolumn10(int value);

    bool isReversed();
    void setReversed(bool isReversed);

    int linkChannel();
    void setLinkChannel(int channel);

    void setSpeakerEnabled(SpeakerEnum speaker, bool isEnabled);

    void setMaskVisible(bool isVisible);

signals:
    void highPassEnabledChanged(bool isEnabled);
    void lowPassEnabledChanged(bool isEnabled);
    void isMutedChanged(bool isMuted);
    void spChanged(SpeakerEnum sp);
    void volumn10Changed(int value);
    void isReversedChanged(bool isReversed);
    void linkChannelChanged(int channel);

    void linkBtnClicked(int channel, int linkChannel);

private:
    Ui::ChannelItem *ui;
    QMap<SpeakerEnum, QAction*> mSpeakerActions;

    int mChannel;
    bool mHighPassEnabled;
    bool mLowPassEnabled;
    bool mIsMuted;
    SpeakerEnum mSp;
    int mVolumn10;  // 10 times volumn
    bool mIsReversed;
    int mLinkChannel;
};

#endif // CHANNELITEM_H
