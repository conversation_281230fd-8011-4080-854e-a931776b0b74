#ifndef MID04HANDLER_H
#define MID04HANDLER_H

#include "DataHandlerAbstract.h"

class Mid04Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid04Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    QByteArray send03Data(const SystemOperation0304 &data);
    QByteArray send04Data();
    QByteArray send07Data(const SystemOperation0708 &data);
    QByteArray send08Data();
    QByteArray send09Data();

    QByteArray send0AData(uint16_t index);
    int send0BData(const QByteArray &data, QVector<QByteArray> &frameDatas);

    const SystemOperation0304& getSystemOperation0304();
    const SystemOperation0708& getSystemOperation0708();

signals:
    void naviMixEnableChanged(uint8_t isEnabled);
    void naviSensitivityChanged(uint8_t sensitivity);
    void naviAttenuationChanged(uint8_t attenuation);
    void naviDurationChanged(uint8_t duration);
    void externalAttenuationChanged(uint8_t attenuation);
    void externalPolarityChanged(uint8_t polarity);
    void externalDspAttenuationChanged(uint8_t attenuation);
    void delayUnitChanged(uint8_t unit);

    void highGainChanged(uint8_t gain);
    void rcaGainChanged(uint8_t gain);
    void auxGainChanged(uint8_t gain);
    void btGainChanged(uint8_t gain);
    void spdifGainChanged(uint8_t gain);
    void usbGainChanged(uint8_t gain);

    void savePackageCountChanged(int count, const QByteArray data);
    void savePackageDataChanged(uint16_t index, const QByteArray data);
    void loadPackageReplyIndex(uint16_t index);

private:
    void parse04Data(const QByteArray &data);
    void parse08Data(const QByteArray &data);
    void parse0AData(const QByteArray &data);
    void parse0BData(const QByteArray &data);

private:
    SystemOperation0304 mSystemOperation0304;
    SystemOperation0708 mSystemOperation0708;
};

#endif // MID04HANDLER_H
