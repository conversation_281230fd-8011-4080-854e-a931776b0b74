import QtQuick
import QtQuick.Controls.Basic

RadioButton {
    id: control
    implicitWidth: indicator.width + textItem.contentWidth + spacing + leftPadding + rightPadding
    implicitHeight: 14
    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
    font.pixelSize: 12
    spacing: 8
    opacity: enabled ? 1 : 0.3
    padding: 0

    indicator: Rectangle {
        implicitWidth: 14
        implicitHeight: implicitWidth
        anchors.left: control.left
        // anchors.leftMargin: control.leftPadding
        anchors.verticalCenter: control.verticalCenter
        color: "#484C54"
        border.width: 1
        border.color: "#646870"
        radius: height / 2

        Rectangle {
            anchors.centerIn: parent
            width: 8
            height: width
            color: "#AACCEE"
            radius: height / 2
            visible: control.checked
        }
    }

    contentItem: Text {
        id: textItem
        color: "#E8E8E8"
        font: control.font
        text: control.text
        verticalAlignment: Text.AlignVCenter
        bottomPadding: 3
        leftPadding: control.indicator.width + control.spacing
    }
}
