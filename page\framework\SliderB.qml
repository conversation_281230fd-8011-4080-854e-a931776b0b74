import QtQuick
import QtQuick.Controls.Basic
import Qt5Compat.GraphicalEffects

Slider {
    id: control
    padding: 1
    // wheelEnabled: true
    snapMode: Slider.SnapAlways

    MouseArea {
        anchors.fill: parent
        onPressed: (mouse)=> {
                       if(mouseX < handle.x)
                       {
                           // control.decrease()
                           control.focus = true
                       }
                       else if(mouseX > (handle.x + handle.width))
                       {
                           // control.increase()
                           control.focus = true
                       }
                       else
                       {
                           mouse.accepted = false
                       }
                   }
    }

    background: Rectangle {
        x: (Qt.Horizontal === control.orientation) ?
               control.leftPadding : (control.leftPadding + control.availableWidth / 2 - width / 2)
        y: (Qt.Horizontal === control.orientation) ?
               (control.topPadding + control.availableHeight / 2 - height / 2) : control.topPadding
        width: (Qt.Horizontal === control.orientation) ? control.availableWidth : 6
        height: (Qt.Horizontal === control.orientation) ? 6 : control.availableHeight
        radius: (Qt.Horizontal === control.orientation) ? (height / 2) : (width / 2)
        color: "#181818"
        border.color: "#5C6068"
        border.width: 1

        Rectangle {
            x: parent.border.width
            y: parent.border.width
            width: (Qt.Horizontal === control.orientation) ?
                       (control.visualPosition * (parent.width - parent.border.width * 2))
                     : (parent.width - parent.border.width * 2)
            height: (Qt.Horizontal === control.orientation) ?
                        (parent.height - parent.border.width * 2)
                      : (control.visualPosition * (parent.height - parent.border.width * 2))
            radius: height / 2
            color: "#C8D8FF"
        }
    }

    handle: Rectangle {
        x: (Qt.Horizontal === control.orientation) ?
               (control.leftPadding + control.visualPosition * (control.availableWidth - width))
             : (control.leftPadding + control.availableWidth / 2 - width / 2)
        y: (Qt.Horizontal === control.orientation) ?
               (control.topPadding + control.availableHeight / 2 - height / 2)
             : (control.topPadding + control.visualPosition * (control.availableHeight - height))
        implicitWidth: 18
        implicitHeight: 18
        radius: width / 2
        color: (Qt.Horizontal === control.orientation) ? (control.pressed ? "#C0C0C0" : "#A0A0A0") : "#767676"
        layer.enabled: true
        layer.effect: DropShadow {
            transparentBorder: false
            horizontalOffset: 0
            verticalOffset: 2
            radius: 2
            color: "#80000000"
        }
    }

    Keys.onPressed: (event)=> {
        switch(event.key)
        {
        case Qt.Key_Up:
        case Qt.Key_Right:
        {
            // control.increase()
            event.accepted = true
            break
        }
        case Qt.Key_Down:
        case Qt.Key_Left:
        {
            // control.decrease()
            event.accepted = true
            break
        }
        }
    }
}
