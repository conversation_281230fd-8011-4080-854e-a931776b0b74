<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1366</width>
    <height>720</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1280</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
	background-color: #2C3038;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true">font: 12px &quot;Segoe UI&quot;;
color: #E8E8E8;

QPushButton {
	background-color: #484C54;
	border: 1px solid #646870;
}
QPushButton:hover {
	background-color: #5C6068;
	border: 1px solid #7C8088;
}
QPushButton:pressed {
	background-color: #3C4048;
	border: 1px solid #646870;
}</string>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QScrollArea" name="scrollArea">
      <property name="styleSheet">
       <string notr="true">QScrollBar:vertical {
	background-color: #007C8088;
	border: 0px;
	width: 10px;
}
QScrollBar:horizontal {
	background-color: #007C8088;
	border: 0px;
	height: 10px;
}
QScrollBar::handle {
	background-color: #7C8088;
	border: 0px;
	border-radius: 5px;
}
QScrollBar::handle:hover, QScrollBar::handle:pressed {
	background-color: #9498A0;
}
QScrollBar::add-line, QScrollBar::sub-line {
	width: 0px;
	height: 0px;
}</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::NoFrame</enum>
      </property>
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
      </property>
      <widget class="QWidget" name="scrollAreaWidgetContents">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>1366</width>
         <height>720</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>1366</width>
         <height>720</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">#scrollAreaWidgetContents {
	background-color: #2C3038;
}</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_1">
        <property name="spacing">
         <number>10</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="PageTitle" name="titleBar">
            <property name="minimumSize">
             <size>
              <width>1366</width>
              <height>48</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>48</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="PageToolBar" name="toolBar">
            <property name="minimumSize">
             <size>
              <width>1366</width>
              <height>48</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>48</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: #50545C;</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QQuickWidget" name="mainLayout">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="resizeMode">
           <enum>QQuickWidget::ResizeMode::SizeRootObjectToView</enum>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QQuickWidget</class>
   <extends>QWidget</extends>
   <header location="global">QtQuickWidgets/QQuickWidget</header>
  </customwidget>
  <customwidget>
   <class>PageTitle</class>
   <extends>QFrame</extends>
   <header>./page/framework/PageTitle.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PageToolBar</class>
   <extends>QFrame</extends>
   <header>./page/framework/PageToolBar.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
