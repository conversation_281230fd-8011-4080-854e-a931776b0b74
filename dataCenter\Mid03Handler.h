#ifndef MID03HANDLER_H
#define MID03HANDLER_H

#include <QMap>

#include "DataHandlerAbstract.h"

class Mid03Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid03Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    void onInit(int deviceLevel);

    QByteArray send01Data(const AudioOperation0102 &data);
    QByteArray send02Data();
    QByteArray send03Data(const AudioOperation0304 &data);
    QByteArray send04Data();
    QByteArray send05Data(const AudioOperation0506 &data);
    QByteArray send06Data(uint8_t source, uint8_t channel, uint8_t num);
    QByteArray send07Data(const AudioOperation0708 &data);
    QByteArray send08Data(uint8_t channel, uint8_t band = 0x00, uint8_t num = EQ_BAND_MAX);
    QByteArray send09Data(const AudioOperation09 &data);
    QByteArray send0AData(const AudioOperation0A0B &data);
    QByteArray send0BData(uint8_t type, uint8_t memoryId = 0x00, uint8_t num = MEMORY_MAX);
    QByteArray send0CData(const AudioOperation0C &data);
    QByteArray send0DData(const AudioOperation0D &data);
    QByteArray send0EData(const AudioOperation0E0F &data);
    QByteArray send0FData();
    QByteArray send10Data();

    const AudioOperation0102& getAudioOperation0102();
    const AudioOperation0304& getAudioOperation0304();
    const QMap<uint8_t, QMap<uint8_t, QVector<Mix>>>& getAudioOperation0506Map();
    const QMap<uint8_t, QVector<EqParm>>& getAudioOperation0708Map();
    const QMap<uint8_t, QVector<MemorySetting>>& getAudioOperation0A0B();
    const AudioOperation0E0F& getAudioOperation0E0F();

signals:
    void mainGainChanged(uint16_t gain);
    void mainMuteChanged(uint8_t isMute);
    void dspGainChanged(uint8_t gain);
    void dspMuteChanged(uint8_t isMute);
    void bassLevelChanged(uint8_t level);
    void bassMuteChanged(uint8_t isMute);

    void currentSourceChanged(uint8_t source);
    void currentMemoryChanged(uint8_t memory);

    void mixGainChanged(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t gain);
    void mixEnableChanged(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t enable);

    void eqFreqChanged(uint8_t channel, uint8_t band, uint16_t freq);
    void eqQValueChanged(uint8_t channel, uint8_t band, uint16_t qValue);
    void eqGainChanged(uint8_t channel, uint8_t band, uint16_t gain);
    void eqTypeChanged(uint8_t channel, uint8_t band, uint8_t type);
    void eqEnableChanged(uint8_t channel, uint8_t band, uint8_t enable);

    void memoryEnableChanged(uint8_t type, uint8_t memoryId, uint8_t enable);
    void memoryNameChanged(uint8_t type, uint8_t memoryId, QString name);

    void presetModeChanged(uint8_t mode);
    void presetLevelChanged(uint8_t level);

private:
    void parse02Data(const QByteArray &data);
    void parse04Data(const QByteArray &data);
    void parse06Data(const QByteArray &data);
    void parse08Data(const QByteArray &data);
    void parse0BData(const QByteArray &data);
    void parse0FData(const QByteArray &data);

private:
    int mDeviceLevel = 0;

    AudioOperation0102 mAudioOperation0102;
    AudioOperation0304 mAudioOperation0304;
    QMap<uint8_t, QMap<uint8_t, QVector<Mix>>> mMixMap;
    QMap<uint8_t, QVector<EqParm>> mEqParmMap;
    QMap<uint8_t, QVector<MemorySetting>> mMemorySettingMap;
    AudioOperation0E0F mAudioOperation0E0F;
};

#endif // MID03HANDLER_H
