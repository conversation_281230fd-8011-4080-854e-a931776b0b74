/**
 * @file canvas_hover_test.cpp
 * @brief Canvas悬停功能测试示例
 * 演示鼠标悬停在可选点上时的颜色变化功能
 */

#include "canvasview.h"
#include "datamodel.h"
#include <QDebug>
#include <QHoverEvent>
#include <QPointF>

/**
 * @brief 演示Canvas悬停功能
 */
void demonstrateCanvasHoverFeature()
{
    qDebug() << "\n=== Canvas悬停功能演示 ===";

    // 创建数据模型和Canvas视图
    DataModel dataModel;
    CanvasView canvasView;
    canvasView.setDataModel(&dataModel);

    qDebug() << "\n1. 初始化测试环境:";
    qDebug() << "设置活动曲线为通道0...";
    dataModel.setSelectedCurve(0);
    
    qDebug() << "确保曲线可见...";
    dataModel.setCurveVisible(0, true);

    qDebug() << "\n2. 测试悬停状态变化:";
    
    // 模拟鼠标悬停在第一个可调节点上
    QPointF point1Position = dataModel.getAdjustablePoints(0)[0];
    QPointF widget1Position = canvasView.dataToWidget(point1Position);
    
    qDebug() << "模拟鼠标悬停在第1个可调节点上...";
    qDebug() << "点的数据坐标:" << point1Position;
    qDebug() << "点的窗口坐标:" << widget1Position;
    
    // 创建悬停事件并处理
    QHoverEvent hoverEnterEvent(QEvent::HoverEnter, widget1Position, QPointF());
    canvasView.hoverEnterEvent(&hoverEnterEvent);
    
    qDebug() << "当前悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 第1个点应该显示悬停颜色（曲线颜色，80%不透明度）";

    qDebug() << "\n3. 测试悬停移动:";
    
    // 模拟鼠标移动到第二个可调节点
    QPointF point2Position = dataModel.getAdjustablePoints(0)[1];
    QPointF widget2Position = canvasView.dataToWidget(point2Position);
    
    qDebug() << "模拟鼠标移动到第2个可调节点...";
    QHoverEvent hoverMoveEvent(QEvent::HoverMove, widget2Position, widget1Position);
    canvasView.hoverMoveEvent(&hoverMoveEvent);
    
    qDebug() << "当前悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 第2个点应该显示悬停颜色，第1个点恢复正常颜色";

    qDebug() << "\n4. 测试点击后悬停状态清除:";
    
    // 模拟点击第二个点
    qDebug() << "模拟点击第2个可调节点...";
    bool clickHandled = canvasView.handleAdjustablePointClick(widget2Position);
    
    qDebug() << "点击是否被处理:" << clickHandled;
    qDebug() << "当前选中点索引:" << dataModel.getSelectedPointIndex();
    qDebug() << "当前悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 第2个点应该被选中，悬停状态应该被清除";

    qDebug() << "\n5. 测试悬停离开:";
    
    // 模拟鼠标离开Canvas区域
    qDebug() << "模拟鼠标离开Canvas区域...";
    QHoverEvent hoverLeaveEvent(QEvent::HoverLeave, QPointF(), widget2Position);
    canvasView.hoverLeaveEvent(&hoverLeaveEvent);
    
    qDebug() << "当前悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 悬停状态应该被清除（索引为-1）";

    qDebug() << "\n6. 测试悬停范围:";
    
    // 测试悬停检测范围
    QPointF nearPoint = widget1Position + QPointF(12, 0); // 距离第1个点12像素
    QPointF farPoint = widget1Position + QPointF(20, 0);  // 距离第1个点20像素
    
    qDebug() << "测试悬停检测范围（15像素）...";
    
    // 测试近距离点（应该触发悬停）
    canvasView.updateHoverState(nearPoint);
    qDebug() << "距离12像素时的悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 应该检测到悬停（索引为0）";
    
    // 测试远距离点（不应该触发悬停）
    canvasView.updateHoverState(farPoint);
    qDebug() << "距离20像素时的悬停点索引:" << canvasView.m_hoveredPointIndex;
    qDebug() << "→ 不应该检测到悬停（索引为-1）";

    qDebug() << "\n=== Canvas悬停功能演示完成 ===\n";
}

/**
 * @brief 主函数，运行悬停功能演示
 */
int main()
{
    demonstrateCanvasHoverFeature();
    return 0;
}
