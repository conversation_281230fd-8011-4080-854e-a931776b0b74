#include "common.h"
#include "butterworth_filter.h"
#include "bessel_filter.h"
#include "linkwitz_riley_filter.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#ifdef __cplusplus
#include <complex>
#else
#include <complex.h>
#endif

// 创建复数的辅助函数，支持不同平台
static double_complex create_complex(double real, double imag) {
#if defined(_MSC_VER) || defined(__cplusplus)
    return double_complex(real, imag);
#else
    return real + imag * I;
#endif
}

// 滤波器类型枚举（用于calc_iir_filter_coeff函数）
enum {
    PEQ_PEAKING_FILTER = 0,
    PEQ_LOW_SHELF_FILTER = 1,
    PEQ_HIGH_SHELF_FILTER = 2,
    PEQ_LOWPASS_FILTER = 3,
    PEQ_HIGHPASS_FILTER = 4
};

// 生成倍频程频率列表
// min_freq: 最小频率，max_freq: 最大频率
// 返回DoubleArray结构体，包含频率点
DoubleArray get_oct_freq_list(double min_freq, double max_freq) {
    DoubleArray result;

    // 在最小和最大频率之间生成倍频程分布的频率点
    // 计算需要的点数
    int num_points = 100;  // 默认点数
    double oct_range = log2(max_freq / min_freq);  // 倍频程范围

    // 为了保证平滑，每倍频程至少10个点
    if (oct_range * OCTAVE_STEPS > num_points) {
        num_points = (int)(oct_range * OCTAVE_STEPS) + 1;
    }

    // 分配内存
    result.size = num_points;
    result.data = (double*)malloc(sizeof(double) * num_points);

    // 生成频率点
    for (int i = 0; i < num_points; i++) {
        double t = (double)i / (num_points - 1);  // 0到1之间的比例
        result.data[i] = min_freq * pow(max_freq / min_freq, t);
    }

    return result;
}

// 计算单个双二阶滤波器在一组频率下的频率响应
// freq_list: 频率点数组
// fs: 采样率
// coeffs: 滤波器系数
// response: 输出复数响应
// order: 滤波器阶数（1或2）
void calc_single_biquad_resp(const DoubleArray* freq_list, double fs,
                            const BiquadCoeffs* coeffs,
                            double_complex* response, int order)
{
    size_t i;
    double a0, a1, a2, b0, b1, b2;
    double w0;
#if !defined(_MSC_VER)
    double cosw0, sinw0;
#endif
    double_complex num, den;

    // 注意：不再初始化响应为0，保留输入的响应值
    // 这样可以让31个点的增益值参与计算

        if (order == 1) {
        // 一阶滤波器
        b0 = coeffs->b1st[0];
        b1 = coeffs->b1st[1];
        a0 = coeffs->a1st[0];
        a1 = coeffs->a1st[1];

        for (i = 0; i < freq_list->size; i++) {
            double freq = freq_list->data[i];
            w0 = 2.0 * PI * freq / fs;
#if defined(_MSC_VER)
            double_complex z_1;
            z_1.re = cos(w0);
            z_1.im = -sin(w0);

            // 分子
            double_complex num_term1, num_term2;
            num_term1.re = b0;
            num_term1.im = 0.0;

            num_term2.re = b1 * z_1.re;
            num_term2.im = b1 * z_1.im;

            num.re = num_term1.re + num_term2.re;
            num.im = num_term1.im + num_term2.im;

            // 分母
            double_complex den_term1, den_term2;
            den_term1.re = a0;
            den_term1.im = 0.0;

            den_term2.re = a1 * z_1.re;
            den_term2.im = a1 * z_1.im;

            den.re = den_term1.re + den_term2.re;
            den.im = den_term1.im + den_term2.im;

            // 复数除法
            double denominator = den.re * den.re + den.im * den.im;
            double_complex result;
            result.re = (num.re * den.re + num.im * den.im) / denominator;
            result.im = (num.im * den.re - num.re * den.im) / denominator;

            response[i] *= result;
#else
            cosw0 = cos(w0);
            sinw0 = sin(w0);
#ifdef __cplusplus
            double_complex z_1 = std::complex<double>(cosw0, -sinw0);
#else
            double_complex z_1 = cosw0 - I * sinw0;
#endif
            num = b0 + b1 * z_1;
            den = a0 + a1 * z_1;
            response[i] *= num / den;
#endif
        }
    }
    else {
        // 二阶滤波器
        b0 = coeffs->b[0];
        b1 = coeffs->b[1];
        b2 = coeffs->b[2];
        a0 = coeffs->a[0];
        a1 = coeffs->a[1];
        a2 = coeffs->a[2];

        for (i = 0; i < freq_list->size; i++) {
            double freq = freq_list->data[i];
            w0 = 2.0 * PI * freq / fs;

#if defined(_MSC_VER)
            double_complex z_1, z_2;
            z_1.re = cos(w0);
            z_1.im = -sin(w0);

            z_2.re = cos(2.0 * w0);
            z_2.im = -sin(2.0 * w0);

            // 分子
            double_complex num_term1, num_term2, num_term3;
            num_term1.re = b0;
            num_term1.im = 0.0;

            num_term2.re = b1 * z_1.re;
            num_term2.im = b1 * z_1.im;

            num_term3.re = b2 * z_2.re;
            num_term3.im = b2 * z_2.im;

            num.re = num_term1.re + num_term2.re + num_term3.re;
            num.im = num_term1.im + num_term2.im + num_term3.im;

            // 分母
            double_complex den_term1, den_term2, den_term3;
            den_term1.re = a0;
            den_term1.im = 0.0;

            den_term2.re = a1 * z_1.re;
            den_term2.im = a1 * z_1.im;

            den_term3.re = a2 * z_2.re;
            den_term3.im = a2 * z_2.im;

            den.re = den_term1.re + den_term2.re + den_term3.re;
            den.im = den_term1.im + den_term2.im + den_term3.im;

            // 复数除法
            double denominator = den.re * den.re + den.im * den.im;
            double_complex result;
            result.re = (num.re * den.re + num.im * den.im) / denominator;
            result.im = (num.im * den.re - num.re * den.im) / denominator;

            response[i] *= result;
#else
            cosw0 = cos(w0);
            sinw0 = sin(w0);
#ifdef __cplusplus
            double_complex z_1 = std::complex<double>(cosw0, -sinw0);
            double_complex z_2 = std::complex<double>(cos(2.0 * w0), -sin(2.0 * w0));
#else
            double_complex z_1 = cosw0 - I * sinw0;
            double_complex z_2 = cos(2.0 * w0) - I * sin(2.0 * w0);
#endif
            num = b0 + b1 * z_1 + b2 * z_2;
            den = a0 + a1 * z_1 + a2 * z_2;
            response[i] *= num / den;
#endif
        }
    }
}

// 计算一阶滤波器系数
// pass_type: 1为低通，0为高通
// C: 预处理参数
// b, a: 输出系数
void complex_filter_1st_order_coeff(int pass_type, float C, double *b, double *a)
{
    if (C == 0.0f) {
        // printf("[FIX] complex_filter_1st_order_coeff: C=0, use identity coeffs\n");
        b[0] = 1.0; b[1] = 0.0;
        a[0] = 1.0; a[1] = 0.0;
        return;
    }
    if (pass_type) { // 1阶低通
        b[0] = C / (C + 1.0f);
        b[1] = b[0];
    } else { // 1阶高通
        b[0] = 1.0f / (C + 1.0f);
        b[1] = -b[0];
    }
    a[0] = 1.0f;
    a[1] = (C - 1) / (C + 1.0f);
}

// 计算IIR滤波器系数
// type: 滤波器类型
// f0: 截止频率
// gain_db: 增益（dB）
// q_value: Q值
// coeff: 输出系数结构体
void calc_iir_filter_coeff( float fs, int type, float f0,
                            float gain_db, float q_value,
                            BiquadCoeffs *coeff)
{
    float a0, a1, a2, b0, b1, b2, alpha, omega, cos_omega, sin_omega;
    float A, beta;

    // 预计算
    omega = 2 * PI * f0 / fs;
    cos_omega = cosf(omega);
    sin_omega = sinf(omega);
    alpha = sin_omega / (2 * q_value);

    // 增益计算
    A = powf(10, gain_db / 40); // 平方根倍数

    // 根据滤波器类型计算系数
    switch(type) {
        case PEQ_PEAKING_FILTER:
            // EQ增益滤波器
            beta = powf(10, gain_db / 40); // 平方根倍数

            b0 = 1 + alpha * beta;
            b1 = -2 * cos_omega;
            b2 = 1 - alpha * beta;
            a0 = 1 + alpha / beta;
            a1 = -2 * cos_omega;
            a2 = 1 - alpha / beta;
            break;
        case PEQ_LOW_SHELF_FILTER:
            // 低架滤波器 - 使用标准Audio EQ Cookbook公式
            // beta = 2*sqrt(A)*alpha，其中alpha = sin(ω₀)/(2*Q)
            beta = 2 * sqrtf(A) * alpha;

            b0 = A * ((A + 1) - (A - 1) * cos_omega + beta);
            b1 = 2 * A * ((A - 1) - (A + 1) * cos_omega);
            b2 = A * ((A + 1) - (A - 1) * cos_omega - beta);
            a0 = (A + 1) + (A - 1) * cos_omega + beta;
            a1 = -2 * ((A - 1) + (A + 1) * cos_omega);
            a2 = (A + 1) + (A - 1) * cos_omega - beta;
            break;
        case PEQ_HIGH_SHELF_FILTER:
            // 高架滤波器 - 使用标准Audio EQ Cookbook公式
            // beta = 2*sqrt(A)*alpha，其中alpha = sin(ω₀)/(2*Q)
            beta = 2 * sqrtf(A) * alpha;

            b0 = A * ((A + 1) + (A - 1) * cos_omega + beta);
            b1 = -2 * A * ((A - 1) + (A + 1) * cos_omega);
            b2 = A * ((A + 1) + (A - 1) * cos_omega - beta);
            a0 = (A + 1) - (A - 1) * cos_omega + beta;
            a1 = 2 * ((A - 1) - (A + 1) * cos_omega);
            a2 = (A + 1) - (A - 1) * cos_omega - beta;
            break;
        case PEQ_LOWPASS_FILTER:
            // 低通滤波器
            b0 = (1 - cos_omega) / 2;
            b1 = 1 - cos_omega;
            b2 = (1 - cos_omega) / 2;
            a0 = 1 + alpha;
            a1 = -2 * cos_omega;
            a2 = 1 - alpha;
            break;
        case PEQ_HIGHPASS_FILTER:
            // 高通滤波器
            b0 = (1 + cos_omega) / 2;
            b1 = -(1 + cos_omega);
            b2 = (1 + cos_omega) / 2;
            a0 = 1 + alpha;
            a1 = -2 * cos_omega;
            a2 = 1 - alpha;
            break;
        default:
            // 默认值
            b0 = 1.0f;
            b1 = 0.0f;
            b2 = 0.0f;
            a0 = 1.0f;
            a1 = 0.0f;
            a2 = 0.0f;
            break;
    }

    // 标准化系数
    coeff->b[0] = b0 / a0;
    coeff->b[1] = b1 / a0;
    coeff->b[2] = b2 / a0;
    coeff->a[0] = 1.0f;
    coeff->a[1] = a1 / a0;
    coeff->a[2] = a2 / a0;
}

// 组合型滤波器响应计算方法
// type: 滤波器类型
// fc: 截止频率
// slope_rate: 滤波器斜率
// freq_list: 频率点数组
// total_resp: 总响应（输入输出）
void complex_filter_combined_method(int type, double fc, int slope_rate,
                                const DoubleArray* freq_list,
                                double_complex* total_resp) {
    // 添加调试日志
    printf("[complex_filter_combined_method] 调用参数: type=%d, fc=%.2f, slope_rate=%d\n", type, fc, slope_rate);

    // 巴特沃斯滤波器特殊处理 - 使用优化的计算方法
    if (type == FILTER_TYPE_BUTTERWORTH_LP || type == FILTER_TYPE_BUTTERWORTH_HP) {
        printf("[complex_filter_combined_method] 调用巴特沃斯滤波器\n");
        butterworth_filter_optimized_response(type, fc, slope_rate, freq_list, total_resp);
        return;
    }

    // 贝塞尔滤波器特殊处理 - 使用优化的计算方法
    if (type == FILTER_TYPE_BESSEL_LP || type == FILTER_TYPE_BESSEL_HP) {
        printf("[complex_filter_combined_method] 调用贝塞尔滤波器\n");
        bessel_filter_optimized_response(type, fc, slope_rate, freq_list, total_resp);
        return;
    }

    // 宁克-锐滤波器处理 - 使用优化的响应计算
    if (type == FILTER_TYPE_LINKWITZ_RILEY_LP || type == FILTER_TYPE_LINKWITZ_RILEY_HP) {
        linkwitz_riley_optimized_response(type, fc, slope_rate, freq_list, total_resp);
        return;
    }

    // 其他滤波器类型使用标准方法
    BiquadCoeffs coeffs;
    size_t i;
    int j;
    int first_order_num = 0, second_order_num = 0;

    if (slope_rate / SLOPE_STEP != 1 || slope_rate < SLOPE_STEP) {
        first_order_num = 1;
        second_order_num = (int)((slope_rate - 6) / SLOPE_STEP);
    } else {
        second_order_num = (int)(slope_rate / SLOPE_STEP);
    }
    calc_iir_filter_coeff(FS, type, fc, 0.0f, 4.32f, &coeffs);

    // 创建临时响应缓冲区，确保数据类型正确
    double_complex* temp_buffer = (double_complex*)calloc(freq_list->size, sizeof(double_complex));
    if (temp_buffer == NULL) {
        return;
    }

    // 初始化临时缓冲区为1.0（0dB增益），而不是0
    for (i = 0; i < freq_list->size; i++) {
        temp_buffer[i] = create_complex(1.0, 0.0);
    }

    if (first_order_num != 0) {
        calc_single_biquad_resp(freq_list, FS, &coeffs, temp_buffer, 1);
        for(i = 0; i < freq_list->size; i++) {
            total_resp[i] *= temp_buffer[i];
        }
    }

    if (second_order_num != 0) {
        for (j = 0; j < second_order_num; j++) {
            // 每次计算前重置temp_buffer为1.0
            for (i = 0; i < freq_list->size; i++) {
                temp_buffer[i] = create_complex(1.0, 0.0);
            }

            calc_single_biquad_resp(freq_list, FS, &coeffs, temp_buffer, 2);
            for(i = 0; i < freq_list->size; i++) {
                total_resp[i] *= temp_buffer[i];
            }
        }
    }

    free(temp_buffer);
    // Ensure all printf output is flushed immediately
    fflush(stdout);
    fflush(stderr);
}

// 为datamodel获取滤波后数据点的通用接口
void process_filter_points(const FilterParamsUniversal* highpass, const FilterParamsUniversal* lowpass,
                            const DoubleArray* freq_list, double_complex* out_resp) {
    // 注意：不再初始化输出响应为0，保留输入的响应值（已经包含了增益信息）
    // 这样可以让31个点的增益值参与计算

    // printf("[process_filter_points] 高通参数: type=%d, fc=%.2f, slope=%d\n",
    //        highpass->type, highpass->fc, highpass->slope);
    // printf("[process_filter_points] 低通参数: type=%d, fc=%.2f, slope=%d\n",
    //        lowpass->type, lowpass->fc, lowpass->slope);

    // 叠加高通
    if (highpass->type != 0 && highpass->fc > 0 && highpass->slope > 0) {
        printf("[process_filter_points] 处理高通滤波器\n");
        complex_filter_combined_method(highpass->type, highpass->fc, highpass->slope, freq_list, out_resp);
    }

    // 叠加低通
    if (lowpass->type != 0 && lowpass->fc > 0 && lowpass->slope > 0) {
        printf("[process_filter_points] 处理低通滤波器\n");
        complex_filter_combined_method(lowpass->type, lowpass->fc, lowpass->slope, freq_list, out_resp);
    }
}

// 测试滤波器响应的函数
void test_filter(const char* filter_name _UNUSED_, double min_freq, double max_freq,
                int high_pass_type, double high_pass_fc, int high_pass_slope,
                int low_pass_type, double low_pass_fc, int low_pass_slope,
                int print_full_response) {
    // 获取计算后的数据点
    DoubleArray freq_list = get_oct_freq_list(min_freq, max_freq);
    double_complex* resp = (double_complex*)calloc(freq_list.size, sizeof(double_complex));

    // 初始化为0
    for(size_t i=0; i<freq_list.size; i++) {
#ifdef __cplusplus
        resp[i] = std::complex<double>(0.0, 0.0);
#else
        resp[i] = 0.0 + 0.0*I;
#endif
    }

    // 应用高通滤波器
    if (high_pass_type > 0 && high_pass_fc > 0 && high_pass_slope > 0) {
        complex_filter_combined_method(high_pass_type, high_pass_fc, high_pass_slope, &freq_list, resp);
    }

    // 应用低通滤波器
    if (low_pass_type > 0 && low_pass_fc > 0 && low_pass_slope > 0) {
        complex_filter_combined_method(low_pass_type, low_pass_fc, low_pass_slope, &freq_list, resp);
    }

    // 打印完整响应曲线
    if (print_full_response) {
        for (size_t i = 0; i < freq_list.size; i++) {
            // double freq = freq_list.data[i]; // 移除未使用变量
            // double mag = cabs(resp[i]);      // 移除未使用变量
            // double db = 20.0 * log10(mag);   // 移除未使用变量
        }
    }

    // 释放内存
    free(freq_list.data);
    free(resp);
}

// 测试贝塞尔滤波器的快捷函数已移动到bessel_filter.cpp

// 以下函数已移动到对应的独立文件中
// void butterworth_section_coeffs(...) - 已移动到butterworth_filter.cpp
// void butterworth_filter_optimized_response(...) - 已移动到butterworth_filter.cpp
// void bessel_section_coeffs(...) - 已移动到bessel_filter.cpp
// void bessel_filter_optimized_response(...) - 已移动到bessel_filter.cpp
// void linkwitz_riley_optimized_response(...) - 已移动到linkwitz_riley_filter.cpp
