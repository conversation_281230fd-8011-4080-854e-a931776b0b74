# DSP Controller 打包说明

本文档说明如何为DSP Controller项目创建Windows安装包。

## 打包方式选择

本项目提供两种打包方式：

1. Qt官方工具（推荐）
   - 使用`windeployqt`自动部署Qt依赖
   - 使用Qt Installer Framework创建安装包
   - 提供更好的安装体验和维护功能

2. NSIS打包（备选）
   - 使用传统的NSIS创建安装包
   - 更轻量级的安装过程
   - 适合简单部署场景

## 环境要求

### 方式一：Qt官方工具（推荐）

1. Qt开发环境
   - Qt 6.5.2
   - MinGW 64位编译器
   - Qt Installer Framework
   - 默认安装路径：`C:\Qt\6.5.2\mingw_64`

2. windeployqt工具安装
   - 打开Qt维护工具(MaintenanceTool)
   - 选择"添加或删除组件"
   - 展开"Qt 6.5.2"
   - 勾选"Qt 6.5.2 > Developer and Designer Tools > Qt Quick App Development Tools"
   - 点击"下一步"完成安装
   - 安装完成后，windeployqt会位于`C:\Qt\6.5.2\mingw_64\bin`目录下

3. 验证windeployqt安装
   ```batch
   # 检查windeployqt是否可用
   where windeployqt
   # 或
   windeployqt --version
   ```

### 方式二：NSIS打包

1. Qt开发环境
   - Qt 6.5.2
   - MinGW 64位编译器
   - 默认安装路径：`C:\Qt\6.5.2\mingw_64`

2. NSIS (Nullsoft Scriptable Install System)
   - 下载地址：[NSIS官网](https://nsis.sourceforge.io/Download)
   - 默认安装路径：`C:\Program Files (x86)\NSIS`

## 文件说明

打包相关文件位于`packaging`目录：

- `make_installer_qt.bat` - Qt官方工具打包脚本
- `make_installer.bat` - NSIS打包脚本
- `installer.nsi` - NSIS安装包配置脚本
- `License.txt` - 软件许可协议

## 打包步骤

### 方式一：使用Qt官方工具（推荐）

1. 确保已安装Qt Installer Framework
   ```batch
   # 检查是否安装
   where binarycreator
   ```

2. 运行Qt打包脚本
   ```batch
   make_installer_qt.bat
   ```

   脚本会自动执行以下操作：
   - 编译Release版本
   - 使用windeployqt自动部署Qt依赖
   - 创建安装包配置和脚本
   - 生成安装包

3. 打包完成后会在项目根目录生成安装包：
   `DSP_Controller_Setup_0.0.1.exe`

### 方式二：使用NSIS打包

1. 确保已安装NSIS

2. 运行NSIS打包脚本
   ```batch
   make_installer.bat
   ```

## Qt安装包特性

使用Qt Installer Framework创建的安装包具有以下特性：

1. 自动检测和部署所需的Qt依赖
2. 现代化的安装界面
3. 软件许可协议显示
4. 自定义安装路径
5. 创建开始菜单和桌面快捷方式
6. 内置维护工具（卸载功能）
   - 通过"开始菜单 > DSP Controller > 卸载 DSP Controller"卸载
   - 或通过"控制面板 > 程序和功能"卸载
   - 支持软件更新
   - 支持组件管理
   - 自动清理注册表和快捷方式
7. 完整的中文界面
8. 卸载功能
   - 完整删除所有组件
   - 自动清理注册表
   - 删除开始菜单和桌面快捷方式
   - 卸载完成提示

## 自定义配置

### Qt安装包配置

1. 修改版本号
   ```batch
   set VERSION=0.0.1.2
   ```

2. 修改公司信息
   编辑`installer\config\config.xml`：
   ```xml
   <Publisher>Your Company</Publisher>
   ```

### NSIS安装包配置

1. 修改版本号
   ```nsis
   !define PRODUCT_VERSION "0.0.1.2"
   ```

2. 修改公司信息
   ```nsis
   !define PRODUCT_PUBLISHER "Your Company Name"
   ```

## 常见问题

1. Qt依赖部署问题
   - 使用`windeployqt --list-source`检查依赖
   - 确保所有Qt模块都已正确部署

2. 安装包创建失败
   - 检查Qt Installer Framework是否正确安装
   - 检查配置文件格式是否正确
   - 检查目录结构是否完整

3. 运行时缺少DLL
   - 使用Dependency Walker检查依赖
   - 确保所有第三方DLL都已包含

4. windeployqt工具找不到
   - 确保已通过Qt维护工具安装Qt Quick App Development Tools
   - 检查环境变量PATH是否包含Qt bin目录
   - 重新打开命令行窗口尝试

## 注意事项

1. 打包前的检查：
   - 确保项目能在Release模式下正常编译运行
   - 测试所有功能是否正常
   - 检查版本号是否正确

2. 版本号更新：
   - `DSP_PC.pro`中的VERSION
   - 打包脚本中的VERSION
   - 安装包配置中的版本号

3. 安装包测试：
   - 在干净的环境中测试安装
   - 测试所有功能是否正常
   - 测试卸载是否完整

## 卸载说明

软件提供以下卸载方式：

1. 开始菜单卸载
   - 点击"开始菜单 > DSP Controller > 卸载 DSP Controller"
   - 按照卸载向导完成卸载

2. 控制面板卸载
   - 打开"控制面板 > 程序和功能"
   - 找到"DSP Controller"并点击卸载
   - 按照卸载向导完成卸载

3. 维护工具卸载
   - 运行安装目录下的"维护工具.exe"
   - 选择"删除所有组件"
   - 按照向导完成卸载

卸载过程会：
- 删除所有程序文件
- 删除开始菜单快捷方式
- 删除桌面快捷方式
- 清理注册表
- 显示卸载完成提示

# Qt Installer Framework 打包工具

## 翻译文件生成

### 使用的脚本
- `create_installer_translations_fixed.bat` - 主要的翻译文件生成脚本，包含完整的Qt Installer Framework标准翻译

### 已删除的冗余脚本
- `create_installer_translations.bat` - 功能重复，错误处理较差
- `create_translations.bat` - 功能较弱，翻译不完整
- `create_translations.ps1` - 不必要的PowerShell包装器

### 翻译功能特性
- 支持英语(en_US)和日语(ja_JP)
- 包含Qt Installer Framework标准界面字符串翻译
- 自动生成.ts源文件和.qm编译文件
- 完整的错误处理机制
- 支持自动模式(auto参数)，适合集成到构建流程

### 翻译的界面元素
- ComponentSelectionPage - 组件选择页面
- IntroductionPage - 欢迎页面
- TargetDirectoryPage - 安装目录选择
- StartMenuDirectoryPage - 开始菜单快捷方式
- ReadyForInstallationPage - 准备安装页面
- PerformInstallationPage - 安装进行页面
- FinishedPage - 安装完成页面

### 使用方法
1. 直接运行: `create_installer_translations_fixed.bat`
2. 自动模式: `create_installer_translations_fixed.bat auto`
3. 通过make_installer_qt.bat自动调用

### 生成的文件
- `translations/en_US.ts` - 英语翻译源文件
- `translations/en_US.qm` - 英语翻译编译文件
- `translations/ja_JP.ts` - 日语翻译源文件
- `translations/ja_JP.qm` - 日语翻译编译文件

## 构建流程集成

`make_installer_qt.bat` 已经集成了翻译文件生成，会自动调用翻译脚本确保安装包包含最新的多语言支持。