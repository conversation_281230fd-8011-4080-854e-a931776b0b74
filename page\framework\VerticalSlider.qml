import QtQuick
import QtQuick.Controls.Basic
import Qt5Compat.GraphicalEffects

Slider {
    id: control
    padding: 1
    // wheelEnabled: true
    orientation: Qt.Vertical

    property bool bandFocus: false
    property color bandFocusColor: "transparent"
    property alias text: title.text

    MouseArea {
        anchors.fill: parent
        onPressed: (mouse)=> {
                       if(mouseY < handle.y)
                       {
                           // control.increase()
                           control.focus = true
                       }
                       else if(mouseY > (handle.y + handle.height))
                       {
                           // control.decrease()
                           control.focus = true
                       }
                       else
                       {
                           mouse.accepted = false
                       }
                   }
    }

    background: Rectangle {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: parent.top
        anchors.topMargin: parent.topPadding
        width: 6
        height: control.availableHeight
        radius: width / 2
        color: "#181818"
        border.color: "#5C6068"
        border.width: 1

        Rectangle {
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: parent.bottom
            anchors.bottomMargin: parent.border.width
            width: parent.width - parent.border.width * 2
            height: (1 - control.visualPosition) * (parent.height - parent.border.width * 2)
            radius: height / 2
            color: "#C8D8FF"
        }
    }

    handle: Item {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: control.bottomPadding + (1 - control.visualPosition) * (control.availableHeight - height)
        implicitWidth: 18
        implicitHeight: 18

        Rectangle {
            anchors.fill: parent
            radius: width / 2
            color: control.bandFocus ? bandFocusColor : "#767676"
            layer.enabled: true
            layer.effect: DropShadow {
                transparentBorder: false
                horizontalOffset: 0
                verticalOffset: 2
                radius: 2
                color: "#80000000"
            }
        }

        Text {
            id: title
            anchors.centerIn: parent
            color: "#FFFFFF"
            font.family: "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
        }
    }

    Keys.onPressed: (event)=> {
        switch(event.key)
        {
        case Qt.Key_Up:
        case Qt.Key_Right:
        {
            // control.increase()
            event.accepted = true
            break
        }
        case Qt.Key_Down:
        case Qt.Key_Left:
        {
            // control.decrease()
            event.accepted = true
            break
        }
        }
    }
}
