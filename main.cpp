#include "page/MainWindow.h"
#include "page/CommonController.h"
#include "page/framework/curve/canvasview.h"
#include "page/framework/curve/datamodel.h"

#include <QApplication>
#include <QStyleFactory>
#include <QTranslator>
#include <QQmlEngine>
#include <QSharedMemory>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    //限制单一程序
    static QSharedMemory *singleApp = new QSharedMemory("SingleApp");
    if (!singleApp->create(1)) {
        qApp->quit();
        return -1;
    }

    // QApplication::setStyle(QStyleFactory::create("fusion"));

    // 注册QML类型
    qmlRegisterType<CanvasView>("CanvasView", 1, 0, "CanvasView");

    CommonController::getInstance();

    MainWindow w;

    int result = app.exec();

    // 应用程序退出时销毁单例
    DataModel::destroyInstance();

    return result;
}
