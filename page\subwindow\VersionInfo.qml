import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// Version Information Display Component
Rectangle {
    id: versionInfo
    width: 400
    height: 300
    color: "#2b2b2b"
    border.color: "#555555"
    border.width: 1
    radius: 8

    property alias title: titleText.text

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 15

        // Title
        Text {
            id: titleText
            text: qsTr("Version Information")
            font.pixelSize: 18
            font.bold: true
            color: "#ffffff"
            Layout.alignment: Qt.AlignHCenter
        }

        // Version details
        GridLayout {
            columns: 2
            columnSpacing: 20
            rowSpacing: 10
            Layout.fillWidth: true

            // Version String
            Text {
                text: qsTr("Version:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getVersionString()
                font.pixelSize: 12
                color: "#ffffff"
                font.bold: true
            }

            // Full Version
            Text {
                text: qsTr("Full Version:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getVersionFull()
                font.pixelSize: 12
                color: "#ffffff"
            }

            // Git Branch
            Text {
                text: qsTr("Branch:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getGitBranch()
                font.pixelSize: 12
                color: "#ffffff"
            }

            // Git Commit
            Text {
                text: qsTr("Commit:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getGitCommit()
                font.pixelSize: 12
                color: "#ffffff"
                font.family: "Consolas, Monaco, monospace"
            }

            // Build Time
            Text {
                text: qsTr("Build Time:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getBuildTimestamp()
                font.pixelSize: 12
                color: "#ffffff"
            }

            // Version Components
            Text {
                text: qsTr("Version Components:")
                font.pixelSize: 12
                color: "#cccccc"
                Layout.alignment: Qt.AlignRight
            }
            Text {
                text: commonCtrl.getVersionMajor() + "." + 
                      commonCtrl.getVersionMinor() + "." + 
                      commonCtrl.getVersionPatch()
                font.pixelSize: 12
                color: "#ffffff"
            }
        }

        // Separator
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: "#555555"
        }

        // Version comparison example
        Text {
            text: qsTr("Version Checks:")
            font.pixelSize: 14
            font.bold: true
            color: "#ffffff"
        }

        Column {
            spacing: 5
            Layout.fillWidth: true

            Text {
                text: qsTr("Is version >= 1.0.0: ") + 
                      (commonCtrl.isVersionAtLeast(1, 0, 0) ? qsTr("Yes") : qsTr("No"))
                font.pixelSize: 11
                color: commonCtrl.isVersionAtLeast(1, 0, 0) ? "#00ff00" : "#ff0000"
            }

            Text {
                text: qsTr("Is version >= 1.1.0: ") + 
                      (commonCtrl.isVersionAtLeast(1, 1, 0) ? qsTr("Yes") : qsTr("No"))
                font.pixelSize: 11
                color: commonCtrl.isVersionAtLeast(1, 1, 0) ? "#00ff00" : "#ff0000"
            }

            Text {
                text: qsTr("Is version >= 2.0.0: ") + 
                      (commonCtrl.isVersionAtLeast(2, 0, 0) ? qsTr("Yes") : qsTr("No"))
                font.pixelSize: 11
                color: commonCtrl.isVersionAtLeast(2, 0, 0) ? "#00ff00" : "#ff0000"
            }
        }

        // Spacer
        Item {
            Layout.fillHeight: true
        }

        // Close button
        Button {
            text: qsTr("Close")
            Layout.alignment: Qt.AlignHCenter
            onClicked: versionInfo.visible = false
        }
    }
}
