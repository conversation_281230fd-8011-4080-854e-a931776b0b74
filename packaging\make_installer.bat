@echo off
echo 正在构建安装包...

REM 设置Qt环境变量
set QTDIR=C:\Qt\6.5.2\mingw_64
set PATH=%QTDIR%\bin;%PATH%

REM 切换到项目根目录
cd ..

REM 构建Release版本
qmake -spec win32-g++
mingw32-make clean
mingw32-make release

REM 复制必要的DLL文件到发布目录
if not exist release mkdir release
copy %QTDIR%\bin\Qt6Core.dll release\
copy %QTDIR%\bin\Qt6Gui.dll release\
copy %QTDIR%\bin\Qt6Widgets.dll release\
copy %QTDIR%\bin\Qt6Quick.dll release\
copy %QTDIR%\bin\Qt6QuickWidgets.dll release\
copy %QTDIR%\bin\Qt6Network.dll release\
copy %QTDIR%\bin\Qt6Qml.dll release\
copy %QTDIR%\bin\Qt6QmlModels.dll release\
copy %QTDIR%\bin\libgcc_s_seh-1.dll release\
copy %QTDIR%\bin\libstdc++-6.dll release\
copy %QTDIR%\bin\libwinpthread-1.dll release\
copy communication\LibUsb\hidapi.dll release\

REM 运行NSIS脚本生成安装包
cd packaging
"C:\Program Files (x86)\NSIS\makensis.exe" installer.nsi
cd ..

echo 安装包构建完成！
pause 