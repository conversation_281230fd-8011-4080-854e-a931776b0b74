#include "Mid05Handler.h"
#include <QtCore>

Mid05Handler::Mid05<PERSON>and<PERSON>()
{

}

void Mid05Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(OTAOperation::SID::SYSTEM_VERSION_QUERY):
    {
        parse01Data(data);
        break;
    }
    case static_cast<uint8_t>(OTAOperation::SID::UPGRADE_DATA):
    {
        parse02Data(data);
        break;
    }
    case static_cast<uint8_t>(OTAOperation::SID::UPGRADE_RESULT):
    {
        parse03Data(data);
        break;
    }
    default:
        break;
    }
}

void Mid05Handler::parse01Data(const QByteArray &data)
{
    uint8_t dspMainVersion = data.at(5);
    emit dspMainVersionChanged(dspMainVersion);

    uint8_t dspSubVersion = data.at(6);
    emit dspSubVersionChanged(dspSubVersion);

    uint8_t remoteMainVersion = data.at(17);
    emit remoteMainVersionChanged(remoteMainVersion);

    uint8_t remoteSubVersion = data.at(18);
    emit remoteSubVersionChanged(remoteSubVersion);
}

void Mid05Handler::parse02Data(const QByteArray &data)
{
    OTAOperation02Received temp02;
    memcpy(&temp02, data.data(), data.size());

    emit upgradeError(temp02.flag);

    if(0 != temp02.index)
    {
        emit upgradeProgress(temp02.mode);
    }
}

void Mid05Handler::parse03Data(const QByteArray &data)
{
    OTAOperation03Received temp03;
    memcpy(&temp03, data.data(), data.size());

    emit upgradeStatus(temp03.flag);
}

QByteArray Mid05Handler::send01Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

void Mid05Handler::send02Data(uint8_t type, const QByteArray &data, QList<QByteArray> &frameDatas)
{
    const int dataPackMaxSize = 128;
    frameDatas.clear();
    uint32_t totalSize = data.size();

    QByteArray frame0(4, (uint8_t)0x00);
    switch (type) {
    case 0:
    {
        QByteArray name("MCU", 4);
        frame0.append(name);
        break;
    }
    case 2:
    {
        QByteArray name("line", 4);
        frame0.append(name);
        break;
    }
    default:
        break;
    }
    frame0.append((uint8_t)0x00);
    QByteArray sizeData(sizeof(totalSize), (char)0x00);
    memcpy(sizeData.data(), &totalSize, sizeof(totalSize));
    frame0.append(sizeData);
    frameDatas.append(frame0);

    QByteArray sendData = data;
    uint32_t packageCount = (data.size() + dataPackMaxSize - 1) / dataPackMaxSize;
    for(uint32_t index = 1; index < (packageCount + 1); index++)
    {
        if(sendData.size() <= dataPackMaxSize)
        {
            QByteArray frame(sizeof(index), (char)0x00);
            memcpy(frame.data(), &index, sizeof(index));
            frame.append(sendData);
            frame.resize((dataPackMaxSize + sizeof(index)), 0x00);
            frameDatas.append(frame);
            break;
        }
        else
        {
            QByteArray frame(sizeof(index), (char)0x00);
            memcpy(frame.data(), &index, sizeof(index));
            frame.append(sendData.first(dataPackMaxSize));
            frameDatas.append(frame);
            sendData.slice(dataPackMaxSize);
        }
    }
}

QByteArray Mid05Handler::send03Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}
