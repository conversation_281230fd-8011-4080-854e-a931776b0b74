﻿// Auto-generated version header file
// Generated on: 2025-07-15 18:02:48
// Do not edit this file manually!

#ifndef VERSION_H
#define VERSION_H

// Version information from git tag
#define VERSION_MAJOR 1
#define VERSION_MINOR 0
#define VERSION_PATCH 0

// Version strings
#define VERSION_STRING "1.0.0"
#define VERSION_TAG "v1.0.0"
#define VERSION_FULL "v1.0.0 (unknown)"

// Git information
#define GIT_COMMIT "unknown"
#define GIT_BRANCH "unknown"

// Build information
#define BUILD_TIMESTAMP "2025-07-15 18:02:48"
#define BUILD_DATE "2025-07-15"
#define BUILD_TIME "18:02:48"

// Convenience macros
#define VERSION_AT_LEAST(major, minor, patch) ( \
    (VERSION_MAJOR > (major)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR > (minor)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR == (minor) && VERSION_PATCH >= (patch)) \
)

#endif // VERSION_H
