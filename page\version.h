﻿// Auto-generated version header file
// Generated on: 2025-07-20 22:08:15
// Do not edit this file manually!

#ifndef VERSION_H
#define VERSION_H

// Version information from git tag
#define VERSION_MAJOR 1
#define VERSION_MINOR 1
#define VERSION_PATCH 7

// Version strings
#define VERSION_STRING "1.1.7"
#define VERSION_TAG "V1.1.7"
#define VERSION_FULL "V1.1.7 (bd31c74)"

// Git information
#define GIT_COMMIT "bd31c74"
#define GIT_BRANCH "dev-wk"

// Build information
#define BUILD_TIMESTAMP "2025-07-20 22:08:15"
#define BUILD_DATE "2025-07-20"
#define BUILD_TIME "22:08:15"

// Convenience macros
#define VERSION_AT_LEAST(major, minor, patch) ( \
    (VERSION_MAJOR > (major)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR > (minor)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR == (minor) && VERSION_PATCH >= (patch)) \
)

#endif // VERSION_H
