import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    anchors.fill: parent
    color: "#2C3038"

    Rectangle {
        id: control
        anchors.centerIn: parent
        width: 400
        height: 540
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"
        state: "hid connecting"

        states: [
            State {
                name: "pc updating"
                PropertyChanges {
                    info.text: qsTr("A new update is available for the PC tool. Please download the latest version and use it.")
                    vectorImg.visible: false
                    pcUpdateBtnRow.visible: true
                    dspUpdateBtnRow.visible: false
                    dspUpdateConfirm.visible: false
                    usbIcon.visible: false
                    initBtnRow.visible: false
                }
            },
            State {
                name: "hid connecting"
                PropertyChanges {
                    info.text: qsTr("Now Connecting…")
                    vectorImg.visible: true
                    pcUpdateBtnRow.visible: false
                    dspUpdateBtnRow.visible: false
                    dspUpdateConfirm.visible: false
                    usbIcon.visible: false
                    initBtnRow.visible: false
                }
            },
            State {
                name: "dsp updating"
                PropertyChanges {
                    info.text: qsTr("Firmware update for %1 has been found.
If you update, your current settings will be lost. If you want to save, select \"Cancel\", launch the Tool, and save the settings.
If you want to update, select \"Update\".").arg(dataMap["deviceType"])
                    vectorImg.visible: false
                    pcUpdateBtnRow.visible: false
                    dspUpdateBtnRow.visible: true
                    dspUpdateConfirm.visible: false
                    usbIcon.visible: false
                    initBtnRow.visible: false
                }
            },
            State {
                name: "dsp update confirm"
                PropertyChanges {
                    info.text: qsTr("Go to [Settings] > [Device Firmware Update], select the download file, and update the DSP unit.")
                    vectorImg.visible: false
                    pcUpdateBtnRow.visible: false
                    dspUpdateBtnRow.visible: false
                    dspUpdateConfirm.visible: true
                    usbIcon.visible: false
                    initBtnRow.visible: false
                }
            },
            State {
                name: "hid connect fail"
                PropertyChanges {
                    info.text: qsTr("Please check your USB cable.
If you do not connect with a DSP device, the application can start in demo mode.")
                    vectorImg.visible: false
                    pcUpdateBtnRow.visible: false
                    dspUpdateBtnRow.visible: false
                    dspUpdateConfirm.visible: false
                    usbIcon.visible: true
                    initBtnRow.visible: true
                }
            }
        ]

        Timer {
            id: initTimer
            interval: 8000
            onTriggered: {
                //连接失败，显示demo重连信息和按键
                control.state = "hid connect fail"
            }
        }

        Connections {
            target: commonCtrl
            function onShowToolUpdate(isShown) {
                if(isShown)
                {
                    //需要升级，显示升级信息和按键
                    control.state = "pc updating"
                }
                else
                {
                    //不需升级，连接dsp，获取dsp版本号
                    commonCtrl.reconnectDevice()
                    initTimer.start()
                    control.state = "hid connecting"
                }
            }
            function onInitFinish() {
                initTimer.stop()

                //连接成功，对比dsp版本号
                if(((dataMap["dspMainVersion"] + "." + dataMap["dspSubVersion"]) !== dataMap["uiDataMap"]["newDspVersion"])
                        && ("" !== dataMap["uiDataMap"]["newDspVersion"]))
                {
                    control.state = "dsp updating"
                }
                else
                {
                    commonCtrl.showMainWindow()
                }
            }
        }

        Rectangle {
            id: header
            width: parent.width
            height: 72
            color: "#FFFFFF"

            Image {
                anchors.left: parent.left
                anchors.leftMargin: 24
                anchors.top: parent.top
                anchors.topMargin: 26
                source: "qrc:/Image/icn_logo_pioneer128.png"
            }
        }

        Text {
            id: title
            anchors.left: header.left
            anchors.top: header.bottom
            width: header.width
            height: 100
            color: "#F0F0F0"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 56
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignBottom
            text: qsTr("DSP Controller")
        }

        Text {
            id: info
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: title.bottom
            anchors.topMargin: 24
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            color: "#FFFFFF"
            width: header.width
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            lineHeight: 22
            lineHeightMode: Text.FixedHeight
        }

        // 中心旋转的Vector图片
        property real vectorAngle: 0
        Image {
            id: vectorImg
            source: "qrc:/Image/Vector.png"
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: title.bottom
            anchors.topMargin: 88
            fillMode: Image.PreserveAspectFit
            RotationAnimation on rotation {
                loops: Animation.Infinite
                from: 0
                to: 360
                duration: 1000
                easing.type: Easing.Linear
            }
        }

        // USB图标
        Image {
            id: usbIcon
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: title.bottom
            anchors.topMargin: 80
            source: "qrc:/Image/icn_fig_setusb.png"
            fillMode: Image.PreserveAspectFit
        }

        // demo、退出和重连按钮
        Row {
            id: initBtnRow
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: usbIcon.bottom
            anchors.topMargin: 10
            spacing: 8

            ButtonB {
                id: demoBtn
                width: 112
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Start Demo Mode")
                onClicked: commonCtrl.showMainWindow()
            }

            ButtonB {
                id: exitBtn
                width: 112
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Exit the application")
                onClicked: commonCtrl.quitApp()
            }

            ButtonB {
                id: reconnectBtn
                width: 112
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Reconnect device")
                onClicked: {
                    initTimer.restart()
                    control.state = "hid connecting"
                    commonCtrl.reconnectDevice()
                }
            }
        }

        Row {
            id: pcUpdateBtnRow
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: initBtnRow.top
            spacing: 8

            ButtonB {
                id: pcDownloadBtn
                width: 140
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Download")
                onClicked: {
                    Qt.openUrlExternally("https://jpn.pioneer/ja/carrozzeria/car_av/processor/?ref=header")
                    commonCtrl.quitApp()
                }
            }

            ButtonB {
                id: pcCancelBtn
                width: 140
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Cancel")
                onClicked: {
                    initTimer.restart()
                    control.state = "hid connecting"
                    commonCtrl.reconnectDevice()
                }
            }
        }

        Row {
            id: dspUpdateBtnRow
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: initBtnRow.top
            spacing: 8

            ButtonB {
                id: dspCancelBtn
                width: 140
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Cancel")
                onClicked: commonCtrl.showMainWindow()
            }

            ButtonB {
                id: dspUpdateBtn
                width: 140
                height: 28
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                text: qsTr("Update")
                onClicked: {
                    Qt.openUrlExternally("https://jpn.pioneer/ja/car/dsp/terms/ja.php")
                    control.state = "dsp update confirm"
                }
            }
        }

        ButtonB {
            id: dspUpdateConfirm
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: initBtnRow.top
            width: 140
            height: 28
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            text: qsTr("OK")
            onClicked: commonCtrl.showMainWindow()
        }

        // 底部版权
        Text {
            id: copyright
            anchors.right: parent.right
            anchors.rightMargin: 22
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 14
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            color: "#F0F0F0"
            horizontalAlignment: Text.AlignRight
            verticalAlignment: Text.AlignVCenter
            text: "Copyright © 2025 Pioneer Corporation.All Rights Reserved."
        }
    }
}
