#!/bin/bash

# Generate version.h from git tag information
# Usage: generate_version.sh [output_directory]

OUTPUT_DIR="${1:-page}"
VERSION_FILE="$OUTPUT_DIR/version.h"

echo "[INFO] Generating version.h..."

# Get git tag version
if VERSION_TAG=$(git describe --tags --abbrev=0 2>/dev/null); then
    # Remove v or V prefix if present
    VERSION_CLEAN="${VERSION_TAG#[vV]}"
    
    # Parse version components (assuming format: major.minor.patch)
    IFS='.' read -ra VERSION_PARTS <<< "$VERSION_CLEAN"
    VERSION_MAJOR="${VERSION_PARTS[0]//[^0-9]/}"
    VERSION_MINOR="${VERSION_PARTS[1]//[^0-9]/}"
    VERSION_PATCH="${VERSION_PARTS[2]//[^0-9]/}"
    
    # Handle missing components
    VERSION_MAJOR="${VERSION_MAJOR:-0}"
    VERSION_MINOR="${VERSION_MINOR:-0}"
    VERSION_PATCH="${VERSION_PATCH:-0}"
    
    echo "[INFO] Using git tag version: $VERSION_TAG ($VERSION_MAJOR.$VERSION_MINOR.$VERSION_PATCH)"
else
    # No tags found, use default version
    VERSION_TAG="v1.0.0"
    VERSION_MAJOR=1
    VERSION_MINOR=0
    VERSION_PATCH=0
    echo "[WARNING] No git tags found, using default version: 1.0.0"
fi

# Get git commit hash
if GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null); then
    echo "[INFO] Git commit: $GIT_COMMIT"
else
    GIT_COMMIT="unknown"
    echo "[WARNING] Could not get git commit hash"
fi

# Get git branch name
if GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null); then
    echo "[INFO] Git branch: $GIT_BRANCH"
else
    GIT_BRANCH="unknown"
    echo "[WARNING] Could not get git branch name"
fi

# Get build timestamp
BUILD_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
BUILD_DATE=$(date '+%Y-%m-%d')
BUILD_TIME=$(date '+%H:%M:%S')

# Ensure output directory exists
mkdir -p "$OUTPUT_DIR"

# Generate version.h file
cat > "$VERSION_FILE" << EOF
// Auto-generated version header file
// Generated on: $BUILD_TIMESTAMP
// Do not edit this file manually!

#ifndef VERSION_H
#define VERSION_H

// Version information from git tag
#define VERSION_MAJOR $VERSION_MAJOR
#define VERSION_MINOR $VERSION_MINOR
#define VERSION_PATCH $VERSION_PATCH

// Version strings
#define VERSION_STRING "$VERSION_MAJOR.$VERSION_MINOR.$VERSION_PATCH"
#define VERSION_TAG "$VERSION_TAG"
#define VERSION_FULL "$VERSION_TAG ($GIT_COMMIT)"

// Git information
#define GIT_COMMIT "$GIT_COMMIT"
#define GIT_BRANCH "$GIT_BRANCH"

// Build information
#define BUILD_TIMESTAMP "$BUILD_TIMESTAMP"
#define BUILD_DATE "$BUILD_DATE"
#define BUILD_TIME "$BUILD_TIME"

// Convenience macros
#define VERSION_AT_LEAST(major, minor, patch) ( \\
    (VERSION_MAJOR > (major)) || \\
    (VERSION_MAJOR == (major) && VERSION_MINOR > (minor)) || \\
    (VERSION_MAJOR == (major) && VERSION_MINOR == (minor) && VERSION_PATCH >= (patch)) \\
)

#endif // VERSION_H
EOF

echo "[INFO] Version header generated: $VERSION_FILE"
echo "[INFO] Version: $VERSION_MAJOR.$VERSION_MINOR.$VERSION_PATCH"
echo "[INFO] Git commit: $GIT_COMMIT"
echo "[INFO] Git branch: $GIT_BRANCH"
echo "[INFO] Build time: $BUILD_TIMESTAMP"
