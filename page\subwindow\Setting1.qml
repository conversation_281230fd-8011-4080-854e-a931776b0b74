import QtQuick
import "../framework"

Item {
    id: control

    property int uiMainUnitSelect: mainUnitSelect // 1:high 2:rca

    property bool uiUsbAudioEnable: usbAudioEnable
    property bool uiBtAudioEnable: btAudioEnable
    property bool uiSpdifEnable: spdifEnable
    property bool uiAuxEnable: auxEnable

    property bool uiNaviMixEnable: naviMixEnable
    property alias uiNaviMixSensitivity: naviMixSensitivityComboBox.currentIndex
    property alias uiNaviMixDspAtt: naviMixDspAttComboBox.currentIndex
    property alias uiNaviMixReleaseTime: naviMixReleaseTimeComboBox.currentIndex

    property alias uiExCtrlAttControl: exCtrlAttControlComboBox.currentIndex
    property alias uiExCtrlPolarity: exCtrlPolarityComboBox.currentIndex
    property alias uiExCtrlDspSourceAttLevel: exCtrlDspSourceAttLevelComboBox.currentIndex

    property alias uiRemoteBrightness: remoteBrightnessComboBox.currentIndex
    property alias uiRemoteDimmer: remoteDimmerComboBox.currentIndex
    property alias uiRemotePolarity: remotePolarityComboBox.currentIndex
    property alias uiRemoteDimmerBrightness: remoteDimmerBrightnessComboBox.currentIndex

    property int mainUnitSelect: dataMap["mainUnitEnabledType"]
    property bool usbAudioEnable: dataMap["usbEnabled"]
    property bool btAudioEnable: dataMap["btEnabled"]
    property bool spdifEnable: dataMap["spdifEnabled"]
    property bool auxEnable: dataMap["auxEnabled"]

    property bool naviMixEnable: dataMap["naviMixEnable"]
    property int naviMixSensitivity: dataMap["naviSensitivity"]
    property int naviMixDspAtt: dataMap["naviAttenuation"]
    property int naviMixReleaseTime: dataMap["naviDuration"]

    property int exCtrlAttControl: dataMap["externalAttenuation"]
    property int exCtrlPolarity: (dataMap["externalPolarity"] + 1) % 2
    property int exCtrlDspSourceAttLevel: dataMap["externalDspAttenuation"]

    property int remoteBrightness: dataMap["remoteBrightness"]
    property int remoteDimmer: dataMap["remoteDimmer"]
    property int remotePolarity: (dataMap["remotePolarity"] + 1) % 2
    property int remoteDimmerBrightness: dataMap["remoteDimmerBrightness"]

    onUiMainUnitSelectChanged: {
        if(uiMainUnitSelect !== mainUnitSelect)
        {
            commonCtrl.setHighRcaEnable(uiMainUnitSelect)
        }
    }

    onUiUsbAudioEnableChanged: {
        if(uiUsbAudioEnable !== usbAudioEnable)
        {
            commonCtrl.setUsbEnable(uiUsbAudioEnable)
        }
    }

    onUiBtAudioEnableChanged: {
        if(uiBtAudioEnable !== btAudioEnable)
        {
            commonCtrl.setBtEnable(uiBtAudioEnable)
        }
    }

    onUiSpdifEnableChanged: {
        if(uiSpdifEnable !== spdifEnable)
        {
            commonCtrl.setSpdifEnable(uiSpdifEnable)
        }
    }

    onUiAuxEnableChanged: {
        if(uiAuxEnable !== auxEnable)
        {
            commonCtrl.setAuxEnable(uiAuxEnable)
        }
    }

    onUiNaviMixEnableChanged: {
        if(uiNaviMixEnable !== naviMixEnable)
        {
            commonCtrl.setNaviMixEnable(uiNaviMixEnable)
        }
    }

    onUiNaviMixSensitivityChanged: {
        if(uiNaviMixSensitivity !== naviMixSensitivity)
        {
            commonCtrl.setNaviSensitivity(uiNaviMixSensitivity)
        }
    }

    onUiNaviMixDspAttChanged: {
        if(uiNaviMixDspAtt !== naviMixDspAtt)
        {
            commonCtrl.setNaviAttenuation(uiNaviMixDspAtt)
        }
    }

    onUiNaviMixReleaseTimeChanged: {
        if(uiNaviMixReleaseTime !== naviMixReleaseTime)
        {
            commonCtrl.setNaviDuration(uiNaviMixReleaseTime)
        }
    }

    onUiExCtrlAttControlChanged: {
        if(uiExCtrlAttControl !== exCtrlAttControl)
        {
            commonCtrl.setExternalAttenuation(uiExCtrlAttControl)
        }
    }

    onUiExCtrlPolarityChanged: {
        if(uiExCtrlPolarity !== exCtrlPolarity)
        {
            commonCtrl.setExternalPolarity((uiExCtrlPolarity + 1) % 2)
        }
    }

    onUiExCtrlDspSourceAttLevelChanged: {
        if(uiExCtrlDspSourceAttLevel !== exCtrlDspSourceAttLevel)
        {
            commonCtrl.setExternalDspAttenuation(uiExCtrlDspSourceAttLevel)
        }
    }

    onUiRemoteBrightnessChanged: {
        if(uiRemoteBrightness !== remoteBrightness)
        {
            commonCtrl.setRemoteBrightness(uiRemoteBrightness)
        }
    }

    onUiRemoteDimmerChanged: {
        if(uiRemoteDimmer !== remoteDimmer)
        {
            commonCtrl.setRemoteDimmer(uiRemoteDimmer)
        }
    }

    onUiRemotePolarityChanged: {
        if(uiRemotePolarity !== remotePolarity)
        {
            commonCtrl.setRemotePolarity((uiRemotePolarity + 1) % 2)
        }
    }

    onUiRemoteDimmerBrightnessChanged: {
        if(uiRemoteDimmerBrightness !== remoteDimmerBrightness)
        {
            commonCtrl.setRemoteDimmerBrightness(uiRemoteDimmerBrightness)
        }
    }

    onMainUnitSelectChanged: {
        uiMainUnitSelect = mainUnitSelect
    }

    onUsbAudioEnableChanged: {
        uiUsbAudioEnable = usbAudioEnable
    }

    onBtAudioEnableChanged: {
        uiBtAudioEnable = btAudioEnable
    }

    onSpdifEnableChanged: {
        uiSpdifEnable = spdifEnable
    }

    onAuxEnableChanged: {
        uiAuxEnable = auxEnable
    }

    onNaviMixEnableChanged: {
        uiNaviMixEnable = naviMixEnable
    }

    onNaviMixSensitivityChanged: {
        uiNaviMixSensitivity = naviMixSensitivity
    }

    onNaviMixDspAttChanged: {
        uiNaviMixDspAtt = naviMixDspAtt
    }

    onNaviMixReleaseTimeChanged: {
        uiNaviMixReleaseTime = naviMixReleaseTime
    }

    onExCtrlAttControlChanged: {
        uiExCtrlAttControl = exCtrlAttControl
    }

    onExCtrlPolarityChanged: {
        uiExCtrlPolarity = exCtrlPolarity
    }

    onExCtrlDspSourceAttLevelChanged: {
        uiExCtrlDspSourceAttLevel = exCtrlDspSourceAttLevel
    }

    onRemoteBrightnessChanged: {
        uiRemoteBrightness = remoteBrightness
    }

    onRemoteDimmerChanged: {
        uiRemoteDimmer = remoteDimmer
    }

    onRemotePolarityChanged: {
        uiRemotePolarity = remotePolarity
    }

    onRemoteDimmerBrightnessChanged: {
        uiRemoteDimmerBrightness = remoteDimmerBrightness
    }

    Rectangle {
        id: mainUnitSelectFrame
        anchors.left: parent.left
        anchors.leftMargin: 24
        anchors.top: parent.top
        anchors.topMargin: 24
        width: 300
        height: 92
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: mainUnitSelectTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: mainUnitSelectTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Main Unit Select")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 16

            MyRadioButton {
                text: qsTr("Speaker Input")
                checked: (1 === uiMainUnitSelect)

                onClicked: uiMainUnitSelect = 1
            }

            MyRadioButton {
                text: qsTr("RCA Input")
                checked: (2 === uiMainUnitSelect)

                onClicked: uiMainUnitSelect = 2
            }
        }
    }

    Rectangle {
        id: musicSourceSettingFrame
        anchors.left: mainUnitSelectFrame.left
        anchors.top: mainUnitSelectFrame.bottom
        anchors.topMargin: 24
        width: 300
        height: 152
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: musicSourceSetting.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: musicSourceSetting
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("DSP Source Setting")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 16

            Row {
                visible: (1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 88
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "USB AUDIO"
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Valid")
                    checked: uiUsbAudioEnable

                    onClicked: uiUsbAudioEnable = true
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Invalid")
                    checked: !uiUsbAudioEnable

                    onClicked: uiUsbAudioEnable = false
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 88
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "BT AUDIO"
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Valid")
                    checked: uiBtAudioEnable

                    onClicked: uiBtAudioEnable = true
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Invalid")
                    checked: !uiBtAudioEnable

                    onClicked: uiBtAudioEnable = false
                }
            }

            Row {
                visible: (1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 88
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "SPDIF"
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Valid")
                    checked: uiSpdifEnable

                    onClicked: uiSpdifEnable = true
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Invalid")
                    checked: !uiSpdifEnable

                    onClicked: uiSpdifEnable = false
                }
            }

            Row {
                enabled: (1 !== mainUnitSelect) ? false : true

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 88
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: "AUX"
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Valid")
                    checked: uiAuxEnable

                    onClicked: uiAuxEnable = true
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Invalid")
                    checked: !uiAuxEnable

                    onClicked: uiAuxEnable = false
                }
            }
        }
    }

    Rectangle {
        id: naviMixSettingFrame
        anchors.left: mainUnitSelectFrame.right
        anchors.leftMargin: 24
        anchors.top: mainUnitSelectFrame.top
        width: 300
        height: 174
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: naviMixSettingTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: naviMixSettingTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Navi-Mix Setting")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 24

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 88
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Function")
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Valid")
                    checked: uiNaviMixEnable

                    onClicked: uiNaviMixEnable = true
                }

                MyRadioButton {
                    width: 80
                    height: 14
                    text: qsTr("Invalid")
                    checked: !uiNaviMixEnable

                    onClicked: uiNaviMixEnable = false
                }
            }

            Column {
                spacing: 8

                Row {
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 160
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Detection Sensitivity")
                    }

                    ComboBoxA {
                        id: naviMixSensitivityComboBox
                        width: 88
                        height: 24
                        model: ["-30dB", "-40dB", "-50dB", "-60dB"]
                    }
                }

                Row {
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 160
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("DSP Source ATT Level")
                    }

                    ComboBoxA {
                        id: naviMixDspAttComboBox
                        width: 88
                        height: 24
                        model: ["0dB", "-6dB", "-9dB", "-12dB", "MUTE"]
                    }
                }

                Row {
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 160
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Release Time")
                    }

                    ComboBoxA {
                        id: naviMixReleaseTimeComboBox
                        width: 88
                        height: 24
                        model: ["0.5sec", "1.0sec", "1.5sec", "2.0sec", "2.5sec", "3.0sec"]
                    }
                }
            }
        }
    }

    Rectangle {
        id: externalControlFrame
        anchors.left: naviMixSettingFrame.right
        anchors.leftMargin: 24
        anchors.top: naviMixSettingFrame.top
        width: 300
        height: 136
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: externalControlTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: externalControlTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("External ATT Control")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 8

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Function")
                }

                ComboBoxA {
                    id: exCtrlAttControlComboBox
                    width: 88
                    height: 24
                    model: [qsTr("Invalid"), qsTr("Valid")]
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Trigger Polarity")
                }

                ComboBoxA {
                    id: exCtrlPolarityComboBox
                    width: 88
                    height: 24
                    model: [qsTr("High"), qsTr("Low")]
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("DSP Source ATT Level")
                }

                ComboBoxA {
                    id: exCtrlDspSourceAttLevelComboBox
                    width: 88
                    height: 24
                    model: ["-6dB", "-9dB", "-12dB", "MUTE"]
                }
            }
        }
    }

    Rectangle {
        id: remoteBrightnessSettingFrame
        anchors.left: externalControlFrame.left
        anchors.top: externalControlFrame.bottom
        anchors.topMargin: 24
        width: 300
        height: 168
        color: "transparent"
        border.width: 1
        border.color: "#747880"
        visible: (1 !== dataMap["uiDataMap"]["deviceLevel"])

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: remoteBrightnessSettingTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: remoteBrightnessSettingTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Remote Brightness Setting")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 8

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Default")
                }

                ComboBoxA {
                    id: remoteBrightnessComboBox
                    width: 88
                    height: 24
                    model: ["1", "2", "3", "4", "5"]
                    currentIndex: remoteBrightness
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Function")
                }

                ComboBoxA {
                    id: remoteDimmerComboBox
                    width: 88
                    height: 24
                    model: [qsTr("Invalid"), qsTr("Valid")]
                    currentIndex: remoteDimmer
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Trigger Polarity")
                }

                ComboBoxA {
                    id: remotePolarityComboBox
                    width: 88
                    height: 24
                    model: [qsTr("High"), qsTr("Low")]
                    currentIndex: remotePolarity
                }
            }

            Row {
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 160
                    height: 8
                    color: "#E8E8E8"
                    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                    font.pixelSize: 12
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr("Dimmer Brightness")
                }

                ComboBoxA {
                    id: remoteDimmerBrightnessComboBox
                    width: 88
                    height: 24
                    model: ["1", "2", "3", "4", "5"]
                    currentIndex: remoteDimmerBrightness
                }
            }
        }
    }
}
