/*** 
 * Copyright (c) 2025 , NIO , LTD 
 * All Rights Reserved.
 * Product      : 
 * Component ID : 
 * File Name    : 
 * Description  : 
 * History      : 
 * Version		date		author		context
 * v1.0.0 		<date>		<user>		<context>
 */
/**
 * @file test_bessel_filter.cpp
 * @brief 贝塞尔滤波器测试程序
 * 
 * 验证所有1-8阶滤波器的精度，确保误差在1%以内
 * 
 * <AUTHOR> Assistant
 * @date 2024
 * @encoding UTF-8
 */

#include "../include/BesselFilter.h"
#include <iostream>
#include <iomanip>
#include <vector>
#include <fstream>

using namespace BesselFilter;

/**
 * @brief 运行精度测试
 */
void runAccuracyTest() {
    std::cout << "==================================================================" << std::endl;
    std::cout << "贝塞尔滤波器精度测试" << std::endl;
    std::cout << "目标：所有1-8阶滤波器偏差控制在1%以内" << std::endl;
    std::cout << "==================================================================" << std::endl;
    
    const std::vector<int> orders = {1, 2, 3, 4, 5, 6, 7, 8};
    const double lp_cutoff = 10000.0;  // 低通截止频率
    const double hp_cutoff = 500.0;    // 高通截止频率
    const double sample_rate = 96000.0;
    
    int total_tests = 0;
    int passed_tests = 0;
    
    // 测试低通滤波器
    std::cout << "\n低通滤波器测试 (目标: " << lp_cutoff << "Hz)" << std::endl;
    std::cout << "----------------------------------------------------------------" << std::endl;
    std::cout << std::setw(4) << "阶数" << std::setw(12) << "误差%" 
              << std::setw(8) << "状态" << std::endl;
    std::cout << "----------------------------------------------------------------" << std::endl;
    
    for (int order : orders) {
        try {
            double error = Designer::verifyAccuracy(lp_cutoff, order, FilterType::LOWPASS, sample_rate);
            bool passed = (error < 1.0);
            
            std::cout << std::setw(4) << order 
                      << std::setw(12) << std::fixed << std::setprecision(3) << error
                      << std::setw(8) << (passed ? "✓" : "✗") << std::endl;
            
            total_tests++;
            if (passed) passed_tests++;
            
        } catch (const std::exception& e) {
            std::cout << std::setw(4) << order << " 测试失败: " << e.what() << std::endl;
            total_tests++;
        }
    }
    
    // 测试高通滤波器
    std::cout << "\n高通滤波器测试 (目标: " << hp_cutoff << "Hz)" << std::endl;
    std::cout << "----------------------------------------------------------------" << std::endl;
    std::cout << std::setw(4) << "阶数" << std::setw(12) << "误差%" 
              << std::setw(8) << "状态" << std::endl;
    std::cout << "----------------------------------------------------------------" << std::endl;
    
    for (int order : orders) {
        try {
            double error = Designer::verifyAccuracy(hp_cutoff, order, FilterType::HIGHPASS, sample_rate);
            bool passed = (error < 1.0);
            
            std::cout << std::setw(4) << order 
                      << std::setw(12) << std::fixed << std::setprecision(3) << error
                      << std::setw(8) << (passed ? "✓" : "✗") << std::endl;
            
            total_tests++;
            if (passed) passed_tests++;
            
        } catch (const std::exception& e) {
            std::cout << std::setw(4) << order << " 测试失败: " << e.what() << std::endl;
            total_tests++;
        }
    }
    
    // 输出总结
    std::cout << "\n==================================================================" << std::endl;
    std::cout << "测试总结" << std::endl;
    std::cout << "==================================================================" << std::endl;
    std::cout << "通过测试: " << passed_tests << "/" << total_tests 
              << " (" << std::fixed << std::setprecision(1) 
              << (double)passed_tests / total_tests * 100.0 << "%)" << std::endl;
    
    if (passed_tests == total_tests) {
        std::cout << "\n🎉 所有滤波器都达到了1%精度目标！" << std::endl;
    } else {
        std::cout << "\n⚠️  还有 " << (total_tests - passed_tests) << " 个滤波器未达到1%目标" << std::endl;
    }
}

/**
 * @brief 演示基本用法
 */
void demonstrateUsage() {
    std::cout << "\n==================================================================" << std::endl;
    std::cout << "基本用法演示" << std::endl;
    std::cout << "==================================================================" << std::endl;
    
    try {
        // 设计一个4阶低通贝塞尔滤波器
        std::cout << "\n1. 设计4阶低通贝塞尔滤波器 (截止频率: 1000Hz)" << std::endl;
        auto coeffs = Designer::design(1000.0, 4, FilterType::LOWPASS, 48000.0);
        
        std::cout << "   分子系数 (b): ";
        for (size_t i = 0; i < coeffs.b.size(); ++i) {
            std::cout << std::scientific << std::setprecision(3) << coeffs.b[i];
            if (i < coeffs.b.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
        
        std::cout << "   分母系数 (a): ";
        for (size_t i = 0; i < coeffs.a.size(); ++i) {
            std::cout << std::scientific << std::setprecision(3) << coeffs.a[i];
            if (i < coeffs.a.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
        
        // 验证截止频率
        double actual_cutoff = Designer::find3dbFrequency(coeffs, 48000.0);
        double error = std::abs(actual_cutoff - 1000.0) / 1000.0 * 100.0;
        std::cout << "   实际截止频率: " << std::fixed << std::setprecision(1) << actual_cutoff << "Hz" << std::endl;
        std::cout << "   精度误差: " << std::fixed << std::setprecision(3) << error << "%" << std::endl;
        
        // 演示实时处理
        std::cout << "\n2. 实时处理演示" << std::endl;
        Processor processor;
        processor.initialize(coeffs);
        
        std::cout << "   处理测试信号..." << std::endl;
        std::vector<double> test_input = {1.0, 0.5, -0.3, 0.8, -0.2};
        std::vector<double> test_output(test_input.size());
        
        for (size_t i = 0; i < test_input.size(); ++i) {
            test_output[i] = processor.processSample(test_input[i]);
            std::cout << "   输入: " << std::setw(6) << std::fixed << std::setprecision(2) << test_input[i]
                      << " -> 输出: " << std::setw(6) << std::fixed << std::setprecision(2) << test_output[i] << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "演示过程中发生错误: " << e.what() << std::endl;
    }
}

/**
 * @brief 生成频率响应数据
 */
void generateFrequencyResponseData() {
    std::cout << "\n==================================================================" << std::endl;
    std::cout << "生成频率响应数据" << std::endl;
    std::cout << "==================================================================" << std::endl;
    
    try {
        // 生成测试数据
        std::ofstream file("frequency_response_data.csv");
        if (!file.is_open()) {
            std::cout << "无法创建输出文件" << std::endl;
            return;
        }
        
        file << "Frequency,LP2_dB,LP4_dB,LP6_dB,HP2_dB,HP4_dB,HP6_dB\n";
        
        auto frequencies = Designer::generateLogFrequencies(20.0, 20000.0, 200);
        
        // 设计几个测试滤波器
        auto lp2 = Designer::design(1000.0, 2, FilterType::LOWPASS, 48000.0);
        auto lp4 = Designer::design(1000.0, 4, FilterType::LOWPASS, 48000.0);
        auto lp6 = Designer::design(1000.0, 6, FilterType::LOWPASS, 48000.0);
        auto hp2 = Designer::design(1000.0, 2, FilterType::HIGHPASS, 48000.0);
        auto hp4 = Designer::design(1000.0, 4, FilterType::HIGHPASS, 48000.0);
        auto hp6 = Designer::design(1000.0, 6, FilterType::HIGHPASS, 48000.0);
        
        // 计算频率响应
        auto resp_lp2 = Designer::getFrequencyResponse(lp2, frequencies, 48000.0);
        auto resp_lp4 = Designer::getFrequencyResponse(lp4, frequencies, 48000.0);
        auto resp_lp6 = Designer::getFrequencyResponse(lp6, frequencies, 48000.0);
        auto resp_hp2 = Designer::getFrequencyResponse(hp2, frequencies, 48000.0);
        auto resp_hp4 = Designer::getFrequencyResponse(hp4, frequencies, 48000.0);
        auto resp_hp6 = Designer::getFrequencyResponse(hp6, frequencies, 48000.0);
        
        // 写入数据
        for (size_t i = 0; i < frequencies.size(); ++i) {
            file << frequencies[i] << ","
                 << resp_lp2.magnitude_db[i] << ","
                 << resp_lp4.magnitude_db[i] << ","
                 << resp_lp6.magnitude_db[i] << ","
                 << resp_hp2.magnitude_db[i] << ","
                 << resp_hp4.magnitude_db[i] << ","
                 << resp_hp6.magnitude_db[i] << "\n";
        }
        
        file.close();
        std::cout << "✓ 频率响应数据已保存到: frequency_response_data.csv" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "生成数据时发生错误: " << e.what() << std::endl;
    }
}

/**
 * @brief 主函数
 */
int main() {
    try {
        // 运行精度测试
        runAccuracyTest();
        
        // 演示基本用法
        demonstrateUsage();
        
        // 生成频率响应数据
        generateFrequencyResponseData();
        
        std::cout << "\n==================================================================" << std::endl;
        std::cout << "测试完成！" << std::endl;
        std::cout << "==================================================================" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "程序执行过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
}
