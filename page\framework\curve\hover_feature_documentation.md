# Canvas 悬停功能文档

## 功能概述

在 `canvasview.cpp` 中实现了鼠标悬停功能，使得当鼠标悬停在可选点上时，点的颜色会发生变化，但点击后颜色不会改变。

## 实现细节

### 1. 头文件修改 (canvasview.h)

- 添加了悬停事件处理函数声明：
  - `hoverEnterEvent(QHoverEvent* event)`
  - `hoverMoveEvent(QHoverEvent* event)`
  - `hoverLeaveEvent(QHoverEvent* event)`
  - `updateHoverState(const QPointF& hoverPos)`

- 添加了悬停状态成员变量：
  - `int m_hoveredPointIndex = -1` - 当前悬停的可调节点索引，-1表示无悬停

### 2. 源文件修改 (canvasview.cpp)

#### 构造函数修改
- 添加了 `setAcceptHoverEvents(true)` 来启用悬停事件处理
- 添加了 `#include <QHoverEvent>` 头文件包含

#### 悬停事件处理
- **hoverEnterEvent**: 鼠标进入Canvas区域时调用 `updateHoverState`
- **hoverMoveEvent**: 鼠标在Canvas区域内移动时调用 `updateHoverState`
- **hoverLeaveEvent**: 鼠标离开Canvas区域时清除悬停状态

#### 悬停状态更新 (updateHoverState)
- 检测鼠标位置是否在可调节点的15像素范围内
- 更新 `m_hoveredPointIndex` 变量
- 如果悬停状态发生变化，触发重绘

#### 绘制逻辑修改 (drawAdjustablePoints)
- 在绘制可调节点时增加了悬停状态判断
- 悬停状态的点显示曲线颜色，80%不透明度
- 优先级：选中状态 > 悬停状态 > 曾经选中过 > 从未选中过

#### 点击处理修改 (handleAdjustablePointClick)
- 在点击处理时清除悬停状态 (`m_hoveredPointIndex = -1`)
- 确保点击后悬停效果消失

## 颜色状态优先级

1. **选中状态**: 显示曲线颜色的实心圆，白色文字
2. **悬停状态**: 显示曲线颜色，80%不透明度
3. **曾经选中过**: 显示 #c8c8c8，70%不透明度
4. **从未选中过**: 显示 #606060，70%不透明度

## 交互行为

### 悬停行为
- 鼠标悬停在可调节点上时，点显示悬停颜色
- 鼠标移动到其他点时，之前的悬停效果消失，新点显示悬停效果
- 鼠标离开Canvas区域时，所有悬停效果消失

### 点击行为
- 点击可调节点时，点被选中并显示选中状态颜色
- 点击时悬停状态被清除
- 选中状态的颜色不会因为悬停而改变

### 检测范围
- 悬停检测范围：15像素
- 点击检测范围：10像素
- 悬停范围比点击范围稍大，提供更好的用户体验

## 性能考虑

- 使用节流重绘机制 (`throttledUpdate`) 避免频繁重绘
- 只在悬停状态实际发生变化时才触发重绘
- 悬停检测只针对当前活动曲线的可调节点

## 测试

可以使用 `canvas_hover_test.cpp` 文件来测试悬停功能：

```cpp
// 编译并运行测试
// 该测试演示了悬停状态的各种场景
demonstrateCanvasHoverFeature();
```

## 兼容性

- 该功能与现有的点击、拖拽功能完全兼容
- 不影响现有的选中状态管理
- 保持了原有的绘制性能和响应速度
