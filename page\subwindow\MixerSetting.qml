import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "../framework"

SubWindow {
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 1280
    contentHeight: 694
    titleStr: qsTr("Mixer Setting")
    confirmVisible: false
    cancelStr: qsTr("Close")

    onClickCancel: clickClose()

    contentItem: Item {
        TabBar {
            id: bar
            width: (itemWidth + 1) * 2 - 1 + padding * 2
            height: 26
            contentWidth: width - padding * 2
            contentHeight: height - padding
            padding: 1

            property int itemWidth: 96

            background: Rectangle {
                color: "#5C6068"
            }

            MyTabButton {
                text: "Main unit"
                selected: (0 === bar.currentIndex)
            }

            MyTabButton {
                text: "DSP Source"
                selected: (1 === bar.currentIndex)
            }
        }

        StackLayout {
            anchors.left: bar.left
            anchors.top: bar.bottom
            width: parent.width
            currentIndex: bar.currentIndex

            Rectangle {
                id: mu
                width: parent.width
                height: 574
                color: "#3C4048"
                border.color: "#5C6068"
                border.width: 1

                Rectangle {
                    x: bar.padding
                    width: bar.itemWidth
                    height: parent.border.width
                    color: "#3C4048"
                    visible: (0 === bar.currentIndex)
                }

                Column {
                    anchors.top: parent.top
                    anchors.topMargin: 26
                    anchors.left: parent.left
                    anchors.leftMargin: 12
                    spacing: 8

                    Repeater {
                        model: 4

                        Text {
                            height: 60
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Input " + (modelData + 1)
                        }
                    }

                    Repeater {
                        model: 4

                        Text {
                            height: 60
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Input " + (modelData + 5)
                            visible: ((1 === dataMap["mainUnitEnabledType"]) && (1 !== dataMap["uiDataMap"]["deviceLevel"]))
                        }
                    }
                }

                Row {
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.leftMargin: 72
                    spacing: 4

                    Repeater {
                        model: 6

                        Text {
                            width: 114
                            height: 26
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Out " + (modelData + 1)
                        }
                    }

                    Repeater {
                        model: 4

                        Text {
                            width: 114
                            height: 26
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Out " + (modelData + 7)
                            visible: (1 !== dataMap["uiDataMap"]["deviceLevel"])
                        }
                    }
                }

                Grid {
                    anchors.left: parent.left
                    anchors.leftMargin: 72
                    anchors.top: parent.top
                    anchors.topMargin: 26
                    columns: 10
                    columnSpacing: 4
                    rows: 8
                    rowSpacing: 8

                    Repeater {
                        model: 80

                        MixerItem {
                            sourceType: (2 !== dataMap["mainUnitEnabledType"]) ? 0 : 1
                            inputChannel: modelData / 10
                            outputChannel: modelData % 10
                            itemVisible: {
                                if(1 === dataMap["uiDataMap"]["deviceLevel"])
                                {
                                    return ((inputChannel < 4) && (outputChannel < 6))
                                }
                                else
                                {
                                    return ((2 !== dataMap["mainUnitEnabledType"]) || (inputChannel < 4))
                                }
                            }
                        }
                    }
                }
            }

            Rectangle {
                id: dsp
                width: parent.width
                height: 574
                color: "#3C4048"
                border.color: "#5C6068"
                border.width: 1

                Rectangle {
                    x:bar.padding + bar.itemWidth + 1
                    width: bar.itemWidth
                    height: parent.border.width
                    color: "#3C4048"
                    visible: (1 === bar.currentIndex)
                }

                Column {
                    anchors.top: parent.top
                    anchors.topMargin: 26
                    anchors.left: parent.left
                    anchors.leftMargin: 12
                    spacing: 8

                    Text {
                        height: 60
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: "Input L"
                    }

                    Text {
                        height: 60
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        text: "Input R"
                    }
                }

                Row {
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.leftMargin: 72
                    spacing: 4

                    Repeater {
                        model: 6

                        Text {
                            width: 114
                            height: 26
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Out " + (modelData + 1)
                        }
                    }

                    Repeater {
                        model: 4

                        Text {
                            width: 114
                            height: 26
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            text: "Out " + (modelData + 7)
                            visible: (1 !== dataMap["uiDataMap"]["deviceLevel"])
                        }
                    }
                }

                Grid {
                    anchors.left: parent.left
                    anchors.leftMargin: 72
                    anchors.top: parent.top
                    anchors.topMargin: 26
                    columns: 10
                    columnSpacing: 4
                    rows: 2
                    rowSpacing: 8

                    Repeater {
                        model: 20

                        MixerItem {
                            sourceType: 2
                            inputChannel: modelData / 10
                            outputChannel: modelData % 10
                            itemVisible: (1 === dataMap["uiDataMap"]["deviceLevel"]) ? ((outputChannel < 6) ? true : false) : true
                        }
                    }
                }
            }
        }
    }
}
