function Component() { 
    // 构造函数 
} 
 
Component.prototype.createOperations = function() { 
    // 默认操作 
    component.createOperations(); 
 
    if (systemInfo.productType === "windows") { 
        // 创建快捷方式 
        component.addOperation("CreateShortcut", 
            "@TargetDir@/DSP Controller.exe", 
            "@DesktopDir@/DSP Controller.lnk", 
            "workingDirectory=@TargetDir@", 
            "iconPath=@TargetDir@/DSP Controller.exe", 
            "description=DSP Controller"); 
 
        component.addOperation("CreateShortcut", 
            "@TargetDir@/DSP Controller.exe", 
            "@StartMenuDir@/DSP Controller.lnk", 
            "workingDirectory=@TargetDir@", 
            "iconPath=@TargetDir@/DSP Controller.exe", 
            "description=DSP Controller"); 
    } 
} 
 
Component.prototype.beginInstallation = function() { 
    var targetDir = installer.value("TargetDir"); 
    var currentVersion = "1.0.0"; 
 
    // 允许覆盖安装，不进行任何阻止操作 
    if (installer.fileExists(targetDir)) { 
        console.log("Target directory exists, proceeding with overwrite installation..."); 
 
        // 尝试停止正在运行的程序 
        var mainProgram = targetDir + "/DSP Controller.exe"; 
        if (installer.fileExists(mainProgram)) { 
            try { 
                // 尝试终止正在运行的程序 
                installer.execute("taskkill", ["/F", "/IM", "DSP Controller.exe"]); 
            } catch (e) { 
                // 忽略错误，程序可能没有运行 
            } 
 
            // 删除旧的主程序文件（在文件复制之前） 
            try { 
                installer.performOperation("Delete", mainProgram); 
            } catch (e) { 
                console.log("Could not delete old executable: " + e.message); 
            } 
        } 
 
        // 删除旧的MaintenanceTool（如果存在） 
        var maintenanceTool = targetDir + "/MaintenanceTool.exe"; 
        if (installer.fileExists(maintenanceTool)) { 
            try { 
                installer.performOperation("Delete", maintenanceTool); 
            } catch (e) { 
                console.log("Could not delete old maintenance tool: " + e.message); 
            } 
        } 
    } 
 
    // 继续安装，允许覆盖所有文件 
    console.log("Installation proceeding with version: " + currentVersion); 
} 
