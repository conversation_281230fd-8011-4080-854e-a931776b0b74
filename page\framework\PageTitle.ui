<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PageTitle</class>
 <widget class="QFrame" name="PageTitle">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1366</width>
    <height>48</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1280</width>
    <height>48</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>48</height>
   </size>
  </property>
  <property name="styleSheet">
   <string notr="true">#PageTitle {
    background-color: #242830;
}
QPushButton {
	background-color: #484C54;
	border: 1px solid #646870;
	color: #E8E8E8;
}
QPushButton:hover {
	background-color: #5C6068;
	border: 1px solid #7C8088;
}
QPushButton:pressed {
	background-color: #3C4048;
	border: 1px solid #646870;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>17</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>6</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="deviceImg">
     <property name="minimumSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/dspConnected.png);</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="deviceType">
     <property name="minimumSize">
      <size>
       <width>133</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>133</width>
       <height>48</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
       <pointsize>-1</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
	font-size: 14px;
	color: #C8D8FF;
	padding: 12px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="minimumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #5C6068;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="generalSettings">
     <property name="minimumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
       <pointsize>-1</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: #00242830;
	border: 0px;
	font-size: 12px;
	color: #E8E8E8;
	margin: 10px;
}
QPushButton::menu-indicator {
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}
QPushButton::menu-indicator:open {
	image: url(:/Image/up.png);
}</string>
     </property>
     <property name="text">
      <string>Setting</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="minimumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #5C6068;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="memorySettings">
     <property name="minimumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
       <pointsize>-1</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	background-color: #00242830;
	border: 0px;
	font-size: 12px;
	color: #E8E8E8;
	margin: 10px;
}
QPushButton::menu-indicator {
	subcontrol-position: center right;
	image: url(:/Image/down.png);
}
QPushButton::menu-indicator:open {
	image: url(:/Image/up.png);
}</string>
     </property>
     <property name="text">
      <string>Memory Management</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_3">
     <property name="minimumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #5C6068;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="mixSettings">
     <property name="minimumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
       <pointsize>-1</pointsize>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	border: 0px;
	background-color: #00383744;
	font-size: 12px;
	color: #FFFFFF;
}</string>
     </property>
     <property name="text">
      <string>Mixer Setting</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_4">
     <property name="minimumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>2</width>
       <height>26</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #5C6068;</string>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="delaySettings">
     <property name="minimumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>144</width>
       <height>48</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Segoe UI</family>
      </font>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
	border: 0px;
	background-color: #00383744;
}</string>
     </property>
     <property name="text">
      <string>Time Alignment</string>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="min">
     <property name="minimumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/hide.png);
background-color: #00242830;
border: 0px;</string>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>6</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="max">
     <property name="minimumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
	image: url(:/Image/full.png);
	background-color: #00242830;
	border: 0px;
}
QPushButton:checked {
	image: url(:/Image/window.png);
}</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_3">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeType">
      <enum>QSizePolicy::Policy::Fixed</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>6</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="close">
     <property name="minimumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>56</width>
       <height>48</height>
      </size>
     </property>
     <property name="focusPolicy">
      <enum>Qt::FocusPolicy::NoFocus</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">image: url(:/Image/close.png);
background-color: #00242830;
border: 0px;</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
