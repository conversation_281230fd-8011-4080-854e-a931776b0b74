#pragma once
#include <QObject>
#include <QPointF>
#include <QVector>
#include <QColor>
#include <QMap>

#include "common/common.h"

/**
 * @brief 曲线数据模型类
 * 负责管理多条曲线的数据、颜色和可见性状态
 */
class DataModel : public QObject {
    Q_OBJECT
public:
    // 新增：点数量常量定义
    static const int ADJUSTABLE_POINTS_COUNT = 31;    // 可见可调节点数量
    static const int INVISIBLE_POINTS_COUNT = 5;      // 不可见点数量（32~36）
    static const int TOTAL_POINTS_COUNT = 36;         // 总点数量

    // 曲线类型枚举
    enum LineType {
        LINE_TYPE_NORMAL = 0,  // 普通曲线
        LINE_TYPE_BEZIER = 1,  // 贝塞尔曲线
        LINE_TYPE_STEP = 2     // 阶梯曲线
    };

    // EQ类型枚举（弱类型枚举）
    // enum EQType {
    //     EQ_TYPE_PEAK = 0x00,    // 峰值
    //     EQ_TYPE_LP1 = 0x01,     // 低通1阶
    //     EQ_TYPE_LP2 = 0x02,     // 低通2阶
    //     EQ_TYPE_LS1 = 0x03,     // 低架1阶
    //     EQ_TYPE_HS1 = 0x04,     // 高架1阶
    //     EQ_TYPE_LP2_ALT = 0x05, // 低通2阶(替代)
    //     EQ_TYPE_HP2 = 0x06,     // 高通2阶
    //     EQ_TYPE_LS2 = 0x07,     // 低架2阶
    //     EQ_TYPE_HS2 = 0x08,     // 高架2阶
    //     EQ_TYPE_NOTCH = 0x09,   // 陷波
    //     EQ_TYPE_AP1 = 0x0a,     // 全通1阶
    //     EQ_TYPE_AP2 = 0x0b,     // 全通2阶
    //     EQ_TYPE_BP2 = 0x0c      // 带通2阶
    // };


        enum EQType {
        EQ_TYPE_PEAK = 0x00,    // 峰值
        EQ_TYPE_LP1 = 0x01,     // 低通1阶
        EQ_TYPE_LP2 = 0x02,     // 低通2阶
        EQ_TYPE_LS1 = 0x03,     // 低架1阶
        EQ_TYPE_HS1 = 0x04,     // 高架1阶
        EQ_TYPE_LP2_ALT = 0x05, // 低通2阶(替代)
        EQ_TYPE_HP2 = 0x06,     // 高通2阶
        EQ_TYPE_LS2 = 0x07,     // 低架2阶
        EQ_TYPE_HS2 = 0x08,     // 高架2阶
        EQ_TYPE_LS  = 0x07,     // 低架2阶 同LS2
        EQ_TYPE_HS  = 0x08,     // 高架2阶 同HS2
        EQ_TYPE_NOTCH = 0x09,   // 陷波
        EQ_TYPE_AP1 = 0x0a,     // 全通1阶
        EQ_TYPE_AP2 = 0x0b,     // 全通2阶
        EQ_TYPE_AP  = 0x0b,     // 同AP2
        EQ_TYPE_BP2 = 0x0c      // 带通2阶
    };

    // 通用滤波参数结构体
    struct FilterParams {
        int filterType = 0; // 0:无, 1:biquad, 2:1st, 3:iir, 4:combined
        int order = 2;      // 阶数
        int passType = 1;   // 1:低通, 0:高通
        double fc = 1000.0; // 截止频率
        double q = 4.320;   // Q值
        double gain = 0.0;  // 增益
        int slope = 12;     // 斜率
    };

    // 可调节点详细数据结构体
    struct AdjustablePointData {
        double frequency;     // 频率 (Hz)
        double qValue;        // Q值
        double gain;          // 增益 (dB)
        EQType type;          // 类型 (对应EQ_type定义)
        QPointF position;     // 点的位置 (x, y)
        bool hasBeenSelected; // 是否被选中过，默认未被选中过

        // 构造函数，设置默认值
        AdjustablePointData()
            : frequency(1000.0), qValue(4.32), gain(0.0), type(EQ_TYPE_PEAK), position(0.0, 0.0), hasBeenSelected(false) {}

        // 带参数的构造函数
        AdjustablePointData(double freq, double q, double g, EQType t, const QPointF& pos = QPointF(0.0, 0.0), bool selected = false)
            : frequency(freq), qValue(q), gain(g), type(t), position(pos), hasBeenSelected(selected) {}
    };

    /**
     * @brief 获取单例实例
     * @return DataModel单例指针
     */
    static DataModel* getInstance();

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance();

private:
    /**
     * @brief 私有构造函数
     * 初始化曲线颜色和可见性状态，并生成初始数据
     */
    explicit DataModel(QObject* parent = nullptr);

    /**
     * @brief 私有析构函数
     */
    ~DataModel();

    // 禁用拷贝构造和赋值操作
    DataModel(const DataModel&) = delete;
    DataModel& operator=(const DataModel&) = delete;

    static DataModel* m_instance;  // 单例实例指针

public:

    // 获取所有曲线的点数据
    const QVector<QVector<QPointF>>& allPoints() const { return m_allPoints; }
    // 获取所有曲线的颜色
    const QVector<QColor>& colors() const { return m_colors; }
    // 获取所有曲线的可见性状态
    const QVector<bool>& visibility() const { return m_visibility; }

    // 设置指定曲线的点数据
    void setPoints(int curveIndex, const QVector<QPointF>& points);
    // 更新指定曲线的指定点位置
    void updatePoint(int curveIndex, int pointIndex, const QPointF& point);
    // 设置点的密度倍数
    void setMultiplier(int multiplier);
    // 获取点的密度倍数
    int getMultiplier() const { return m_multiplier; }
    // 重新生成所有曲线的点数据
    void generateAllPoints();
    // 重新生成指定通道曲线的点数据
    void generatePoints(int chIndex);
    // 获取曲线总数
    int curveCount() const { return int(MAX_CURVES); }

    // 控制曲线显示/隐藏
    void setCurveVisible(int curveIndex, bool visible);
    bool isCurveVisible(int curveIndex) const;

    // 获取红点位置
    QPointF redPoint() const { return m_redPoint; }
    // 获取当前曲线类型
    LineType lineType() const { return m_lineType; }

#if 1
public:
    // 更新曲线类型 todo
    void updateLineType(int lintType);
    // 更新红点位置
    void updateRedPoint(QPointF point);

    // 显示哪几条, 当前选中的是哪一条
    void updateLineDisplayStatus(int currentLine, QMap<int, bool> lineDisplayState);

    // 是否可以左右拖拽 todo
    void updateRedPointLateralChangeState(bool enable = false);
    // 获取是否可以左右拖拽的状态
    bool redPointLateralChangeState() const { return m_redPointLateralChange; }

    // 新增：控制可调节点是否可以横向拖拽
    void setAdjustablePointsLateralChangeState(bool enable = true);
    // 获取可调节点是否可以横向拖拽的状态
    bool adjustablePointsLateralChangeState() const { return m_adjustablePointsLateralChange; }

    // 新增：控制可调节点是否可以纵向拖拽
    void setAdjustablePointsVerticalChangeState(bool enable = true);
    // 获取可调节点是否可以纵向拖拽的状态
    bool adjustablePointsVerticalChangeState() const { return m_adjustablePointsVerticalChange; }

    // 更新某条曲线的所有数据
    void updateLineData(int lineIndex, QVector<QPointF> points); // x:(0,30) y:(-20,20)

    // 更新某条曲线的固定点数据
    void updateLinePoint(int lineIndex, int pointIndex ,QPointF point);
#endif

    // 新增：设置滤波器类型、截止频率、斜率
    void setCurveFilterParams(int curveIndex, int filterType, double fc, int slope);
    // 获取滤波器类型
    int curveFilterType(int curveIndex) const;
    // 获取截止频率
    double curveFc(int curveIndex) const;
    // 获取斜率
    int curveSlope(int curveIndex) const;

    const QVector<QPointF>& customPoints(int lineIndex) const;
    void setCustomPoint(int lineIndex, int pointIndex, const QPointF& pt);
    void setCustomPoints(int lineIndex, const QVector<QPointF>& pts);
    void clearCustomPoints(int lineIndex);

    // 新增：获取指定曲线的点数据
    const QVector<QPointF>& getPoints(int curveIndex) const;

    // 通用滤波处理接口
    void processFilter(QVector<QPointF>& array, const FilterParams& params);
    // get/set接口
    void setFilterParams(int curveIndex, const FilterParams& params);
    FilterParams getFilterParams(int curveIndex) const;

    // 设置/获取每条曲线的高通参数
    void setCurveHighpassParam(int curveIndex, const FilterParamsUniversal& param);
    FilterParamsUniversal getCurveHighpassParam(int curveIndex) const;
    // 设置/获取每条曲线的低通参数
    void setCurveLowpassParam(int curveIndex, const FilterParamsUniversal& param);
    FilterParamsUniversal getCurveLowpassParam(int curveIndex) const;

    // 高通参数单项设置/获取
    void setCurveHighpassFc(int curveIndex, double fc);
    double getCurveHighpassFc(int curveIndex) const;
    void setCurveHighpassType(int curveIndex, FilterTypeUniversal type);
    FilterTypeUniversal getCurveHighpassType(int curveIndex) const;
    void setCurveHighpassSlope(int curveIndex, int slope);
    int getCurveHighpassSlope(int curveIndex) const;
    // 低通参数单项设置/获取
    void setCurveLowpassFc(int curveIndex, double fc);
    double getCurveLowpassFc(int curveIndex) const;
    void setCurveLowpassType(int curveIndex, FilterTypeUniversal type);
    FilterTypeUniversal getCurveLowpassType(int curveIndex) const;
    void setCurveLowpassSlope(int curveIndex, int slope);
    int getCurveLowpassSlope(int curveIndex) const;

    // 初始化所有曲线的高通/低通参数
    void initCurveFilterParams(int curveCount = MAX_EQ_FILTERS);

    // 新增：31个可选点相关方法
    const QVector<QPointF>& getAdjustablePoints(int curveIndex) const;
    void initAdjustablePoints();
    void updateAdjustablePoint(int curveIndex, int pointIndex, const QPointF& point);

    // 新增：获取和设置可调节点详细数据
    const AdjustablePointData& getAdjustablePointData(int curveIndex, int pointIndex) const;
    void setAdjustablePointData(int curveIndex, int pointIndex, const AdjustablePointData& data);
    void updateAdjustablePointData(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type = EQ_TYPE_PEAK);

    // 新增：选中点相关方法
    int getSelectedPointIndex() const { return m_selectedPointIndex; }
    int getSelectedCurveIndex() const { return m_selectedCurveIndex; }
    void setSelectedPoint(int curveIndex, int pointIndex);
    void clearSelectedPoint();

    // 新增：获取点的F、Q、G值
    double getPointFrequency(int curveIndex, int pointIndex) const;
    double getPointQValue(int curveIndex, int pointIndex) const;
    double getPointGain(int curveIndex, int pointIndex) const;
    EQType getPointType(int curveIndex, int pointIndex) const;

    // 新增：高低通滤波点相关方法
    QPointF getHighpassPoint(int curveIndex) const;
    QPointF getLowpassPoint(int curveIndex) const;
    void updateHighpassPoint(int curveIndex, double xValue);
    void updateLowpassPoint(int curveIndex, double xValue);

    // 新增：设置可调节点参数
    void setAdjustablePointParams(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type = EQ_TYPE_PEAK);

    // 新增：设置高通滤波点参数
    void setHighpassPointParams(int curveIndex, double frequency, FilterTypeUniversal type, int slope);

    // 新增：设置低通滤波点参数
    void setLowpassPointParams(int curveIndex, double frequency, FilterTypeUniversal type, int slope);

    // 新增：获取高通滤波点完整参数
    void getHighpassPointParams(int curveIndex, double& frequency, FilterTypeUniversal& type, int& slope) const;

    // 新增：获取低通滤波点完整参数
    void getLowpassPointParams(int curveIndex, double& frequency, FilterTypeUniversal& type, int& slope) const;

    // 新增：高低通滤波点显示控制（兼容接口）
    bool isFilterPointsVisible() const { return m_highpassPointVisible && m_lowpassPointVisible; }
    void setFilterPointsVisible(bool visible);

    // 新增：独立的高通和低通滤波点显示控制
    bool isHighpassPointVisible() const { return m_highpassPointVisible; }
    bool isLowpassPointVisible() const { return m_lowpassPointVisible; }
    void setHighpassPointVisible(bool visible);
    void setLowpassPointVisible(bool visible);

    // 新增：32~36号不可见点操作接口
    void setInvisiblePointParams(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type = EQ_TYPE_PEAK);
    void getInvisiblePointParams(int curveIndex, int pointIndex, double& frequency, double& qValue, double& gain, EQType& type) const;
    void updateInvisiblePointData(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type = EQ_TYPE_PEAK);
    const AdjustablePointData& getInvisiblePointData(int curveIndex, int pointIndex) const;

    // 新增：批量设置某个通道的所有不可见点
    void setInvisiblePointsForCurve(int curveIndex, const QVector<AdjustablePointData>& invisiblePointsData);
    QVector<AdjustablePointData> getInvisiblePointsForCurve(int curveIndex) const;

    // 辅助函数：将FilterTypeUniversal枚举值转换为字符串
    QString filterTypeToString(FilterTypeUniversal type) const;

    // 新增：预设相关枚举定义
    enum PresetType {
        PRESET_TYPE_FLAT = 0,         // 平坦预设（关闭效果）
        PRESET_TYPE_SUPERBASS = 1,    // 超低音
        PRESET_TYPE_POWERFUL = 2,     // 澎湃
        PRESET_TYPE_VOCAL = 3,        // 人声
        PRESET_TYPE_NATURAL = 4       // 自然
    };

    enum PresetLevel {
        PRESET_LEVEL_1 = 1,
        PRESET_LEVEL_2 = 2,
        PRESET_LEVEL_3 = 3,
        PRESET_LEVEL_4 = 4,
        PRESET_LEVEL_5 = 5
    };

    // 新增：预设相关接口
    void setCurvePreset(int curveIndex, PresetType type, PresetLevel level);
    PresetType getCurvePresetType(int curveIndex) const;
    PresetLevel getCurvePresetLevel(int curveIndex) const;
    bool isCurvePresetEnabled(int curveIndex) const;
    bool isCurvePresetActive(int curveIndex) const; // 检查是否启用了非Flat预设
    void clearCurvePreset(int curveIndex);

    // 预设类型和等级转换函数
    QString presetTypeToString(PresetType type) const;
    QString presetLevelToString(PresetLevel level) const;

signals:
    // 数据变化信号
    void dataChanged();
    // 可见性变化信号
    void visibilityChanged();
    // 红点位置变化信号
    void redpointUpdated(int lineIndex, int pointIndex, float xPercentage, float yPercentage); //pointIndex(0-30) 更新百分比 x(0~2000) / 2000; y(-20,20) / 40
    void redpointUpdated(int lineIndex, int pointIndex, int frequency, float gain); //pointIndex(0-30) 更新实际参数 frequency (20~40k); y(-20.0,20.0) / 40

    // 新增：点位置变化信号
    void pointPositionChanged(int curveIndex, int pointIndex, const QPointF& position);
    // 新增：选中点变化信号
    void selectedPointChanged(int curveIndex, int pointIndex);
    // 新增：高通滤波点变化信号
    void highpassPointChanged(int curveIndex, double frequency);
    // 新增：低通滤波点变化信号
    void lowpassPointChanged(int curveIndex, double frequency);
    // 新增：可调节点变化信号，包含点编号和F、Q、G信息
    void adjustablePointChanged(int curveIndex, int pointIndex, double frequency, double qValue, double gain, EQType type = EQ_TYPE_PEAK);
    // 新增：高通滤波点完整参数变化信号
    void highpassPointParamsChanged(int curveIndex, double frequency, FilterTypeUniversal type, int slope);
    // 新增：低通滤波点完整参数变化信号
    void lowpassPointParamsChanged(int curveIndex, double frequency, FilterTypeUniversal type, int slope);
    // 新增：高低通滤波点显示状态变化信号
    void filterPointsVisibilityChanged(bool visible);
    // 新增：独立的高通和低通滤波点显示状态变化信号
    void highpassPointVisibilityChanged(bool visible);
    void lowpassPointVisibilityChanged(bool visible);
    // 新增：预设变化信号
    void curvePresetChanged(int curveIndex, PresetType type, PresetLevel level);
    // 注意：不再需要curvePresetCleared信号，清除操作发送curvePresetChanged(Empty)

private:
    QVector<QVector<QPointF>> m_allPoints;  // 所有曲线的点数据
    QVector<QColor> m_colors;               // 所有曲线的颜色
    QVector<bool> m_visibility;             // 所有曲线的可见性状态
    int m_multiplier = 8;                   // 点的密度倍数
    const int MAX_CURVES = 14;              // 最大曲线数量

    // 新增：封装曲线点更新的通用逻辑
    void processLinePointsWithFilter(int lineIndex, QVector<QPointF>& array);

    LineType m_lineType = LINE_TYPE_NORMAL;     // 当前曲线类型
    QPointF m_redPoint{2.5, 6.25};             // 红点位置
    bool m_redPointLateralChange = false;       // 红点是否可以左右拖拽
    int m_currentLine = 0;                      // 当前选中的曲线
    QMap<int, bool> m_lineDisplayState;         // 曲线显示状态

    // 新增：每条曲线的滤波器参数
    QVector<FilterParamsUniversal> m_curveHighpassParams; // 每条曲线的高通参数
    QVector<FilterParamsUniversal> m_curveLowpassParams;  // 每条曲线的低通参数

    QVector<QVector<QPointF>> m_customPoints;
    QVector<FilterParams> m_curveFilterParams;

    // 新增：31个可调节点
    QVector<QVector<QPointF>> m_adjustablePoints; // 每条曲线的31个可调点
    QVector<QVector<AdjustablePointData>> m_adjustablePointsData; // 每条曲线的31个可调点的详细数据
    int m_selectedPointIndex = -1;               // 当前选中的点索引
    int m_selectedCurveIndex = -1;               // 当前选中的曲线索引

    // 新增：高低通滤波点显示控制
    bool m_filterPointsVisible = true;           // 高低通滤波点是否可见，默认开启（兼容接口）
    bool m_highpassPointVisible = true;          // 高通滤波点是否可见，默认开启
    bool m_lowpassPointVisible = true;           // 低通滤波点是否可见，默认开启

    // 新增：控制可调节点是否可以横向拖拽
    bool m_adjustablePointsLateralChange = true; // 可调节点是否可以横向拖拽，默认允许
    // 新增：控制可调节点是否可以纵向拖拽
    bool m_adjustablePointsVerticalChange = true; // 可调节点是否可以纵向拖拽，默认允许

    // 新增：预设相关成员变量
    QVector<PresetType> m_curvePresetTypes;      // 每条曲线的预设类型
    QVector<PresetLevel> m_curvePresetLevels;    // 每条曲线的预设等级
    QVector<bool> m_curvePresetEnabled;          // 每条曲线是否启用预设

    // 新增：预设管理器
    void initPresetData();
    class PresetManager* m_presetManager;  // 预设管理器实例
};
