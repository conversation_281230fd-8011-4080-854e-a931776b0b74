/**
 * @file preset_usage_example.cpp
 * @brief PresetManager使用示例
 *
 * 此文件展示如何使用PresetManager来管理和应用音效预设
 */

#include "preset_manager.h"
#include "datamodel.h"
#include <QCoreApplication>
#include <QDebug>

/**
 * @brief 使用示例函数
 * 展示如何使用PresetManager的各种功能
 */
void presetManagerUsageExample()
{
    qDebug() << "=== PresetManager 使用示例 ===";

    // 1. 创建数据模型和预设管理器
    DataModel* dataModel = new DataModel();
    PresetManager* presetManager = new PresetManager();

    // 2. 获取预设数据示例
    qDebug() << "\n--- 获取预设数据示例 ---";
    PresetManager::PresetData superBassLevel5 = presetManager->getPresetData(
        PresetManager::SuperBass, PresetManager::Level5);

    qDebug() << "SuperBass Level5 预设数据:";
    qDebug() << "  100Hz:" << superBassLevel5.gain100Hz << "dB";
    qDebug() << "  315Hz:" << superBassLevel5.gain315Hz << "dB";
    qDebug() << "  1250Hz:" << superBassLevel5.gain1250Hz << "dB";
    qDebug() << "  3150Hz:" << superBassLevel5.gain3150Hz << "dB";
    qDebug() << "  8000Hz:" << superBassLevel5.gain8000Hz << "dB";

    // 3. 应用预设到通道示例
    qDebug() << "\n--- 应用预设示例 ---";
    int curveIndex = 0; // 第一个通道

    // 应用SuperBass Level3预设
    bool success = presetManager->applyPreset(dataModel, curveIndex,
                                             PresetManager::SuperBass,
                                             PresetManager::Level3);

    if (success) {
        qDebug() << "成功应用SuperBass Level3预设到通道" << curveIndex;

        // 验证应用结果
        QVector<DataModel::AdjustablePointData> invisiblePoints =
            dataModel->getInvisiblePointsForCurve(curveIndex);

        qDebug() << "验证不可见点数据:";
        for (int i = 0; i < invisiblePoints.size(); ++i) {
            const auto& point = invisiblePoints[i];
            qDebug() << "  点" << (i + 32) << ":"
                     << "频率=" << point.frequency << "Hz"
                     << ", 增益=" << point.gain << "dB"
                     << ", Q值=" << point.qValue
                     << ", 类型=" << static_cast<int>(point.type);
        }
    } else {
        qDebug() << "应用预设失败";
    }

    // 4. 遍历所有预设类型和级别
    qDebug() << "\n--- 所有可用预设 ---";
    QVector<PresetManager::PresetType> types = PresetManager::getAllPresetTypes();
    QVector<PresetManager::PresetLevel> levels = PresetManager::getAllPresetLevels();

    for (PresetManager::PresetType type : types) {
        QString typeName = PresetManager::getPresetTypeName(type);
        qDebug() << "预设类型:" << typeName;

        for (PresetManager::PresetLevel level : levels) {
            QString levelName = PresetManager::getPresetLevelName(level);
            PresetManager::PresetData data = presetManager->getPresetData(type, level);
            qDebug() << "  " << levelName << ": ["
                     << data.gain100Hz << ","
                     << data.gain315Hz << ","
                     << data.gain1250Hz << ","
                     << data.gain3150Hz << ","
                     << data.gain8000Hz << "]";
        }
    }

    // 5. 字符串转换示例
    qDebug() << "\n--- 字符串转换示例 ---";
    PresetManager::PresetType typeFromString = PresetManager::getPresetTypeFromString("Vocal");
    PresetManager::PresetLevel levelFromString = PresetManager::getPresetLevelFromString("Level4");

    qDebug() << "从字符串转换: Vocal ->" << static_cast<int>(typeFromString);
    qDebug() << "从字符串转换: Level4 ->" << static_cast<int>(levelFromString);

    // 6. 应用不同预设到不同通道
    qDebug() << "\n--- 多通道预设应用示例 ---";
    struct PresetConfig {
        int channel;
        PresetManager::PresetType type;
        PresetManager::PresetLevel level;
        QString description;
    };

    QVector<PresetConfig> configs = {
        {0, PresetManager::SuperBass, PresetManager::Level5, "左前声道 - SuperBass Level5"},
        {1, PresetManager::Vocal, PresetManager::Level3, "右前声道 - Vocal Level3"},
        {2, PresetManager::Powerful, PresetManager::Level4, "左后声道 - Powerful Level4"},
        {3, PresetManager::Natural, PresetManager::Level2, "右后声道 - Natural Level2"}
    };

    for (const auto& config : configs) {
        bool result = presetManager->applyPreset(dataModel, config.channel,
                                               config.type, config.level);
        qDebug() << config.description << (result ? "应用成功" : "应用失败");
    }

    // 7. 信号连接示例（在实际使用中）
    qDebug() << "\n--- 信号连接示例代码 ---";
    qDebug() << "// 连接预设应用成功信号";
    qDebug() << "QObject::connect(presetManager, &PresetManager::presetApplied,";
    qDebug() << "                [](int channel, PresetManager::PresetType type, PresetManager::PresetLevel level) {";
    qDebug() << "    qDebug() << \"预设应用成功: 通道\" << channel";
    qDebug() << "             << \", 类型\" << PresetManager::getPresetTypeName(type)";
    qDebug() << "             << \", 级别\" << PresetManager::getPresetLevelName(level);";
    qDebug() << "});";

    // 清理资源
    delete dataModel;
    delete presetManager;

    qDebug() << "\n=== 示例结束 ===";
}

/**
 * @brief 主函数（仅用于示例，实际项目中不需要）
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    // 运行使用示例
    presetManagerUsageExample();

    return 0;
}

// ===== DataModel 预设接口使用示例 =====

/**
 * @brief DataModel预设接口使用示例
 * 演示如何使用DataModel内置的预设功能
 */
void demonstrateDataModelPresetUsage()
{
    qDebug() << "\n===== DataModel 预设接口使用示例 =====";

    // 创建DataModel实例
    DataModel* dataModel = new DataModel();

    // 示例0：验证初始状态为Flat预设
    qDebug() << "\n=== 示例0：验证初始状态为Flat预设 ===";
    DataModel::PresetType initialType = dataModel->getCurvePresetType(0);
    qDebug() << "通道0初始预设类型:" << dataModel->presetTypeToString(initialType);

    // 示例1：设置通道0为SuperBass Level5预设
    qDebug() << "\n=== 示例1：设置通道0为SuperBass Level5预设 ===";
    dataModel->setCurvePreset(0, DataModel::PRESET_TYPE_SUPERBASS, DataModel::PRESET_LEVEL_5);

    // 检查预设是否设置成功
    if (dataModel->isCurvePresetEnabled(0)) {
        DataModel::PresetType type = dataModel->getCurvePresetType(0);
        DataModel::PresetLevel level = dataModel->getCurvePresetLevel(0);
        qDebug() << "通道0预设设置成功:" << dataModel->presetTypeToString(type)
                 << dataModel->presetLevelToString(level);

        // 验证32~36号不可见点的值是否正确设置
        for (int i = 32; i <= 36; ++i) {
            double frequency, qValue, gain;
            DataModel::EQType eqType;
            dataModel->getInvisiblePointParams(0, i, frequency, qValue, gain, eqType);
            qDebug() << QString("  点%1: 频率=%2Hz, Q=%3, 增益=%4dB, 类型=0x%5")
                        .arg(i).arg(frequency).arg(qValue).arg(gain)
                        .arg(QString::number(static_cast<int>(eqType), 16).toUpper());
        }
    }

    // 示例2：设置通道1为Vocal Level3预设
    qDebug() << "\n=== 示例2：设置通道1为Vocal Level3预设 ===";
    dataModel->setCurvePreset(1, DataModel::PRESET_TYPE_VOCAL, DataModel::PRESET_LEVEL_3);

    // 示例3：设置通道2为Powerful Level4预设
    qDebug() << "\n=== 示例3：设置通道2为Powerful Level4预设 ===";
    dataModel->setCurvePreset(2, DataModel::PRESET_TYPE_POWERFUL, DataModel::PRESET_LEVEL_4);

    // 示例4：查询所有通道的预设状态
    qDebug() << "\n=== 示例4：查询所有通道的预设状态 ===";
    for (int ch = 0; ch < 5; ++ch) {
        if (dataModel->isCurvePresetEnabled(ch)) {
            DataModel::PresetType type = dataModel->getCurvePresetType(ch);
            DataModel::PresetLevel level = dataModel->getCurvePresetLevel(ch);
            qDebug() << QString("通道%1: %2 %3 (已启用)")
                        .arg(ch)
                        .arg(dataModel->presetTypeToString(type))
                        .arg(dataModel->presetLevelToString(level));
        } else {
            qDebug() << QString("通道%1: 无预设 (已禁用)").arg(ch);
        }
    }

    // 示例5：清除通道0的预设（设置为Flat）
    qDebug() << "\n=== 示例5：清除通道0的预设（设置为Flat） ===";
    dataModel->clearCurvePreset(0);

    // 验证清除后的状态
    if (dataModel->isCurvePresetEnabled(0)) {
        DataModel::PresetType clearedType = dataModel->getCurvePresetType(0);
        qDebug() << "通道0预设已设置为:" << dataModel->presetTypeToString(clearedType);
        qDebug() << "预设启用状态:" << dataModel->isCurvePresetEnabled(0);
        qDebug() << "预设激活状态（非Flat）:" << dataModel->isCurvePresetActive(0);

        // 验证32~36号不可见点是否设置为Flat预设值
        for (int i = 32; i <= 36; ++i) {
            double frequency, qValue, gain;
            DataModel::EQType eqType;
            dataModel->getInvisiblePointParams(0, i, frequency, qValue, gain, eqType);
            qDebug() << QString("  点%1: 频率=%2Hz, Q=%3, 增益=%4dB (Flat预设)")
                        .arg(i).arg(frequency).arg(qValue).arg(gain);
        }
    }

    // 示例6：重复设置相同预设（不应该重复更新）
    qDebug() << "\n=== 示例6：重复设置相同预设（不应该重复更新）===";
    dataModel->setCurvePreset(1, DataModel::PRESET_TYPE_VOCAL, DataModel::PRESET_LEVEL_3);
    qDebug() << "重复设置通道1为Vocal Level3预设，应该显示'无需更新'";

    // 示例7：设置不同等级的预设（应该更新）
    qDebug() << "\n=== 示例7：设置不同等级的预设（应该更新）===";
    dataModel->setCurvePreset(1, DataModel::PRESET_TYPE_VOCAL, DataModel::PRESET_LEVEL_5);
    qDebug() << "将通道1从Vocal Level3更改为Vocal Level5，应该更新32~36号点";

    // 验证更新后的值
    for (int i = 32; i <= 36; ++i) {
        double frequency, qValue, gain;
        DataModel::EQType eqType;
        dataModel->getInvisiblePointParams(1, i, frequency, qValue, gain, eqType);
        qDebug() << QString("  点%1: 频率=%2Hz, 增益=%3dB")
                    .arg(i).arg(frequency).arg(gain);
    }

    // 示例8：连接预设变化信号
    qDebug() << "\n=== 示例8：连接预设变化信号 ===";
    QObject::connect(dataModel, &DataModel::curvePresetChanged,
        [](int curveIndex, DataModel::PresetType type, DataModel::PresetLevel level) {
            qDebug() << QString("信号：通道%1预设变更为 %2 %3")
                        .arg(curveIndex)
                        .arg(type == DataModel::PRESET_TYPE_FLAT ? "Flat" :
                             type == DataModel::PRESET_TYPE_SUPERBASS ? "SuperBass" :
                             type == DataModel::PRESET_TYPE_POWERFUL ? "Powerful" :
                             type == DataModel::PRESET_TYPE_VOCAL ? "Vocal" : "Natural")
                        .arg(level == DataModel::PRESET_LEVEL_1 ? "Level1" :
                             level == DataModel::PRESET_LEVEL_2 ? "Level2" :
                             level == DataModel::PRESET_LEVEL_3 ? "Level3" :
                             level == DataModel::PRESET_LEVEL_4 ? "Level4" : "Level5");
        });

    // 注意：clearCurvePreset现在会发送curvePresetChanged信号（设置为Flat）
    // 而不是curvePresetCleared信号

    // 测试信号发送
    dataModel->setCurvePreset(3, DataModel::PRESET_TYPE_NATURAL, DataModel::PRESET_LEVEL_2);
    dataModel->clearCurvePreset(3);

    // 清理
    delete dataModel;

    qDebug() << "\n===== DataModel 预设接口使用示例完成 =====\n";
}

/**
 * @brief 对比PresetManager和DataModel预设接口的区别
 */
void comparePresetManagerAndDataModelApproaches()
{
    qDebug() << "\n===== PresetManager vs DataModel 预设接口对比 =====";

    qDebug() << "\n--- PresetManager 方式 (独立管理) ---";
    qDebug() << "优点:";
    qDebug() << "  1. 功能完整，支持字符串查询和类型安全的枚举";
    qDebug() << "  2. 可以独立使用，不依赖DataModel";
    qDebug() << "  3. 支持从文件加载预设数据";
    qDebug() << "  4. 提供详细的数据转换和验证";
    qDebug() << "缺点:";
    qDebug() << "  1. 需要手动调用applyPreset来应用到DataModel";
    qDebug() << "  2. 多了一层抽象，增加了复杂性";

    qDebug() << "\n--- DataModel 内置预设方式 (集成管理) ---";
    qDebug() << "优点:";
    qDebug() << "  1. 直接集成在DataModel中，调用简单";
    qDebug() << "  2. 自动管理32~36号不可见点的更新";
    qDebug() << "  3. 减少了外部依赖和数据同步问题";
    qDebug() << "  4. 预设变化自动触发曲线重新生成";
    qDebug() << "缺点:";
    qDebug() << "  1. 预设数据硬编码在代码中";
    qDebug() << "  2. 功能相对简单，不支持动态加载";

    qDebug() << "\n--- 建议使用场景 ---";
    qDebug() << "1. 如果需要复杂的预设管理和动态加载，使用PresetManager";
    qDebug() << "2. 如果只需要基本的预设功能且追求简单性，使用DataModel内置接口";
    qDebug() << "3. 可以同时使用两种方式，PresetManager用于高级功能，DataModel内置接口用于基本操作";

    qDebug() << "\n===== 对比分析完成 =====\n";
}