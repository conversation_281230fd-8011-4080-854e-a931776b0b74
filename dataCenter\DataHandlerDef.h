#ifndef DATAHANDLERDEF_H
#define DATAHANDLERDEF_H

#include <stdint.h>
#include <vector>

static const uint8_t caCrc8Data[256] = {
    0x00, 0x5e, 0xbc, 0xe2, 0x61, 0x3f, 0xdd, 0x83,
    0xc2, 0x9c, 0x7e, 0x20, 0xa3, 0xfd, 0x1f, 0x41,
    0x9d, 0xc3, 0x21, 0x7f, 0xfc, 0xa2, 0x40, 0x1e,
    0x5f, 0x01, 0xe3, 0xbd, 0x3e, 0x60, 0x82, 0xdc,
    0x23, 0x7d, 0x9f, 0xc1, 0x42, 0x1c, 0xfe, 0xa0,
    0xe1, 0xbf, 0x5d, 0x03, 0x80, 0xde, 0x3c, 0x62,
    0xbe, 0xe0, 0x02, 0x5c, 0xdf, 0x81, 0x63, 0x3d,
    0x7c, 0x22, 0xc0, 0x9e, 0x1d, 0x43, 0xa1, 0xff,
    0x46, 0x18, 0xfa, 0xa4, 0x27, 0x79, 0x9b, 0xc5,
    0x84, 0xda, 0x38, 0x66, 0xe5, 0xbb, 0x59, 0x07,
    0xdb, 0x85, 0x67, 0x39, 0xba, 0xe4, 0x06, 0x58,
    0x19, 0x47, 0xa5, 0xfb, 0x78, 0x26, 0xc4, 0x9a,
    0x65, 0x3b, 0xd9, 0x87, 0x04, 0x5a, 0xb8, 0xe6,
    0xa7, 0xf9, 0x1b, 0x45, 0xc6, 0x98, 0x7a, 0x24,
    0xf8, 0xa6, 0x44, 0x1a, 0x99, 0xc7, 0x25, 0x7b,
    0x3a, 0x64, 0x86, 0xd8, 0x5b, 0x05, 0xe7, 0xb9,
    0x8c, 0xd2, 0x30, 0x6e, 0xed, 0xb3, 0x51, 0x0f,
    0x4e, 0x10, 0xf2, 0xac, 0x2f, 0x71, 0x93, 0xcd,
    0x11, 0x4f, 0xad, 0xf3, 0x70, 0x2e, 0xcc, 0x92,
    0xd3, 0x8d, 0x6f, 0x31, 0xb2, 0xec, 0x0e, 0x50,
    0xaf, 0xf1, 0x13, 0x4d, 0xce, 0x90, 0x72, 0x2c,
    0x6d, 0x33, 0xd1, 0x8f, 0x0c, 0x52, 0xb0, 0xee,
    0x32, 0x6c, 0x8e, 0xd0, 0x53, 0x0d, 0xef, 0xb1,
    0xf0, 0xae, 0x4c, 0x12, 0x91, 0xcf, 0x2d, 0x73,
    0xca, 0x94, 0x76, 0x28, 0xab, 0xf5, 0x17, 0x49,
    0x08, 0x56, 0xb4, 0xea, 0x69, 0x37, 0xd5, 0x8b,
    0x57, 0x09, 0xeb, 0xb5, 0x36, 0x68, 0x8a, 0xd4,
    0x95, 0xcb, 0x29, 0x77, 0xf4, 0xaa, 0x48, 0x16,
    0xe9, 0xb7, 0x55, 0x0b, 0x88, 0xd6, 0x34, 0x6a,
    0x2b, 0x75, 0x97, 0xc9, 0x4a, 0x14, 0xf6, 0xa8,
    0x74, 0x2a, 0xc8, 0x96, 0x15, 0x4b, 0xa9, 0xf7,
    0xb6, 0xe8, 0x0a, 0x54, 0xd7, 0x89, 0x6b, 0x35
};

constexpr uint8_t FrameHeadER_H = 0xAA;             // 帧头高字节
constexpr uint8_t FrameHeadER_L = 0x55;             // 帧头低字节
constexpr uint16_t FrameHeadER = 0xAA55;            // 完整帧头
constexpr uint8_t CURRENT_VERSION = 0x00;           // 版本号字节

constexpr uint8_t OUTPUT_CHANNEL_MAX = 0X0A;        // 输出通道高配最大个数
constexpr uint8_t OUTPUT_CHANNEL_L_MAX = 0X06;      // 输出通道低配最大个数
constexpr uint8_t MIX_HIGH_CHANNEL_MAX = 0X08;      // 混音HIGH高配最大input channel数
constexpr uint8_t MIX_HIGH_CHANNEL_L_MAX = 0X04;    // 混音HIGH低配最大input channel数
constexpr uint8_t MIX_RCA_CHANNEL_MAX = 0X04;       // 混音RCA最大input channel数
constexpr uint8_t MIX_DSP_CHANNEL_MAX = 0X02;       // 混音DSP最大input channel数
constexpr uint8_t EQ_BAND_MAX = 0X1F;               // EQ band最大个数
constexpr uint8_t MEMORY_TYPE_MAX = 0X02;           // memory type最大个数
constexpr uint8_t MEMORY_MAX = 0X06;                // single source memory最大个数
constexpr uint8_t MU_MEMORY_MAX = MEMORY_MAX;       // main unit memory最大个数
constexpr uint8_t DSP_MEMORY_MAX = MEMORY_MAX;      // dsp memory最大个数
constexpr uint8_t REMOTE_SHORTCUT_MAX = 0X0A;       // 遥控器快捷键最大个数

// 错误码定义 (用于optcode的errcode字段)
enum class ErrorCode : uint8_t {
    SUCCESS = 0x00,             // 成功
    UNKNOWN_ERROR = 0x01,       // 未知定义的错误
    DEVICE_NOT_READY = 0x02,    // 设备已占用（设备已被其他客户端连接时，拒绝进一步请求）
    PROTOCOL_MISMATCH = 0x03,   // 协议版本不匹配
    FORMAT_ERROR = 0x04,        // 校验错误或帧格式错误
    UNSUPPORTED = 0x05,        // 不支持的功能
    PARAM_INVALID = 0x06,       // 参数格式不正确（比如数量、顺序等）
    VALUE_INVALID = 0x07,       // 参数范围不正确（超过参数允许的范围）
    DEVICE_OFFLINE = 0x08,      // 设备未连接
    HARDWARE_ERROR = 0x09,      // 硬件访问错误
    RESOURCE_ERROR = 0x0A,      // 从设备获取错误
    PARAM_LIMIT = 0x0B,         // 比如算力不足不支持当前情况下的参数调节
};

// 音源
enum class MusicSourceEnum : uint8_t {
    CLOSE = 0x00,       // 关闭
    HIGH = 0x01,        // 高电平
    RCA = 0x02,         // 低电平
    AUX = 0x03,         // AUX
    BT = 0x04,          // BT
    SPDIF = 0x05,       // 光纤
    USB_AUDIO = 0x06,   // usb-audio
};

// 高低通斜率
enum class PassSlopeEnum : uint8_t {
    CLOSE = 0x00,       // 关闭
    DB_OCT_6 = 0x01,    // 6dB/Oct
    DB_OCT_12 = 0x02,   // 12dB/Oct
    DB_OCT_18 = 0x03,   // 18dB/Oct
    DB_OCT_24 = 0x04,   // 24dB/Oct
    DB_OCT_30 = 0x05,   // 30dB/Oct
    DB_OCT_36 = 0x06,   // 36dB/Oct
    DB_OCT_42 = 0x07,   // 42dB/Oct
    DB_OCT_48 = 0x08    // 48dB/Oct
};

// 高低通斜率
enum class PassTypeEnum : uint8_t {
    BUTTERWORTH = 0x00,     // 巴特沃兹
    BEZIER = 0x01,          // 贝塞尔
    LINKWITZ_RILEY = 0x02   // 宁克锐利
};

// EQ-type
enum class EqTypeEnum : uint8_t {
    PEAK = 0x00,
    LP1 = 0x01,
    HP1 = 0x02,
    LS1 = 0x03,
    HS1 = 0x04,
    LP2 = 0x05,
    HP2 = 0x06,
    LS2 = 0x07,
    HS2 = 0x08,
    NOTCH = 0x09,
    AP1 = 0x0A,
    AP2 = 0x0B,
    BP2 = 0x0C
};

// SpeakerIdx
enum class SpeakerIdxEnum : uint8_t
{
    SPIDX_DEFAULT,
    FL,
    FR,
    RL,
    RR,
    CENTER,
    L,
    R
};

// SpeakerType
enum class SpeakerTypeEnum : uint8_t
{
    SPTYPE_DEFAULT,
    TWEETER,
    MIDRANGE,
    WOOFER,
    M_T,
    M_WF,
    FULL,
    SUBWOOFER,
    USER_CONFIG_1,
    USER_CONFIG_2
};

// Speaker
enum class SpeakerEnum : uint16_t
{
    SP_DEFAULT,

    FL_TWEETER  = 0x0101,
    FL_MIDRANGE = 0x0201,
    FL_WOOFER   = 0x0301,
    FL_M_T      = 0x0401,
    FL_M_WF     = 0x0501,
    FL_FULL     = 0x0601,

    FR_TWEETER  = 0x0102,
    FR_MIDRANGE = 0x0202,
    FR_WOOFER   = 0x0302,
    FR_M_T      = 0x0402,
    FR_M_WF     = 0x0502,
    FR_FULL     = 0x0602,

    RL_TWEETER  = 0x0103,
    RL_MIDRANGE = 0x0203,
    RL_WOOFER   = 0x0303,
    RL_M_WF     = 0x0503,
    RL_FULL     = 0x0603,

    RR_TWEETER  = 0x0104,
    RR_MIDRANGE = 0x0204,
    RR_WOOFER   = 0x0304,
    RR_M_WF     = 0x0504,
    RR_FULL     = 0x0604,

    C_TWEETER   = 0x0105,
    C_M_WF      = 0x0305,
    C_FULL      = 0x0605,

    L_SUBWOOFER = 0x0706,
    R_SUBWOOFER = 0x0707,
    SUBWOOFER   = 0x0700,

    USER_CONFIG_1   = 0x0800,
    USER_CONFIG_2   = 0x0900
};

// EQ-setting
enum class EqSettingEnum : uint8_t {
    RESET = 0x00,
    RECOVER = 0x01,
    BYPASS = 0x02,
    CLEAR = 0x03
};

// mix类型
enum class MixTypeEnum : uint8_t {
    HIGH = 0x00,    // high
    RCA = 0x01,     // rca
    DSP = 0x02      // dsp
};

// memory类型
enum class MemoryTypeEnum : uint8_t {
    MAIN_UNIT = 0x00,   // main unit
    DSP = 0x01          // dsp
};

// optcode位域定义
#pragma pack(push, 1)
struct OptCodeBits {
    uint8_t errcode : 4;    // bit 0-3: 错误码
    uint8_t reserve : 2;    // bit 4-5: 保留
    uint8_t ack : 1;        // bit 6: 应答标志 0-主动上报/请求，1-应答帧
    uint8_t syn : 1;        // bit 7: 同步标志 0-无需应答，1-需要应答
};
#pragma pack(pop)

// 操作码定义
union OptCode {
    uint8_t value;          // 完整的操作码值
    OptCodeBits bits;       // 按位访问结构
};

#pragma pack(push, 1)
struct FrameHead {
    uint8_t headerH;
    uint8_t headerL;
    uint8_t version;
    OptCode optcode;
    uint16_t dataLen;
    uint8_t mid;
    uint8_t sid;
} ;
#pragma pack(pop)

// 通信帧结构
#pragma pack(push, 1)
struct ProtocolFrame {
    FrameHead header;           // 帧头
    std::vector<uint8_t> data;  // 数据内容
    uint8_t CRC8;               // CRC8校验
};
#pragma pack(pop)

// 设备连接相关功能定义
struct DeviceOperation {
    static constexpr uint8_t MID = 0x01;

    enum class SID : uint8_t {
        CONNECT_REQUEST = 0x01,         // 连接请求
        DISCONNECT_REQUEST = 0x02,      // 断开请求
        HEARTBEAT = 0x03,               // 心跳
        REGISTER = 0x04,                // 注册
        QUERY_REGISTER = 0x05,          // 查询注册文件
        CONTROLLER_CONNECT = 0x06,      // 线控器连接
        CONTROLLER_STATUS_QUERY = 0x07, // 查询设备状态
        SOURCE_ENABLE_SETTING = 0x08,   // 音源用户自定义设定
        SOURCE_ENABLE_QUERY = 0x09,     // 音源用户设定查询
        DEVICE_SETTING = 0x0A,          // 设备级联设置
        DEVICE_QUERY = 0x0B             // 设备级联查询
    };
};

#pragma pack(push, 1)
struct DeviceOperation01 {
    uint8_t token2[32];
    uint8_t deviceID[16];
    uint8_t deviceType[32];
    uint8_t mainVersion;
    uint8_t subVersion;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct DeviceOperation03 {
    uint16_t mainGain;
    uint8_t mainMute;
    uint8_t usbConnected;
    uint8_t musicSource;
    uint8_t btConnected;
    uint8_t aptxConnected;
    uint8_t uacConnected;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct DeviceOperation0809 {
    uint8_t mainUnit;
    uint8_t aux;
    uint8_t bt;
    uint8_t spdif;
    uint8_t usbAudio;
};
#pragma pack(pop)

// 通道配置相关功能定义
struct ChannelOperation {
    static constexpr uint8_t MID = 0x02;

    enum class SID : uint8_t {
        OUTPUT_CHANNEL_SET = 0x01,     // 输出通道设置
        OUTPUT_CHANNEL_QUERY = 0x02,   // 输出通道查询
        INPUT_CHANNEL_SET = 0x03,      // 输入通道设置
        INPUT_CHANNEL_QUERY = 0x04,    // 输入通道查询
        CHANNEL_ROUTE_SET = 0x05,      // 通道路由设置
        CHANNEL_XOVER_SET = 0x06,      // 通道分频器设置
        CHANNEL_CROSSOVER_SET = 0x07,  // 通道路由设置
        CHANNEL_CROSSOVER_QUERY = 0x08 // 通道分频器设置
    };
};

#pragma pack(push, 1)
struct OutputChannelTypeBits {
    uint16_t skpType : 8;
    uint16_t spkIdx : 8;
};
#pragma pack(pop)

union OutputChannelType {
    uint16_t value;
    OutputChannelTypeBits bits;
};

#pragma pack(push, 1)
struct ChannelOperation0102 {
    uint8_t channel;
    OutputChannelType type;
    uint16_t gain;
    uint8_t positiveNegative;
    uint8_t mute;
    uint16_t delay;
    uint8_t delayGroup;
    uint8_t eqSet;
    uint8_t eqType;
    uint8_t linkType;
    uint8_t linkChannel;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct ChannelOperation0708 {
    uint8_t channel;
    uint16_t hpFreq;
    uint8_t hpSlope;
    uint8_t hpType;
    uint8_t hpEnable;
    uint16_t lpFreq;
    uint8_t lpSlope;
    uint8_t lpType;
    uint8_t lpEnable;
};
#pragma pack(pop)

// 音频设置相关功能定义
struct AudioOperation {
    static constexpr uint8_t MID = 0x03;

    enum class SID : uint8_t {
        MAIN_VOLUME_SET = 0x01,         // 主音量设置
        MAIN_VOLUME_QUERY = 0x02,       // 主音量查询
        MUSIC_SOURCE_SET = 0x03,        // 音源设置
        MUSIC_SOURCE_QUERY = 0x04,      // 音源查询
        CHANNEL_MIX_SET = 0x05,         // 混音设置
        CHANNEL_MIX_QUERY = 0x06,       // 混音查询
        EQ_SET = 0x07,                  // EQ设置
        EQ_QUERY = 0x08,                // EQ查询
        EQ_LINK_SET = 0x09,             // EQ link设置
        MEMORY_SET = 0x0A,              // memory设置
        MEMORY_QUERY = 0x0B,            // memory设置查询
        MEMORY_COPY = 0x0C,             // memory复制
        DELAY_SET = 0x0D,               // 延时组设置
        PRESET_SET = 0x0E,              // preset设置
        PRESET_QUERY = 0x0F,            // preset查询
        DISCONNECT_BT = 0x10            // 断开经典蓝牙
    };
};

#pragma pack(push, 1)
struct AudioOperation0102 {
    uint16_t mainGain;
    uint8_t mainMute;
    uint8_t dspGain;
    uint8_t dspMute;
    uint8_t bassLevel;
    uint8_t bassMute;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0304 {
    uint8_t source;
    uint8_t memory;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct MixBits {
    uint8_t gain : 7;
    uint8_t enable : 1;
};
#pragma pack(pop)

union Mix {
    uint8_t value;
    MixBits bits;
};

#pragma pack(push, 1)
struct AudioOperation0506 {
    uint8_t source;
    uint8_t startChannel;
    uint8_t num;
    std::vector<Mix> gains;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct EqTypeBits {
    uint8_t type : 6;
    uint8_t enable : 2;
};
#pragma pack(pop)

union EqType {
    uint8_t value;
    EqTypeBits bits;
};

#pragma pack(push, 1)
struct EqParm {
    uint16_t freq;
    uint16_t qValue;
    uint16_t gain;
    EqType type;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0708 {
    uint8_t channel;
    uint8_t startBand;
    uint8_t num;
    std::vector<EqParm> parms;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct EqLinkParm {
    uint8_t channel;
    uint8_t band;
    uint16_t freq;
    uint16_t qValue;
    uint16_t gain;
    EqType type;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation09 {
    uint8_t num;
    std::vector<EqLinkParm> links;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct MemorySetting {
    uint8_t enable;
    uint8_t name[32];
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0A0B {
    uint8_t startMemory;
    uint8_t type;
    uint8_t num;
    std::vector<MemorySetting> memories;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0C {
    uint8_t type;
    uint8_t memorySrc;
    uint8_t memoryDes;
    uint8_t name[32];
};
#pragma pack(pop)

#pragma pack(push, 1)
struct DelaySetting {
    uint8_t channel;
    uint16_t delayTime;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0D {
    uint8_t num;
    std::vector<DelaySetting> delaySettings;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct AudioOperation0E0F {
    uint8_t mode;
    uint8_t level;
};
#pragma pack(pop)

// 系统设置相关功能定义
struct SystemOperation {
    static constexpr uint8_t MID = 0x04;

    enum class SID : uint8_t {
        MACHINE_CONFIG_SET = 0x01,     // 机器预设置设置
        MACHINE_CONFIG_QUERY = 0x02,   // 机器预设置查询
        SYSTEM_CONFIG1_SET = 0x03,     // 系统配置1设置
        SYSTEM_CONFIG1_QUERY = 0x04,   // 系统配置1查询
        SYSTEM_CONFIG2_SET = 0x05,     // 系统配置2设置
        SYSTEM_CONFIG2_QUERY = 0x06,   // 系统配置2查询
        INPUT_CONFIG_SET = 0x07,       // 输入音量设置
        INPUT_CONFIG_QUERY = 0x08,     // 输入音量查询
        RESTORE_FACTORY = 0x09,        // 恢复出厂
        SAVE_ELECTRONIC = 0x0A,        // 保存电子配置
        LOAD_ELECTRONIC = 0x0B,        // 加载电子配置
        SAVE_MACHINE = 0x0C,           // 保存机器配置
        LOAD_MACHINE = 0x0D,           // 加载机器配置
        REFERENCE_LOAD = 0x0E,         // 方控学习
        TAKE_UI_PATCH = 0x0F,          // 获取UI图片
        WIFI_SET = 0x10,               // wifi设置
        WIFI_QUERY = 0x11,             // wifi查询
        KNOWLEDGE_SET = 0x12,          // 加密设置
        KNOWLEDGE_QUERY = 0x13,        // 加密查询
        RECORD_TIME = 0x14,            // 设置时间
        INPUT_RECORD_SET = 0x15,       // 输入录音度设置
        INPUT_RECORD_QUERY = 0x16      // 输入录音度查询
    };
};

#pragma pack(push, 1)
struct SystemOperation0304 {
    uint8_t naviMixEnable;
    uint8_t naviSensitivity;
    uint8_t naviAttenuation;
    uint8_t naviDuration;
    uint8_t externalAttenuation;
    uint8_t externalPolarity;
    uint8_t externalDspAttenuation;
    uint8_t delayUnit;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct SystemOperation0708 {
    uint8_t highGain;
    uint8_t rcaGain;
    uint8_t auxGain;
    uint8_t btGain;
    uint8_t spdifGain;
    uint8_t usbGain;
};
#pragma pack(pop)

// OTA升级相关功能定义
struct OTAOperation {
    static constexpr uint8_t MID = 0x05;

    enum class SID : uint8_t {
        SYSTEM_VERSION_QUERY = 0x01,    // 系统版本查询
        UPGRADE_DATA = 0x02,            // 升级-传输数据
        UPGRADE_RESULT = 0x03           // 升级-结果查询
    };
};

#pragma pack(push, 1)
struct VersionInfo {
    uint8_t name[4];
    uint8_t mainVersion;
    int32_t subVersion;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct OTAOperation01 {
    uint8_t num;
    std::vector<VersionInfo> info;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct OTAOperation02Index0Send {
    uint8_t index;
    uint8_t name[4];
    uint8_t flag;
    int32_t totalSize;
    uint8_t optionLen;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct OTAOperation02Received {
    int32_t index;
    uint8_t flag;
    uint8_t mode;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct OTAOperation03Received {
    uint8_t flag;
    uint8_t progress;
};
#pragma pack(pop)

// 媒体播放相关功能定义
struct RemoteOperation {
    static constexpr uint8_t MID = 0x06;

    enum class SID : uint8_t {
        BRIGHTNESS_SET = 0x01,          // 亮度设置
        BRIGHTNESS_QUERY = 0x02,        // 亮度查询
        MODEL_SET = 0x03,               // 模式设置
        MODEL_QUERY = 0x04,             // 模式查询
        SHORTCUT_SET = 0x05,            // 快捷键设置
        SHORTCUT_QUERY = 0x06,          // 快捷键查询
    };
};

#pragma pack(push, 1)
struct RemoteOperation0102 {
    uint8_t brightness;
    uint8_t dimmer;
    uint8_t polarity;
    uint8_t dimmerBrightness;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct RemoteOperation0304 {
    uint8_t model;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct RemoteShortCutInfo {
    uint8_t id;
    uint8_t type;
    uint8_t memory;
};
#pragma pack(pop)

#pragma pack(push, 1)
struct RemoteOperation0506 {
    uint8_t num;
    std::vector<RemoteShortCutInfo> infos;
};
#pragma pack(pop)

#endif // DATAHANDLERDEF_H
