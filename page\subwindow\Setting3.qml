import QtQuick
import "../framework"

Item {
    property int uiRemoteMode: remoteMode

    property int remoteMode: dataMap["remoteModel"]

    onUiRemoteModeChanged: {
        if(uiRemoteMode !== remoteMode)
        {
            commonCtrl.setRemoteModel(uiRemoteMode)
        }
    }

    onRemoteModeChanged: {
        if(uiRemoteMode !== remoteMode)
        {
            uiRemoteMode = remoteMode
        }
    }

    Rectangle {
        id: remoteModelFrame
        anchors.left: parent.left
        anchors.leftMargin: 24
        anchors.top: parent.top
        anchors.topMargin: 24
        width: 168
        height: 92
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: remoteModeTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: remoteModeTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Remote Mode")
            }
        }

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 16

            MyRadioButton {
                text: qsTr("Default")
                checked: (0 === uiRemoteMode)

                onClicked: uiRemoteMode = 0
            }

            MyRadioButton {
                text: qsTr("Short Cut mode")
                checked: (1 === uiRemoteMode)

                onClicked: uiRemoteMode = 1
            }
        }
    }

    Rectangle {
        id: shortCutSettingFrame
        anchors.left: remoteModelFrame.right
        anchors.leftMargin: 24
        anchors.top: remoteModelFrame.top
        width: 864
        height: 228
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: shortCutSettingTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: shortCutSettingTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Short Cut Setting")
            }
        }

        Row {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 38

            Repeater {
                model: 2

                Row {
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 28
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "No."
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 106
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Music Source")
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 248
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Memory")
                    }
                }
            }
        }

        Grid {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 48
            rows: 5
            rowSpacing: 8
            columns: 2
            columnSpacing: 38
            flow: Grid.TopToBottom

            Repeater {
                model: 10
                RemoteShortCutItem {
                    shortCutId: modelData
                }
            }
        }
    }

    Rectangle {
        id: memoryInfoFrame
        anchors.left: parent.left
        anchors.leftMargin: 24
        anchors.top: shortCutSettingFrame.bottom
        anchors.topMargin: 16
        width: 1056
        height: 222
        color: "transparent"
        border.width: 1
        border.color: "#747880"

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 8
            anchors.top: parent.top
            anchors.topMargin: -4
            width: memoryInfoTitle.contentWidth + 8 + 8
            height: 8
            color: "#3C4048"

            Text {
                id: memoryInfoTitle
                anchors.centerIn: parent
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
                text: qsTr("Memory Status")
            }
        }

        Text {
            id: mainUnitTitle
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            width: 502
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("Main Unit Memory")
        }

        Rectangle {
            id: separator
            anchors.left: mainUnitTitle.right
            anchors.top: mainUnitTitle.top
            width: 1
            height: 164
            color: "#747880"
        }

        Text {
            id: dspTitle
            anchors.left: separator.right
            anchors.leftMargin: 24
            anchors.top: mainUnitTitle.top
            width: 502
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("DSP Source Memory")
        }

        Row {
            id: subTitles
            anchors.left: mainUnitTitle.left
            anchors.top: mainUnitTitle.bottom
            anchors.topMargin: 8
            spacing: 25

            Repeater {
                model: 2

                Row {
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 38
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: "No."
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 84
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Valid / Invalid")
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 380
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        text: qsTr("Comment")
                    }
                }
            }
        }

        Grid {
            anchors.left: subTitles.left
            anchors.top: subTitles.bottom
            anchors.topMargin: 12
            rows: 6
            rowSpacing: 16
            columns: 2
            columnSpacing: 25
            flow: Grid.TopToBottom

            Repeater {
                model: 12

                Item {
                    width: 502
                    height: 8
                    Row {
                        Text {
                            anchors.verticalCenter: parent.verticalCenter
                            width: 38
                            height: 8
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: (6 > modelData) ? ("M" + (modelData + 1)) : ("S" + (modelData - 5))
                        }

                        Text {
                            anchors.verticalCenter: parent.verticalCenter
                            width: 84
                            height: 8
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: (0 === memoryEnable) ? qsTr("Invalid") : qsTr("Valid")

                            property int memoryEnable: dataMap["memorySettingType" + ((6 > modelData) ? 0 : 1) + "Index" + ((6 > modelData) ? modelData : (modelData - 6))]["enable"]
                        }

                        Text {
                            anchors.verticalCenter: parent.verticalCenter
                            width: 380
                            height: 8
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: dataMap["memorySettingType" + ((6 > modelData) ? 0 : 1) + "Index" + ((6 > modelData) ? modelData : (modelData - 6))]["name"]
                        }
                    }
                }
            }
        }
    }
}
