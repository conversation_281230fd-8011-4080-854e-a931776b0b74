#include "USBManager.h"

USBManager::USBManager(QObject *parent)
    : QObject{parent}
{
    mHidSenderThread = new QThread();
    mHidSender = new HidSender();
    mHidSender->moveToThread(mHidSenderThread);
    connect(mHidSenderThread, SIGNAL(finished()), mHidSenderThread, SLOT(deleteLater()));
    connect(mHidSenderThread, SIGNAL(finished()), mHidSender, SLOT(deleteLater()));
    connect(this, &USBManager::sendMessageSig, mHidSender, &HidSender::onSendMessage, Qt::QueuedConnection);
    connect(mHidSender, &HidSender::receivedMessageSig, this, &USBManager::receiveMessageSig, Qt::QueuedConnection);
    connect(mHidSender, &HidSender::receivedErrorSig, this, &USBManager::receivedErrorSig, Qt::QueuedConnection);
    mHidSenderThread->start();
}

USBManager::~USBManager()
{
    disconnectDevice();
}

int USBManager::connectDevice()
{
    disconnectDevice();

    mHidHandle = hid_open(HID_VID, HID_H_PID, NULL);

    if (mHidHandle == nullptr)
    {
        mHidHandle = hid_open(HID_VID, HID_L_PID, NULL);
    }

    if (mHidHandle == nullptr)
    {
        const wchar_t* errorStr = hid_error(nullptr);
        qDebug("连接异常：%ls", errorStr);
        return -1;
    }

    hid_set_nonblocking(mHidHandle, 0);

    mHidSender->setHidHandle(mHidHandle);

    return 0;
}

void USBManager::disconnectDevice()
{
    if (mHidHandle != nullptr) {
        const uint8_t disData[] = {(uint8_t)0x00,(uint8_t)0x0a,(uint8_t)0xaa,(uint8_t)0x55,(uint8_t)0x00,(uint8_t)0x80,
                                   (uint8_t)0x01,(uint8_t)0x00,(uint8_t)0x01,(uint8_t)0x02,(uint8_t)0x00,(uint8_t)0x9a};
        hid_write(mHidHandle, disData, 12);

        hid_close(mHidHandle);
        mHidHandle = nullptr;
        hid_exit();
    }
}

int USBManager::sendMessage(const QByteArray &data)
{
    emit sendMessageSig(data);
    return 0;
}

int USBManager::receiveMessage(const QByteArray &data)
{
    emit receiveMessageSig(data);
    return 0;
}
