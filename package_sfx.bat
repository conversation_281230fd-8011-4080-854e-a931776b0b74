@echo off
setlocal

:: 检查7-Zip是否安装
if not exist "C:\Program Files\7-Zip\7z.exe" (
    echo Error: 7-Zip is not installed. Please install it first.
    echo Download from: https://7-zip.org/
    exit /b 1
)

:: 先运行普通打包
call package.bat
if errorlevel 1 (
    echo Error: Package creation failed.
    exit /b 1
)

:: 设置变量
set RELEASE_DIR=release
set APP_NAME=LineChartEditor
set SFX_CONFIG=sfx_config.txt

:: 创建发布目录
if exist %RELEASE_DIR% rd /s /q %RELEASE_DIR%
mkdir %RELEASE_DIR%

:: 创建SFX配置文件
echo ;!@Install@!UTF-8! > %SFX_CONFIG%
echo Title="Line Chart Editor" >> %SFX_CONFIG%
echo BeginPrompt="是否要安装Line Chart Editor?" >> %SFX_CONFIG%
echo Progress="yes" >> %SFX_CONFIG%
echo Directory="%%TEMP%%\LineChartEditor" >> %SFX_CONFIG%
echo ExecuteFile="line_chart.exe" >> %SFX_CONFIG%
echo ;!@InstallEnd@! >> %SFX_CONFIG%

:: 创建7z压缩包
"C:\Program Files\7-Zip\7z.exe" a -r %RELEASE_DIR%\app.7z .\deploy\*

:: 合并SFX模块、配置和压缩包
copy /b "C:\Program Files\7-Zip\7z.sfx" + %SFX_CONFIG% + %RELEASE_DIR%\app.7z %RELEASE_DIR%\%APP_NAME%.exe

:: 清理临时文件
del %SFX_CONFIG%
del %RELEASE_DIR%\app.7z

echo.
echo Self-extracting executable created successfully: %RELEASE_DIR%\%APP_NAME%.exe
echo Users can run this single file to extract and run the application. 