import QtQuick
import QtQuick.Controls.Basic

Button {
    id: control
    implicitHeight: 24
    font.family: "Segoe UI"
    font.pixelSize: 12
    opacity: enabled ? 1 : 0.3

    contentItem: Text {
        color: "#E8E8E8"
        font: control.font
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        text: control.text
    }

    background: Rectangle {
        color: control.hovered ? "#5C6068" : control.pressed ? "#3C4048" : "#484C54"
        border.color: control.hovered ? "#7C8088" : "#646870"
        border.width: 1
    }
}
