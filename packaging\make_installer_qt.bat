@echo off
setlocal enabledelayedexpansion
echo [INFO] Building installer...

REM Set Qt environment variables
set "QTDIR=C:\Qt\6.8.3\mingw_64"
set "MINGW_DIR=C:\Qt\Tools\mingw1310_64"
set "QIF_DIR=C:\Qt\Tools\QtInstallerFramework\4.10"
set "PATH=%QTDIR%\bin;%MINGW_DIR%\bin;%QIF_DIR%\bin;%PATH%"

REM Try to auto-detect MinGW version and path
for /d %%i in (C:\Qt\Tools\mingw*) do (
    if exist "%%i\bin\mingw32-make.exe" (
        set "MINGW_DIR=%%i"
        echo [INFO] Found MinGW at: %%i
    )
)

REM Detect actual MinGW library path
set "MINGW_LIB_DIR=%MINGW_DIR%\bin"
if exist "%MINGW_DIR%\x86_64-w64-mingw32\lib" (
    set "MINGW_LIB_DIR=%MINGW_DIR%\x86_64-w64-mingw32\lib"
    echo [INFO] Using MinGW lib directory: %MINGW_LIB_DIR%
)

REM Generate version from git tags
REM First write git output to temp file to avoid PowerShell issues
git describe --tags --abbrev=0 > "%TEMP%\git_version.txt" 2>nul
if errorlevel 1 (
    REM No tags found, use default version
    set "VERSION=0.0.0"
    echo [INFO] No git tags found, using default version: 0.0.0
) else (
    REM Read version from temp file
    set /p VERSION=<"%TEMP%\git_version.txt"
    REM Remove v or V prefix if present
    if "!VERSION:~0,1!"=="v" set "VERSION=!VERSION:~1!"
    if "!VERSION:~0,1!"=="V" set "VERSION=!VERSION:~1!"
    echo [INFO] Using git tag version: !VERSION!
    REM Clean up temp file
    del "%TEMP%\git_version.txt" >nul 2>nul
)

REM Ensure VERSION is not empty
if "!VERSION!"=="" set "VERSION=0.0.0"
echo [INFO] Final version: !VERSION!

REM Check environment
if not exist "%QTDIR%\bin\qmake.exe" (
    echo [ERROR] Qt not found: %QTDIR%
    pause
    exit /b 1
)
if not exist "%MINGW_DIR%\bin\mingw32-make.exe" (
    echo [ERROR] MinGW not found: %MINGW_DIR%
    pause
    exit /b 1
)
if not exist "%QIF_DIR%\bin\binarycreator.exe" (
    echo [ERROR] Qt Installer Framework not found: %QIF_DIR%
    pause
    exit /b 1
)

REM Set project path
set "PRJ_PATH=%~dp0.."
cd /d "%PRJ_PATH%"
set "ABS_PRJ_PATH=%CD%"

REM Generate multilingual translation files
echo [INFO] Generating translation files...
cd /d "%ABS_PRJ_PATH%\packaging"
cmd /c "create_simple_translations.bat auto"
if errorlevel 1 (
    echo [WARNING] Translation file generation had issues, continuing with build...
)
cd /d "%ABS_PRJ_PATH%"

REM Use existing build-qt.bat for compilation
call build-qt.bat
if errorlevel 1 (
    echo [ERROR] build-qt.bat failed
    pause
    exit /b 1
)

REM Deploy Qt dependencies
call "%QTDIR%\bin\windeployqt.exe" --release --no-translations --no-system-d3d-compiler --no-opengl-sw --compiler-runtime --qmldir=. "build\bin\release\DSP Controller.exe"
if errorlevel 1 (
    echo [ERROR] windeployqt failed
    pause
    exit /b 1
)

REM Copy MinGW runtime DLLs
echo [INFO] Copying MinGW runtime libraries...
echo [INFO] MinGW directory: %MINGW_DIR%
echo [INFO] MinGW lib directory: %MINGW_LIB_DIR%

REM Copy basic runtime libraries from bin directory
copy /y "%MINGW_DIR%\bin\libgcc_s_seh-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
if errorlevel 1 echo [WARNING] Failed to copy libgcc_s_seh-1.dll from bin
copy /y "%MINGW_DIR%\bin\libstdc++-6.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
if errorlevel 1 echo [WARNING] Failed to copy libstdc++-6.dll from bin
copy /y "%MINGW_DIR%\bin\libwinpthread-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
if errorlevel 1 echo [WARNING] Failed to copy libwinpthread-1.dll from bin

REM Copy libssp-0.dll from lib directory if exists
copy /y "%MINGW_LIB_DIR%\libssp-0.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
if errorlevel 1 (
    echo [WARNING] Failed to copy libssp-0.dll from lib directory, trying bin...
    copy /y "%MINGW_DIR%\bin\libssp-0.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
    if errorlevel 1 echo [WARNING] Failed to copy libssp-0.dll from bin directory
)

REM Copy other possible MinGW runtime libraries
copy /y "%MINGW_DIR%\bin\libgomp-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
copy /y "%MINGW_LIB_DIR%\libgomp-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
copy /y "%MINGW_DIR%\bin\libquadmath-0.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul
copy /y "%MINGW_LIB_DIR%\libquadmath-0.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul 2>nul

REM Copy Qt DLLs
copy /y "%QTDIR%\bin\libgcc_s_seh-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\libstdc++-6.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\libwinpthread-1.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6Core.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6Gui.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6Widgets.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6Network.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6Svg.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul
copy /y "%QTDIR%\bin\Qt6PrintSupport.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul

REM Copy platform plugins
if not exist "%ABS_PRJ_PATH%\build\bin\release\platforms" mkdir "%ABS_PRJ_PATH%\build\bin\release\platforms"
copy /y "%QTDIR%\plugins\platforms\qwindows.dll" "%ABS_PRJ_PATH%\build\bin\release\platforms\" >nul

REM Copy style plugins
if not exist "%ABS_PRJ_PATH%\build\bin\release\styles" mkdir "%ABS_PRJ_PATH%\build\bin\release\styles"
copy /y "%QTDIR%\plugins\styles\qwindowsvistastyle.dll" "%ABS_PRJ_PATH%\build\bin\release\styles\" >nul

REM Copy DLLs
if exist "%ABS_PRJ_PATH%\communication\LibUsb\hidapi.dll" copy /y "%ABS_PRJ_PATH%\communication\LibUsb\hidapi.dll" "%ABS_PRJ_PATH%\build\bin\release\" >nul

REM Create installer package directory
set "PACKAGE_DIR=%ABS_PRJ_PATH%\installer\packages\com.pioneer.dspcontroller"
if not exist "%PACKAGE_DIR%\meta" mkdir "%PACKAGE_DIR%\meta" >nul 2>nul
if not exist "%PACKAGE_DIR%\data" mkdir "%PACKAGE_DIR%\data"
if not exist "%PACKAGE_DIR%\data\platforms" mkdir "%PACKAGE_DIR%\data\platforms"
if not exist "%PACKAGE_DIR%\data\styles" mkdir "%PACKAGE_DIR%\data\styles"
if not exist "%PACKAGE_DIR%\data\imageformats" mkdir "%PACKAGE_DIR%\data\imageformats"

REM Clean old installer package directory to ensure using latest files
if exist "%PACKAGE_DIR%\data" rmdir /S /Q "%PACKAGE_DIR%\data"
if not exist "%PACKAGE_DIR%\data" mkdir "%PACKAGE_DIR%\data"
if not exist "%PACKAGE_DIR%\data\platforms" mkdir "%PACKAGE_DIR%\data\platforms"
if not exist "%PACKAGE_DIR%\data\styles" mkdir "%PACKAGE_DIR%\data\styles"
if not exist "%PACKAGE_DIR%\data\imageformats" mkdir "%PACKAGE_DIR%\data\imageformats"
if not exist "%PACKAGE_DIR%\data\networkinformation" mkdir "%PACKAGE_DIR%\data\networkinformation"
if not exist "%PACKAGE_DIR%\data\tls" mkdir "%PACKAGE_DIR%\data\tls"

REM Copy main program and necessary files, exclude intermediate files
echo [INFO] Copying main executable...
xcopy /Y "%ABS_PRJ_PATH%\build\bin\release\DSP Controller.exe" "%PACKAGE_DIR%\data\" >nul
if errorlevel 1 (
    echo [ERROR] Failed to copy DSP Controller.exe
    exit /b 1
)
echo [INFO] Copying DLLs...
xcopy /Y "%ABS_PRJ_PATH%\build\bin\release\*.dll" "%PACKAGE_DIR%\data\" >nul
xcopy /Y "%ABS_PRJ_PATH%\build\bin\release\*.ini" "%PACKAGE_DIR%\data\" >nul 2>nul
xcopy /Y "%ABS_PRJ_PATH%\build\bin\release\*.json" "%PACKAGE_DIR%\data\" >nul 2>nul

REM Delete debug information and intermediate files
if exist "%PACKAGE_DIR%\data\*.pdb" del /F /Q "%PACKAGE_DIR%\data\*.pdb"
if exist "%PACKAGE_DIR%\data\*.exp" del /F /Q "%PACKAGE_DIR%\data\*.exp"
if exist "%PACKAGE_DIR%\data\*.lib" del /F /Q "%PACKAGE_DIR%\data\*.lib"
if exist "%PACKAGE_DIR%\data\*.a" del /F /Q "%PACKAGE_DIR%\data\*.a"
if exist "%PACKAGE_DIR%\data\*.o" del /F /Q "%PACKAGE_DIR%\data\*.o"
if exist "%PACKAGE_DIR%\data\*.obj" del /F /Q "%PACKAGE_DIR%\data\*.obj"
if exist "%PACKAGE_DIR%\data\*.cpp" del /F /Q "%PACKAGE_DIR%\data\*.cpp"
if exist "%PACKAGE_DIR%\data\*.h" del /F /Q "%PACKAGE_DIR%\data\*.h"
if exist "%PACKAGE_DIR%\data\*.qrc" del /F /Q "%PACKAGE_DIR%\data\*.qrc"
if exist "%PACKAGE_DIR%\data\*.qml" del /F /Q "%PACKAGE_DIR%\data\*.qml"
if exist "%PACKAGE_DIR%\data\*.pro" del /F /Q "%PACKAGE_DIR%\data\*.pro"
if exist "%PACKAGE_DIR%\data\Makefile*" del /F /Q "%PACKAGE_DIR%\data\Makefile*"

REM Copy minimal necessary Qt plugins
copy /Y "%QTDIR%\plugins\platforms\qwindows.dll" "%PACKAGE_DIR%\data\platforms\" >nul
copy /Y "%QTDIR%\plugins\styles\qwindowsvistastyle.dll" "%PACKAGE_DIR%\data\styles\" >nul
copy /Y "%QTDIR%\plugins\imageformats\qjpeg.dll" "%PACKAGE_DIR%\data\imageformats\" >nul
copy /Y "%QTDIR%\plugins\imageformats\qsvg.dll" "%PACKAGE_DIR%\data\imageformats\" >nul

REM Copy network-related plugins (CRITICAL for network functionality)
if not exist "%PACKAGE_DIR%\data\networkinformation" mkdir "%PACKAGE_DIR%\data\networkinformation"
if not exist "%PACKAGE_DIR%\data\tls" mkdir "%PACKAGE_DIR%\data\tls"

REM Copy network information plugins
copy /Y "%ABS_PRJ_PATH%\build\bin\release\networkinformation\*.dll" "%PACKAGE_DIR%\data\networkinformation\" >nul 2>nul
if errorlevel 1 (
    echo [WARNING] Failed to copy networkinformation plugins from build directory, trying Qt plugins...
    copy /Y "%QTDIR%\plugins\networkinformation\*.dll" "%PACKAGE_DIR%\data\networkinformation\" >nul 2>nul
)

REM Copy TLS/SSL backend plugins
copy /Y "%ABS_PRJ_PATH%\build\bin\release\tls\*.dll" "%PACKAGE_DIR%\data\tls\" >nul 2>nul
if errorlevel 1 (
    echo [WARNING] Failed to copy TLS plugins from build directory, trying Qt plugins...
    copy /Y "%QTDIR%\plugins\tls\*.dll" "%PACKAGE_DIR%\data\tls\" >nul 2>nul
)

REM If QML modules exist, only copy compiled files
if exist "%ABS_PRJ_PATH%\build\bin\release\qml" (
    xcopy /E /I /Y "%ABS_PRJ_PATH%\build\bin\release\qml" "%PACKAGE_DIR%\data\qml\" >nul
    REM Delete QML source files and debug information
    if exist "%PACKAGE_DIR%\data\qml\*.qml" del /F /Q "%PACKAGE_DIR%\data\qml\*.qml"
    if exist "%PACKAGE_DIR%\data\qml\*.qmlc" del /F /Q "%PACKAGE_DIR%\data\qml\*.qmlc"
    if exist "%PACKAGE_DIR%\data\qml\*.qrc" del /F /Q "%PACKAGE_DIR%\data\qml\*.qrc"
)

REM Copy hidapi.dll if exists
if exist "%ABS_PRJ_PATH%\communication\LibUsb\hidapi.dll" (
    copy /Y "%ABS_PRJ_PATH%\communication\LibUsb\hidapi.dll" "%PACKAGE_DIR%\data\" >nul
)

REM Copy translation files to config directory (global translation)
if exist "%ABS_PRJ_PATH%\packaging\translations\en_US.qm" (
    copy /Y "%ABS_PRJ_PATH%\packaging\translations\en_US.qm" "%ABS_PRJ_PATH%\installer\config\" >nul
    echo [INFO] Copied global translation file: en_US.qm
)
if exist "%ABS_PRJ_PATH%\packaging\translations\ja_JP.qm" (
    copy /Y "%ABS_PRJ_PATH%\packaging\translations\ja_JP.qm" "%ABS_PRJ_PATH%\installer\config\" >nul
    echo [INFO] Copied global translation file: ja_JP.qm
)

REM Copy translation files to package meta directory (package level translation)
if exist "%ABS_PRJ_PATH%\packaging\translations\en_US.qm" (
    copy /Y "%ABS_PRJ_PATH%\packaging\translations\en_US.qm" "%PACKAGE_DIR%\meta\" >nul
    echo [INFO] Copied package translation file: en_US.qm
)
if exist "%ABS_PRJ_PATH%\packaging\translations\ja_JP.qm" (
    copy /Y "%ABS_PRJ_PATH%\packaging\translations\ja_JP.qm" "%PACKAGE_DIR%\meta\" >nul
    echo [INFO] Copied package translation file: ja_JP.qm
)

REM Check key file integrity
if not exist "%PACKAGE_DIR%\data\DSP Controller.exe" (
    echo [ERROR] DSP Controller.exe not found in directory
    exit /b 1
)

REM Verify executable file size and timestamp
echo [INFO] Verifying executable file...
for %%F in ("%ABS_PRJ_PATH%\build\bin\release\DSP Controller.exe") do set SOURCE_SIZE=%%~zF
for %%F in ("%PACKAGE_DIR%\data\DSP Controller.exe") do set PACKAGE_SIZE=%%~zF
echo [INFO] Source file size: %SOURCE_SIZE% bytes
echo [INFO] Package file size: %PACKAGE_SIZE% bytes
if not "%SOURCE_SIZE%"=="%PACKAGE_SIZE%" (
    echo [ERROR] File size mismatch! Source and package files are different.
    exit /b 1
)

REM Verify key MinGW runtime libraries exist
echo [INFO] Verifying MinGW runtime libraries...
if not exist "%PACKAGE_DIR%\data\libgcc_s_seh-1.dll" (
    echo [ERROR] Missing libgcc_s_seh-1.dll - this will cause runtime errors
    exit /b 1
)
if not exist "%PACKAGE_DIR%\data\libstdc++-6.dll" (
    echo [ERROR] Missing libstdc++-6.dll - this will cause runtime errors
    exit /b 1
)
if not exist "%PACKAGE_DIR%\data\libwinpthread-1.dll" (
    echo [ERROR] Missing libwinpthread-1.dll - this will cause runtime errors
    exit /b 1
)
if not exist "%PACKAGE_DIR%\data\libssp-0.dll" (
    echo [WARNING] Missing libssp-0.dll - application may fail to start
    echo [INFO] Attempting alternative search for libssp-0.dll...
    if exist "%MINGW_LIB_DIR%\libssp-0.dll" (
        copy /y "%MINGW_LIB_DIR%\libssp-0.dll" "%PACKAGE_DIR%\data\" >nul
        echo [INFO] Successfully copied libssp-0.dll from alternative location
    ) else (
        echo [WARNING] libssp-0.dll not found in any known location
    )
)

if not exist "%PACKAGE_DIR%\data\platforms\qwindows.dll" (
    echo [ERROR] Missing required platforms plugin qwindows.dll
    exit /b 1
)

REM Verify network-related plugins exist
echo [INFO] Verifying network plugins...
if not exist "%PACKAGE_DIR%\data\Qt6Network.dll" (
    echo [ERROR] Missing Qt6Network.dll - network functionality will not work
    exit /b 1
)

REM Check for TLS plugins (at least one should exist)
set "TLS_FOUND=0"
if exist "%PACKAGE_DIR%\data\tls\qcertonlybackend.dll" set "TLS_FOUND=1"
if exist "%PACKAGE_DIR%\data\tls\qschannelbackend.dll" set "TLS_FOUND=1"
if "%TLS_FOUND%"=="0" (
    echo [WARNING] No TLS backend plugins found - HTTPS connections may fail
    echo [INFO] Attempting to copy TLS plugins from Qt installation...
    copy /Y "%QTDIR%\plugins\tls\*.dll" "%PACKAGE_DIR%\data\tls\" >nul 2>nul
)

REM Check for network information plugins
if not exist "%PACKAGE_DIR%\data\networkinformation\qnetworklistmanager.dll" (
    echo [WARNING] Network information plugin missing - network status detection may not work
    echo [INFO] Attempting to copy from Qt installation...
    copy /Y "%QTDIR%\plugins\networkinformation\*.dll" "%PACKAGE_DIR%\data\networkinformation\" >nul 2>nul
)

REM Create config folder
if not exist "%ABS_PRJ_PATH%\installer\config" mkdir "%ABS_PRJ_PATH%\installer\config" >nul 2>nul

REM Generate config.xml
echo ^<?xml version="1.0" encoding="UTF-8"?^> > "%ABS_PRJ_PATH%\installer\config\config.xml"
echo ^<Installer^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<Name^>DSP Controller^</Name^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<Version^>%VERSION%^</Version^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<Title^>DSP Controller Installer^</Title^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<Publisher^>pioneer^</Publisher^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<StartMenuDir^>DSP Controller^</StartMenuDir^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<TargetDir^>@ApplicationsDir@/DSP Controller^</TargetDir^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<MaintenanceToolName^>MaintenanceTool^</MaintenanceToolName^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<AllowNonAsciiCharacters^>true^</AllowNonAsciiCharacters^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<WizardStyle^>Modern^</WizardStyle^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<RemoveTargetDir^>false^</RemoveTargetDir^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<AllowSpaceInPath^>true^</AllowSpaceInPath^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<AllowUnstableComponents^>true^</AllowUnstableComponents^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<SupportsModify^>true^</SupportsModify^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<ControlScript^>control.qs^</ControlScript^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<RunProgram^>@TargetDir@/DSP Controller.exe^</RunProgram^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<RunProgramDescription^>Run DSP Controller now^</RunProgramDescription^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<InstallerApplicationIcon^>../logo^</InstallerApplicationIcon^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<WizardDefaultWidth^>800^</WizardDefaultWidth^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<WizardDefaultHeight^>600^</WizardDefaultHeight^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^<Translations^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo         ^<Translation^>en_US.qm^</Translation^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo         ^<Translation^>ja_JP.qm^</Translation^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo     ^</Translations^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"
echo ^</Installer^> >> "%ABS_PRJ_PATH%\installer\config\config.xml"

REM Get standard date (YYYY-MM-DD)
for /f "skip=1 tokens=1 delims=." %%a in ('wmic os get localdatetime') do if not defined _dt set _dt=%%a
set "YYYY=%_dt:~0,4%"
set "MM=%_dt:~4,2%"
set "DD=%_dt:~6,2%"
set "RELEASE_DATE=%YYYY%-%MM%-%DD%"

REM Modify package.xml to include license
echo ^<?xml version="1.0" encoding="UTF-8"?^> > "%PACKAGE_DIR%\meta\package.xml"
echo ^<Package^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Name^>com.pioneer.dspcontroller^</Name^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<DisplayName^>DSP Controller^</DisplayName^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Description^>DSP Controller Software^</Description^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Version^>%VERSION%^</Version^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<ReleaseDate^>%RELEASE_DATE%^</ReleaseDate^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Default^>true^</Default^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Script^>installscript.qs^</Script^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<SortingPriority^>100^</SortingPriority^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<ForcedInstallation^>true^</ForcedInstallation^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Essential^>true^</Essential^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^<Translations^> >> "%PACKAGE_DIR%\meta\package.xml"
echo         ^<Translation^>en_US.qm^</Translation^> >> "%PACKAGE_DIR%\meta\package.xml"
echo         ^<Translation^>ja_JP.qm^</Translation^> >> "%PACKAGE_DIR%\meta\package.xml"
echo     ^</Translations^> >> "%PACKAGE_DIR%\meta\package.xml"
echo ^</Package^> >> "%PACKAGE_DIR%\meta\package.xml"

REM Generate installscript.qs
echo function Component() { > "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Constructor >> "%PACKAGE_DIR%\meta\installscript.qs"
echo } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo // Multi-language support function >> "%PACKAGE_DIR%\meta\installscript.qs"
echo function getLocalizedText(key) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     var locale = installer.value("os"); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     var language = QLocale().name(); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Detect Japanese environment >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     if (language.indexOf("ja") === 0) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         var japaneseTexts = { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "install_completed": "インストールが完了しました", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "install_success": "DSP Controllerが正常にインストールされました！", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "uninstall_completed": "アンインストール完了", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "uninstall_success": "DSP Controllerがアンインストールされました！" >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         }; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         return japaneseTexts[key] || key; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Default English text >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     var englishTexts = { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         "install_completed": "Installation Completed", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         "install_success": "DSP Controller has been successfully installed!", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         "uninstall_completed": "Uninstall Completed", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         "uninstall_success": "DSP Controller has been uninstalled!" >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     }; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     return englishTexts[key] || key; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo Component.prototype.createOperations = function() { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Default operations >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     component.createOperations(); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     if (systemInfo.productType === "windows") { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         // Create shortcuts >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         component.addOperation("CreateShortcut", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "@TargetDir@/DSP Controller.exe", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "@DesktopDir@/DSP Controller.lnk", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "workingDirectory=@TargetDir@", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "iconPath=@TargetDir@/DSP Controller.exe", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "description=DSP Controller"); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         component.addOperation("CreateShortcut", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "@TargetDir@/DSP Controller.exe", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "@StartMenuDir@/DSP Controller.lnk", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "workingDirectory=@TargetDir@", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "iconPath=@TargetDir@/DSP Controller.exe", >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             "description=DSP Controller"); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo Component.prototype.beginInstallation = function() { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     var targetDir = installer.value("TargetDir"); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     var currentVersion = "%VERSION%"; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Allow overwrite installation, no blocking operations >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     if (installer.fileExists(targetDir)) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         console.log("Target directory exists, proceeding with overwrite installation..."); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         // Try to stop running program >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         var mainProgram = targetDir + "/DSP Controller.exe"; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         if (installer.fileExists(mainProgram)) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             try { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 // Try to terminate running program >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 installer.execute("taskkill", ["/F", "/IM", "DSP Controller.exe"]); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } catch (e) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 // Ignore error, program may not be running >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             // Delete old main program file (before file copy) >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             try { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 installer.performOperation("Delete", mainProgram); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } catch (e) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 console.log("Could not delete old executable: " + e.message); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         // Delete old MaintenanceTool (if exists) >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         var maintenanceTool = targetDir + "/MaintenanceTool.exe"; >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         if (installer.fileExists(maintenanceTool)) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             try { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 installer.performOperation("Delete", maintenanceTool); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } catch (e) { >> "%PACKAGE_DIR%\meta\installscript.qs"
echo                 console.log("Could not delete old maintenance tool: " + e.message); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo             } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo         } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     } >> "%PACKAGE_DIR%\meta\installscript.qs"
echo. >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     // Continue installation, allow overwriting all files >> "%PACKAGE_DIR%\meta\installscript.qs"
echo     console.log("Installation proceeding with version: " + currentVersion); >> "%PACKAGE_DIR%\meta\installscript.qs"
echo } >> "%PACKAGE_DIR%\meta\installscript.qs"

REM Generate control.qs
echo function Controller() { > "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Initialize installer with forced settings >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.installationFinished.connect(this, Controller.prototype.installationFinished); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.uninstallationFinished.connect(this, Controller.prototype.uninstallationFinished); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Completely disable license related pages >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.LicenseCheck, false); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.Introduction, true); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.TargetDirectory, true); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.ComponentSelection, false); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.ReadyForInstallation, true); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.PerformInstallation, true); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.InstallationFinished, true); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo Controller.prototype.IntroductionPageCallback = function() { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Ensure license page is skipped >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setDefaultPageVisible(QInstaller.LicenseCheck, false); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo Controller.prototype.TargetDirectoryPageCallback = function() { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Force allow overwrite installation, disable directory check >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     var page = gui.currentPageWidget(); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     if (page != null) { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         // Force set directory as valid >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         page.complete = true; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         page.validatePage = function() { return true; }; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         // Override isComplete function >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         page.isComplete = function() { return true; }; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         // Force enable next button >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         gui.findChild(page, "NextButton").enabled = true; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Disable built-in directory validation >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setValue("SkipTargetDirectoryPage", "false"); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo Controller.prototype.ReadyForInstallationPageCallback = function() { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Force continue installation, ignore any warnings >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     installer.setValue("allowOverwrite", "true"); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo // Multi-language support function >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo function getLocalizedText(key) { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     var language = QLocale().name(); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Detect Japanese environment >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     if (language.indexOf("ja") === 0) { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         var japaneseTexts = { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo             "install_completed": "インストール完了", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo             "install_success": "DSP Controllerが正常にインストールされました！", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo             "uninstall_completed": "アンインストール完了", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo             "uninstall_success": "DSP Controllerがアンインストールされました！" >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         }; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         return japaneseTexts[key] || key; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // Default English text >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     var englishTexts = { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         "install_completed": "Installation Completed", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         "install_success": "DSP Controller has been successfully installed!", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         "uninstall_completed": "Uninstall Completed", >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         "uninstall_success": "DSP Controller has been uninstalled!" >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     }; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     return englishTexts[key] || key; >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo Controller.prototype.installationFinished = function() { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // After installation completed >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     if (systemInfo.productType === "windows") { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         QMessageBox.information("install.finished", getLocalizedText("install_completed"), getLocalizedText("install_success")); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo. >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo Controller.prototype.uninstallationFinished = function() { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     // After uninstallation completed >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     if (systemInfo.productType === "windows") { >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo         QMessageBox.information("uninstall.finished", getLocalizedText("uninstall_completed"), getLocalizedText("uninstall_success")); >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo     } >> "%ABS_PRJ_PATH%\installer\config\control.qs"
echo } >> "%ABS_PRJ_PATH%\installer\config\control.qs"

REM Set environment variables to force English
set "LANG=en_US"
set "LC_ALL=en_US.UTF-8"

REM Create installer package
call "%QIF_DIR%\bin\binarycreator.exe" --offline-only -c "%ABS_PRJ_PATH%\installer\config\config.xml" -p "%ABS_PRJ_PATH%\installer\packages" "%ABS_PRJ_PATH%\DSP_Controller_Setup_%VERSION%.exe"
if errorlevel 1 (
    echo [ERROR] Installer creation failed
    pause
    exit /b 1
)

echo [INFO] Installer build finished!
echo [INFO] Output: %ABS_PRJ_PATH%\DSP_Controller_Setup_%VERSION%.exe