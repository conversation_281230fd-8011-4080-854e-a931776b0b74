import QtQuick
import QtQuick.Controls.Basic

Menu {
    // background: Rectangle {
    //     color: "#484C54"
    //     border.width: 1
    //     border.color: "#646870"
    // }

    delegate: MenuItem {
        implicitWidth: 120
        height: 24
        // opacity: enabled ? 1 : 0.3

        contentItem: Text {
            color: "#E8E8E8"
            font.family: "Segoe UI"
            font.pixelSize: 12
            leftPadding: 6
            rightPadding: 24
            verticalAlignment: Text.AlignVCenter
            text: parent.text
            opacity: parent.enabled ? 1 : 0.3
        }

        background: Rectangle {
            color: parent.highlighted ? "#646870" : "#484C54"
        }

        arrow: Image {
            anchors.right: parent.right
            anchors.rightMargin: 6
            anchors.verticalCenter: parent.verticalCenter
            source: "qrc:/Image/icn_chevright.png"
            visible: parent.subMenu
        }
    }
}
