#include "Mid01Handler.h"

Mid01Handler::Mid01Handler()
{
    memset(&mDeviceOperation01, 0, sizeof(mDeviceOperation01));
    memset(&mDeviceOperation03, 0, sizeof(mDeviceOperation03));
    memset(&mDeviceOperation0809, 0, sizeof(mDeviceOperation0809));
}

void Mid01Handler::parseReceivedData(uint8_t id, const QByteArray &data)
{
    switch (id)
    {
    case static_cast<uint8_t>(DeviceOperation::SID::CONNECT_REQUEST):
    {
        parse01Data(data);
        break;
    }
    case static_cast<uint8_t>(DeviceOperation::SID::HEARTBEAT):
    {
        parse03Data(data);
        break;
    }
    case static_cast<uint8_t>(DeviceOperation::SID::SOURCE_ENABLE_QUERY):
    {
        parse09Data(data);
        break;
    }
    default:
        break;
    }
}

void Mid01Handler::parse01Data(const QByteArray &data)
{
    // bool ret = false;
    DeviceOperation01 temp01;
    memcpy(&temp01, data.data(), data.size());
    // if(0 != memcmp(temp01.deviceType, mDeviceOperation01.deviceType, sizeof(mDeviceOperation01.deviceType)))
    // {
        emit deviceTypeChanged(QString::fromUtf8(reinterpret_cast<const char *>(temp01.deviceType)
                                                      , -1));
    //     ret = true;
    // }
    // if(temp01.mainVersion != mDeviceOperation01.mainVersion)
    // {
        emit mainVersionChanged(temp01.mainVersion);
    //     ret = true;
    // }
    // if(temp01.subVersion != mDeviceOperation01.subVersion)
    // {
        emit subVersionChanged(temp01.subVersion);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mDeviceOperation01, data.data(), data.size());
    // }
}

void Mid01Handler::parse03Data(const QByteArray &data)
{
    // bool ret = false;
    DeviceOperation03 temp03;
    memcpy(&temp03, data.data(), data.size());
    // if(temp03.mainGain != mDeviceOperation03.mainGain)
    // {
        // emit mainGainChanged(temp03.mainGain);
    //     ret = true;
    // }
    // if(temp03.mainMute != mDeviceOperation03.mainMute)
    // {
        // emit mainMuteChanged(temp03.mainMute);
    //     ret = true;
    // }
    // if(temp03.usbConnected != mDeviceOperation03.usbConnected)
    // {
        emit usbConnectedChanged(temp03.usbConnected);
    //     ret = true;
    // }
    // if(temp03.btConnected != mDeviceOperation03.btConnected)
    // {
        emit btConnectedChanged(temp03.btConnected);
    //     ret = true;
    // }
    // if(temp03.aptxConnected != mDeviceOperation03.aptxConnected)
    // {
        emit aptxConnectedChanged(temp03.aptxConnected);
    //     ret = true;
    // }
    // if(temp03.uacConnected != mDeviceOperation03.uacConnected)
    // {
        emit uacConnectedChanged(temp03.uacConnected);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mDeviceOperation03, data.data(), data.size());
    // }
}

void Mid01Handler::parse09Data(const QByteArray &data)
{
    // bool ret = false;
    DeviceOperation0809 temp09;
    memcpy(&temp09, data.data(), data.size());
    // if(temp09.mainUnit != mDeviceOperation0809.mainUnit)
    // {
        emit mainUnitSupportedChanged(temp09.mainUnit);
    //     ret = true;
    // }
    // if(temp09.aux != mDeviceOperation0809.aux)
    // {
        emit auxSupportedChanged(temp09.aux);
    //     ret = true;
    // }
    // if(temp09.bt != mDeviceOperation0809.bt)
    // {
        emit btSupportedChanged(temp09.bt);
    //     ret = true;
    // }
    // if(temp09.spdif != mDeviceOperation0809.spdif)
    // {
        emit spdifSupportedChanged(temp09.spdif);
    //     ret = true;
    // }
    // if(temp09.usbAudio != mDeviceOperation0809.usbAudio)
    // {
        emit usbAudioSupportedChanged(temp09.usbAudio);
    //     ret = true;
    // }

    // if(ret)
    // {
        memcpy(&mDeviceOperation0809, data.data(), data.size());
    // }
}

QByteArray Mid01Handler::send01Data()
{
    unsigned char token[32] = {0};
    mAesApi.master_auth1(token);

    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    QByteArray token1 = QByteArray::fromHex(QByteArray(reinterpret_cast<const char*>(token), sizeof(token) / sizeof(token[0])));
    frameData.append(token1);

    return frameData;
}

QByteArray Mid01Handler::send03Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

QByteArray Mid01Handler::send08Data(const DeviceOperation0809 &data)
{
    QByteArray frameData;
    frameData.append(QByteArray((char *)&data, sizeof(DeviceOperation0809)));

    return frameData;
}

QByteArray Mid01Handler::send09Data()
{
    QByteArray frameData;
    frameData.append((uint8_t)0x00);

    return frameData;
}

const DeviceOperation01& Mid01Handler::getDeviceOperation01()
{
    return mDeviceOperation01;
}

const DeviceOperation03& Mid01Handler::getDeviceOperation03()
{
    return mDeviceOperation03;
}

const DeviceOperation0809& Mid01Handler::getDeviceOperation0809()
{
    return mDeviceOperation0809;
}
