/**
 * @file canvas_redraw_test.cpp
 * @brief Canvas重绘测试示例
 * 演示某个通道32~36号点更新完之后如何触发canvas重绘
 */

#include "datamodel.h"
#include <QDebug>

/**
 * @brief 演示Canvas重绘功能
 */
void demonstrateCanvasRedrawFeature()
{
    qDebug() << "\n=== Canvas重绘功能演示 ===";

    // 创建数据模型
    DataModel dataModel;

    qDebug() << "\n1. 测试预设应用触发重绘:";
    qDebug() << "应用SuperBass Level5预设到通道0...";
    dataModel.setCurvePreset(0, DataModel::PRESET_TYPE_SUPERBASS, DataModel::PRESET_LEVEL_5);
    qDebug() << "→ 32~36号点已更新，Canvas重绘信号已发送";

    qDebug() << "\n2. 测试预设清除触发重绘（设置为Flat）:";
    qDebug() << "清除通道0的预设（设置为Flat预设）...";
    dataModel.clearCurvePreset(0);
    qDebug() << "→ 32~36号点已设置为Flat预设，Canvas重绘信号已发送";

    qDebug() << "\n3. 测试手动更新单个不可见点:";
    qDebug() << "更新通道0的32号点...";
    dataModel.setInvisiblePointParams(0, 32, 100.0, 4.32, 3.0, DataModel::EQ_TYPE_PEAK);
    qDebug() << "→ 32号点已更新，Canvas重绘信号已发送";

    qDebug() << "\n4. 测试批量更新不可见点:";
    QVector<DataModel::AdjustablePointData> batchData;
    for (int i = 0; i < 5; ++i) {
        DataModel::AdjustablePointData pointData;
        pointData.frequency = 100.0 * (i + 1);
        pointData.qValue = 4.32;
        pointData.gain = i - 2.0; // -2 to +2 dB
        pointData.type = DataModel::EQ_TYPE_PEAK;
        batchData.append(pointData);
    }
    dataModel.setInvisiblePointsForCurve(0, batchData);
    qDebug() << "→ 32~36号点已批量更新，Canvas重绘信号已发送";

    qDebug() << "\n5. 测试Flat预设:";
    qDebug() << "直接应用Flat预设到通道1...";
    dataModel.setCurvePreset(1, DataModel::PRESET_TYPE_FLAT, DataModel::PRESET_LEVEL_1);
    qDebug() << "→ 32~36号点已设置为Flat预设值，Canvas重绘信号已发送";

    qDebug() << "\n=== 重绘机制总结 ===";
    qDebug() << "✅ 预设应用会触发32~36号点更新和Canvas重绘";
    qDebug() << "✅ 预设清除会触发32~36号点设置为Flat预设和Canvas重绘";
    qDebug() << "✅ Flat预设可以关闭其他预设效果，对应PresetManager的Flat数据";
    qDebug() << "✅ 手动更新不可见点会触发Canvas重绘";
    qDebug() << "✅ 批量更新不可见点会触发Canvas重绘";
    qDebug() << "✅ 所有操作都通过信号槽机制自动触发重绘";
    qDebug() << "====================\n";
}