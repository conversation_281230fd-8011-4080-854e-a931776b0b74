#include "linkwitz_riley_filter.h"
#include "bessel_filter.h"
#include "butterworth_filter.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 斜率dB/oct转阶数（只允许12/24/48dB/oct，非标准值自动四舍五入到最近的标准阶数）
static int slope_to_order(int slope) {
    // 只允许12/24/48dB/oct
    if (slope < 18) return 2;      // 12dB/oct
    if (slope < 36) return 4;      // 24dB/oct
    return 8;                     // 48dB/oct
}

/**
 * @brief 计算宁克-锐（Linkwitz-Riley）低通滤波器系数
 */
void linkwitz_riley_lowpass_coeff(float fs, float fc, BiquadCoeffs* coeff) {
    // 优化参数
    float fc_adjust = 0.95f; // 调整截止频率使频率响应更准确
    fc = fc * fc_adjust;

    // 先计算Butterworth二阶低通
    float C = tanf(PI * fc / fs);
    float denom = C*C + sqrtf(2.0f)*C + 1.0f;
    float b0 = (C*C) / denom;
    float b1 = 2.0f * b0;
    float b2 = b0;
    float a0 = 1.0f;
    float a1 = (2.0f*C*C - 2.0f) / denom;
    float a2 = (C*C - sqrtf(2.0f)*C + 1.0f) / denom;

    // 打印调试信息
    // printf("[LR低通系数] fc=%.2fHz, 调整后=%.2fHz, C=%.6f\n", fc/fc_adjust, fc, C);
    // printf("[LR低通系数] b: %.8f %.8f %.8f\n", b0, b1, b2);
    // printf("[LR低通系数] a: %.8f %.8f %.8f\n", a0, a1, a2);

    // Linkwitz-Riley是Butterworth二阶的级联
    // 这里直接返回二阶系数，级联在滤波器响应叠加时实现
    coeff->b[0] = b0;
    coeff->b[1] = b1;
    coeff->b[2] = b2;
    coeff->a[0] = a0;
    coeff->a[1] = a1;
    coeff->a[2] = a2;
}

/**
 * @brief 计算宁克-锐（Linkwitz-Riley）高通滤波器系数
 */
void linkwitz_riley_highpass_coeff(float fs, float fc, BiquadCoeffs* coeff) {
    // 优化参数
    float fc_adjust = 1.05f; // 调整截止频率使频率响应更准确
    fc = fc * fc_adjust;

    float C = tanf(PI * fc / fs);
    float denom = C*C + sqrtf(2.0f)*C + 1.0f;
    float b0 = 1.0f / denom;
    float b1 = -2.0f * b0;
    float b2 = b0;
    float a0 = 1.0f;
    float a1 = (2.0f*C*C - 2.0f) / denom;
    float a2 = (C*C - sqrtf(2.0f)*C + 1.0f) / denom;

    // 打印调试信息
    // printf("[LR高通系数] fc=%.2fHz, 调整后=%.2fHz, C=%.6f\n", fc/fc_adjust, fc, C);
    // printf("[LR高通系数] b: %.8f %.8f %.8f\n", b0, b1, b2);
    // printf("[LR高通系数] a: %.8f %.8f %.8f\n", a0, a1, a2);

    coeff->b[0] = b0;
    coeff->b[1] = b1;
    coeff->b[2] = b2;
    coeff->a[0] = a0;
    coeff->a[1] = a1;
    coeff->a[2] = a2;
}

/**
 * @brief 计算宁克-锐（Linkwitz-Riley）滤波器在各频率点的复数响应
 * @param type 滤波器类型（7=低通，8=高通）
 * @param fc 截止频率
 * @param slope_rate 斜率（dB/oct），只允许12/24/48dB/oct
 * @param freq_list 频率点数组
 * @param resp 输出复数响应（累乘）
 *
 * 实现说明：
 * Linkwitz-Riley滤波器为Butterworth二阶滤波器级联，偶数阶（如4阶、8阶），
 * 截止点-6dB，幅频/相位特性与专业分频器一致。
 *
 * 日志将输出每阶响应的关键参数，便于调试。
 */
void linkwitz_riley_optimized_response(int type, double fc, int slope_rate,
                                      const DoubleArray* freq_list,
                                      double_complex* resp) {
    int order = slope_rate / 6;
    if (order < 2) order = 2;
    if (order % 2 != 0) order++;
    int sections = order / 2;
    BiquadCoeffs coeffs;
    char logbuf[256];
    // 第一次级联
    for (int sec = 0; sec < sections; ++sec) {
        if (type == FILTER_TYPE_LINKWITZ_RILEY_LP) {
            butterworth_section_coeffs(order, sec, FS, fc, &coeffs, FILTER_TYPE_BUTTERWORTH_LP);
            snprintf(logbuf, sizeof(logbuf), "[LR-LP] order:%d, section:%d, fc:%.2f", order, sec, fc);
        } else {
            butterworth_section_coeffs(order, sec, FS, fc, &coeffs, FILTER_TYPE_BUTTERWORTH_HP);
            snprintf(logbuf, sizeof(logbuf), "[LR-HP] order:%d, section:%d, fc:%.2f", order, sec, fc);
        }
        CURVE_LOG("%s\n", logbuf);
        calc_single_biquad_resp(freq_list, FS, &coeffs, resp, 2);
    }
    // 第二次级联
    for (int sec = 0; sec < sections; ++sec) {
        if (type == FILTER_TYPE_LINKWITZ_RILEY_LP) {
            butterworth_section_coeffs(order, sec, FS, fc, &coeffs, FILTER_TYPE_BUTTERWORTH_LP);
        } else {
            butterworth_section_coeffs(order, sec, FS, fc, &coeffs, FILTER_TYPE_BUTTERWORTH_HP);
        }
        calc_single_biquad_resp(freq_list, FS, &coeffs, resp, 2);
    }
    CURVE_LOG("[LR] Response calculation finished, fc=%.2f, order=%d\n", fc, order*2);
}