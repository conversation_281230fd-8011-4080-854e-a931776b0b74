import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import "../framework"

SubWindow {
    id: root
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 784
    contentHeight: 480
    titleStr: qsTr("Memory Copy")
    confirmEnabled: ((srcType !== desType) || (srcMemory !== desMemory))

    property int srcType: currentMemoryType
    property int srcMemory: currentMemoryId
    property int desType: 0
    property int desMemory: 0
    property string desName: ""

    property int currentMemoryType: dataMap["uiDataMap"]["selectedMemoryType"]
    property int currentMemoryId: dataMap["uiDataMap"]["selectedMemoryId"]
    property string currentMemoryName: dataMap["memorySettingType" + currentMemoryType + "Index" + currentMemoryId]["name"]
    property int currentMemory: dataMap["currentMemory"]

    onClickCancel: clickClose()
    onClickConfirm: {
        if((srcType !== desType) || (srcMemory !== desMemory))
        {
            commonCtrl.setMemoryCopy(srcType, srcMemory, desType, desMemory, desName)
        }
        clickClose()
    }

    contentItem: Rectangle {
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"

        Text {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("The current sound field settings will be written to the selected Memory.
The contents of the Memory will be discarded.
Please select the Memory Number and click OK.")
        }

        Rectangle {
            anchors.left: parent.left
            anchors.leftMargin: 510
            anchors.top: parent.top
            anchors.topMargin: 24
            color: "#747880"
            width: 1
            height: 48
        }

        Text {
            anchors.left: parent.left
            anchors.leftMargin: 526
            anchors.top: parent.top
            anchors.topMargin: 24
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: qsTr("Current Memory")
        }

        Text {
            anchors.left: parent.left
            anchors.leftMargin: 526
            anchors.top: parent.top
            anchors.topMargin: 40
            height: 8
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            verticalAlignment: Text.AlignVCenter
            text: ((0 === currentMemoryType) ? "M" : "S") + (currentMemoryId + 1).toString() + ":" + currentMemoryName
        }

        Row {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 108
            spacing: 24

            Repeater {
                model: [qsTr("Main Unit Memory"), qsTr("DSP Source Memory")]

                Rectangle {
                    width: 348
                    height: 252
                    color: "transparent"
                    border.width: 1
                    border.color: "#747880"

                    Rectangle {
                        anchors.left: parent.left
                        anchors.leftMargin: 8
                        anchors.top: parent.top
                        anchors.topMargin: -4
                        width: title.contentWidth + 8 + 8
                        height: 8
                        color: "#3C4048"

                        Text {
                            id: title
                            anchors.centerIn: parent
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: modelData
                        }
                    }

                    Row {
                        anchors.left: parent.left
                        anchors.leftMargin: 24
                        anchors.top: parent.top
                        anchors.topMargin: 24

                        Text {
                            anchors.verticalCenter: parent.verticalCenter
                            width: 100
                            height: 8
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: "No."
                        }

                        Text {
                            anchors.verticalCenter: parent.verticalCenter
                            height: 8
                            color: "#E8E8E8"
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Text.AlignVCenter
                            text: qsTr("Comment")
                        }
                    }
                }
            }
        }

        ButtonGroup {
            id: btnGroup
        }

        Grid {
            anchors.left: parent.left
            anchors.leftMargin: 48
            anchors.top: parent.top
            anchors.topMargin: 149
            rows: 6
            rowSpacing: 6
            columns: 2
            columnSpacing: 72
            flow: Grid.TopToBottom

            Repeater {
                model: 12

                Item {
                    id: memoryItem
                    width: 300
                    height: 24

                    property int type: (6 > modelData) ? 0 : 1
                    property int memory: (0 === type) ? modelData : (modelData - 6)
                    property int memoryEnable: dataMap["memorySettingType" + type + "Index" + memory]["enable"]
                    property string memoryName: dataMap["memorySettingType" + type + "Index" + memory]["name"]

                    MyRadioButton {
                        id: memoryBtn
                        anchors.verticalCenter: parent.verticalCenter
                        width: 100
                        ButtonGroup.group: btnGroup
                        text: ((0 === memoryItem.type) ? "M" : "S") + (memoryItem.memory + 1).toString() + " "
                              + ((0 === memoryItem.memoryEnable) ? qsTr("Invalid") : qsTr("Valid"))
                        enabled: (modelData !== root.currentMemory)
                        checked: (memoryItem.type === root.currentMemoryType) && (memoryItem.memory === root.currentMemoryId)

                        onCheckedChanged: {
                            if(checked)
                            {
                                root.desType = memoryItem.type
                                root.desMemory = memoryItem.memory
                                root.desName = memoryItem.memoryName

                                memoryCommentInput.text = memoryComment.text
                            }
                        }
                    }

                    Text {
                        id: memoryComment
                        anchors.left: memoryBtn.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 200
                        height: 8
                        color: "#E8E8E8"
                        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        opacity: enabled ? 1 : 0.3
                        visible: !memoryBtn.checked || !memoryBtn.enabled
                        enabled: memoryBtn.enabled
                        text: memoryItem.memoryName
                    }

                    Rectangle {
                        anchors.left: memoryBtn.right
                        anchors.verticalCenter: parent.verticalCenter
                        width: 200
                        height: 24
                        color: "#282828"
                        border.width: 1
                        border.color: "#646870"
                        visible: memoryBtn.checked && memoryBtn.enabled

                        TextInput {
                            id: memoryCommentInput
                            anchors.fill: parent
                            color: "#E8E8E8"
                            leftPadding: 3
                            rightPadding: leftPadding
                            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                            font.pixelSize: 12
                            verticalAlignment: Qt.AlignVCenter
                            clip: true
                            maximumLength: (/[^\u0000-\u00FF]/.test(text)) ? 8 : 32
                            text: memoryItem.memoryName

                            onTextEdited: root.desName = text
                        }
                    }

                    Connections {
                        target: root
                        function onClickClose() {
                            if((memoryItem.type === root.currentMemoryType) && (memoryItem.memory === root.currentMemoryId))
                            {
                                memoryBtn.checked = true
                            }
                        }
                    }
                }
            }
        }
    }
}
