import QtQuick
import QtQuick.Controls.Basic

SpinBox {
    id: control
    implicitHeight: 24
    editable: true
    // wheelEnabled: true
    leftPadding: 0
    rightPadding: leftPadding
    inputMethodHints: Qt.ImhFormattedNumbersOnly
    font.family: "Segoe UI"
    font.pixelSize: 10
    opacity: enabled ? 1 : 0.3

    property color focusBorderColor: "transparent"

    onValueChanged: {
        contentInput.text = textFromValue(value, locale)
    }

    contentItem: TextInput {
        id: contentInput
        color: "#E8E8E8"
        font: control.font
        horizontalAlignment: Qt.AlignHCenter
        verticalAlignment: Qt.AlignVCenter
        readOnly: !control.editable
        validator: control.validator
        inputMethodHints: control.inputMethodHints

        text: control.textFromValue(control.value, control.locale)

        onEditingFinished: {
        // onTextEdited: {
            var modifyText = (("-" === contentInput.text) || ("" === contentInput.text)) ?
                        control.textFromValue(control.value, control.locale) : contentInput.text
            var val = control.valueFromText(modifyText, control.locale)
            if (val < Math.min(control.from, control.to)) {
                contentInput.text = control.textFromValue(Math.min(control.from, control.to), control.locale)
            }
            else if(val > Math.max(control.from, control.to)) {
                contentInput.text = control.textFromValue(Math.max(control.from, control.to), control.locale)
            }
            else {
                contentInput.text = control.textFromValue(val, control.locale)
            }
        }
    }

    background: Rectangle {
        color: control.focus ? "#000000" : "transparent"
        border.width: 1
        border.color: control.focus ? focusBorderColor : "transparent"
    }

    up.indicator: Item {}
    down.indicator: Item {}
}
