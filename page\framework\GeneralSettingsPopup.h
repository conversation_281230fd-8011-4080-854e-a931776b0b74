#ifndef GENERALSETTINGSPOPUP_H
#define GENERALSETTINGSPOPUP_H

#include <QWidget>
#include <QButtonGroup>

namespace Ui {
class GeneralSettingsPopup;
}

class GeneralSettingsPopup : public QWidget
{
    Q_OBJECT

public:
    explicit GeneralSettingsPopup(QWidget *parent = nullptr);
    ~GeneralSettingsPopup();

    enum ENButtonGroup
    {
        LANGUAGES,
        DSP_SETTINGS,
        SOURCES,
        UPDATE_PC,
        UPDATE_DSP,
        UPDATE_REMOTE,
        INFO,
        RESET
    };

protected:
    void mousePressEvent(QMouseEvent* e) override;

signals:
    void buttonClicked(int btn);

private:
    Ui::GeneralSettingsPopup *ui;

    QButtonGroup* mBtnGroup;
};

#endif // GENERALSETTINGSPOPUP_H
