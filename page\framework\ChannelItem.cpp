#include "ChannelItem.h"
#include "dataCenter/DataHandlerDef.h"
#include "ui_ChannelItem.h"
#include <QMenu>
#include <QMap>
#include <QStyleFactory>

const QString linkBtnNoLinkQss = "QPushButton{background-color: #484C54; border: 1px solid #8E8E8E; border-radius: 2px; font-size: 12px; color: #FFFFFF;"
                                 " background-image: url(:/Image/link.png); background-position: left center; background-repeat: no-repeat; image: url(:/Image/link.png);}";
const QString linkBtnLinkedQss = "QPushButton{background-color: #484C54; border: 1px solid #8E8E8E; border-radius: 2px; font-size: 12px; color: #FFFFFF;"
                                 " background-image: url(:/Image/link.png); background-position: left center; background-repeat: no-repeat; image: url(:/Image/link.png);}";

ChannelItem::ChannelItem(QWidget *parent, int channel)
    : QFrame(parent)
    , ui(new Ui::ChannelItem)
    , mChannel(channel)
    , mHighPassEnabled(false)
    , mLowPassEnabled(false)
    , mIsMuted(false)
    , mSp(SpeakerEnum::SP_DEFAULT)
    , mVolumn10(0)
    , mIsReversed(false)
    , mLinkChannel(-1)
{
    ui->setupUi(this);

    ui->channelTitle->setText(QString(
        "<html><span style=' font-size:12px;'>CH </span><span style=' font-size:16px;font-weight:bold;'>%1</span></html>").arg(channel + 1));

    // QString menuQss = "QMenu { background-color: #383744; color: #FFFFFF; padding: 10px; }"
    //                   // "QMenu::right-arrow { image: url(:/Image/Volume.png); }"
    //                   "QMenu::item:selected { background-color: #80FFFFFF; }"
    //                   "QMenu::separator { height: 1px; background-color: #D9D9D9; margin-left: 10px; margin-right: 10px; }";
    // setStyleSheet(menuQss);

    ui->volumnPlus->setAutoRepeat(true);
    ui->volumnMinus->setAutoRepeat(true);
    ui->volumnSlider->setStyle(QStyleFactory::create("fusion"));
    ui->volumnSlider->setPageStep(0);

    mSpeakerActions.clear();
    QMenu* spMenu = new QMenu(this);
    spMenu->setStyleSheet("QMenu { background-color: #484C54; color: #E8E8E8; border: 1px, solid, #646870; }"
                          "QMenu::right-arrow { width: 24px; height: 24px; image: url(:/Image/icn_chevright.png); }"
                          "QMenu::item { background-color: #484C54; color: #E8E8E8; height: 24px; padding-left: 6px; padding-right: 24px; font: 12px 'Segoe UI'; }"
                          "QMenu::item:disabled { background-color: #4D484C54; color: #4DE8E8E8; }"
                          "QMenu::item:selected { background-color: #646870; }");

    mSpeakerActions.insert(SpeakerEnum::SP_DEFAULT, spMenu->addAction("---", [&](){setSp(SpeakerEnum::SP_DEFAULT);}));

    QMenu* frontMenu = spMenu->addMenu("Front");
    QMenu* flMenu = frontMenu->addMenu("Left");
    mSpeakerActions.insert(SpeakerEnum::FL_TWEETER, flMenu->addAction("Tweeter",                    [&](){setSp(SpeakerEnum::FL_TWEETER);}));
    mSpeakerActions.insert(SpeakerEnum::FL_MIDRANGE, flMenu->addAction("Midrange",                  [&](){setSp(SpeakerEnum::FL_MIDRANGE);}));
    mSpeakerActions.insert(SpeakerEnum::FL_WOOFER, flMenu->addAction("Woofer",                      [&](){setSp(SpeakerEnum::FL_WOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::FL_M_T, flMenu->addAction("Midrange Tweeter",               [&](){setSp(SpeakerEnum::FL_M_T);}));
    mSpeakerActions.insert(SpeakerEnum::FL_M_WF, flMenu->addAction("Midrange Woofer",               [&](){setSp(SpeakerEnum::FL_M_WF);}));
    mSpeakerActions.insert(SpeakerEnum::FL_FULL, flMenu->addAction("Full",                          [&](){setSp(SpeakerEnum::FL_FULL);}));
    QMenu* frMenu = frontMenu->addMenu("Right");
    mSpeakerActions.insert(SpeakerEnum::FR_TWEETER, frMenu->addAction("Tweeter",                    [&](){setSp(SpeakerEnum::FR_TWEETER);}));
    mSpeakerActions.insert(SpeakerEnum::FR_MIDRANGE, frMenu->addAction("Midrange",                  [&](){setSp(SpeakerEnum::FR_MIDRANGE);}));
    mSpeakerActions.insert(SpeakerEnum::FR_WOOFER, frMenu->addAction("Woofer",                      [&](){setSp(SpeakerEnum::FR_WOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::FR_M_T, frMenu->addAction("Midrange Tweeter",               [&](){setSp(SpeakerEnum::FR_M_T);}));
    mSpeakerActions.insert(SpeakerEnum::FR_M_WF, frMenu->addAction("Midrange Woofer",               [&](){setSp(SpeakerEnum::FR_M_WF);}));
    mSpeakerActions.insert(SpeakerEnum::FR_FULL, frMenu->addAction("Full",                          [&](){setSp(SpeakerEnum::FR_FULL);}));

    QMenu* rearMenu = spMenu->addMenu("Rear");
    QMenu* rlMenu = rearMenu->addMenu("Left");
    mSpeakerActions.insert(SpeakerEnum::RL_TWEETER, rlMenu->addAction("Tweeter",                    [&](){setSp(SpeakerEnum::RL_TWEETER);}));
    mSpeakerActions.insert(SpeakerEnum::RL_MIDRANGE, rlMenu->addAction("Midrange",                  [&](){setSp(SpeakerEnum::RL_MIDRANGE);}));
    mSpeakerActions.insert(SpeakerEnum::RL_WOOFER, rlMenu->addAction("Woofer",                      [&](){setSp(SpeakerEnum::RL_WOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::RL_M_WF, rlMenu->addAction("Midrange Woofer",               [&](){setSp(SpeakerEnum::RL_M_WF);}));
    mSpeakerActions.insert(SpeakerEnum::RL_FULL, rlMenu->addAction("Full",                          [&](){setSp(SpeakerEnum::RL_FULL);}));
    QMenu* rrMenu = rearMenu->addMenu("Right");
    mSpeakerActions.insert(SpeakerEnum::RR_TWEETER, rrMenu->addAction("Tweeter",                    [&](){setSp(SpeakerEnum::RR_TWEETER);}));
    mSpeakerActions.insert(SpeakerEnum::RR_MIDRANGE, rrMenu->addAction("Midrange",                  [&](){setSp(SpeakerEnum::RR_MIDRANGE);}));
    mSpeakerActions.insert(SpeakerEnum::RR_WOOFER, rrMenu->addAction("Woofer",                      [&](){setSp(SpeakerEnum::RR_WOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::RR_M_WF, rrMenu->addAction("Midrange Woofer",               [&](){setSp(SpeakerEnum::RR_M_WF);}));
    mSpeakerActions.insert(SpeakerEnum::RR_FULL, rrMenu->addAction("Full",                          [&](){setSp(SpeakerEnum::RR_FULL);}));

    QMenu* centerMenu = spMenu->addMenu("Center");
    mSpeakerActions.insert(SpeakerEnum::C_TWEETER, centerMenu->addAction("Tweeter",                 [&](){setSp(SpeakerEnum::C_TWEETER);}));
    mSpeakerActions.insert(SpeakerEnum::C_M_WF, centerMenu->addAction("Midrange Woofer",            [&](){setSp(SpeakerEnum::C_M_WF);}));
    mSpeakerActions.insert(SpeakerEnum::C_FULL, centerMenu->addAction("Full",                       [&](){setSp(SpeakerEnum::C_FULL);}));

    QMenu* subwooferMenu = spMenu->addMenu("Subwoofer");
    mSpeakerActions.insert(SpeakerEnum::L_SUBWOOFER, subwooferMenu->addAction("L-Subwoofer",        [&](){setSp(SpeakerEnum::L_SUBWOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::R_SUBWOOFER, subwooferMenu->addAction("R-Subwoofer",        [&](){setSp(SpeakerEnum::R_SUBWOOFER);}));
    mSpeakerActions.insert(SpeakerEnum::SUBWOOFER, subwooferMenu->addAction("Subwoofer",            [&](){setSp(SpeakerEnum::SUBWOOFER);}));

    QMenu* userConfigMenu = spMenu->addMenu("User config");
    mSpeakerActions.insert(SpeakerEnum::USER_CONFIG_1, userConfigMenu->addAction("User config-1",   [&](){setSp(SpeakerEnum::USER_CONFIG_1);}));
    mSpeakerActions.insert(SpeakerEnum::USER_CONFIG_2, userConfigMenu->addAction("User config-2",   [&](){setSp(SpeakerEnum::USER_CONFIG_2);}));

    ui->sp->setMenu(spMenu);

    connect(ui->mute, &QPushButton::clicked, [&](bool isMuted) {
        setMuted(isMuted);
    });

    connect(ui->volumn, &QDoubleSpinBox::valueChanged, [&]() {
        if(mVolumn10 != qRound((ui->volumn->value() + 60) * 10))
        {
            setVolumn10(qRound((ui->volumn->value() + 60) * 10));

            qInfo("volumn valueChanged, mVolumn10: %d", mVolumn10);
        }
    });

    connect(ui->volumnPlus, &QPushButton::clicked, [&] {
        int volumn = ui->volumnSlider->value() + ui->volumnSlider->singleStep();
        if(volumn <= ui->volumnSlider->maximum())
        {
            setVolumn10(volumn + 600);

            qInfo("volumnPlus clicked, mVolumn10: %d", mVolumn10);
        }
    });

    connect(ui->volumnMinus, &QPushButton::clicked, [&] {
        int volumn = ui->volumnSlider->value() - ui->volumnSlider->singleStep();
        if(volumn >= ui->volumnSlider->minimum())
        {
            setVolumn10(volumn + 600);

            qInfo("volumnMinus clicked, mVolumn10: %d", mVolumn10);
        }
    });

    connect(ui->volumnSlider, &QSlider::valueChanged, [&](int value) {
        qInfo("volumnSlider valueChanged, value: %d", value);
        int volumn = value + 600;
        if(mVolumn10 != volumn)
        {
            setVolumn10(volumn);

            qInfo("volumnSlider valueChanged, mVolumn10: %d", mVolumn10);
        }
    });

    connect(ui->phase, &QPushButton::clicked, [&](bool isChecked) {
        setReversed(isChecked);
    });

    connect(ui->link, &QPushButton::clicked, [&] {
        if(-2 < mLinkChannel)
        {
            emit linkBtnClicked(mChannel, mLinkChannel);
        }
    });

}

ChannelItem::~ChannelItem()
{
    delete ui;
}

int ChannelItem::channel()
{
    return mChannel;
}

bool ChannelItem::highPassEnabled()
{
    return mHighPassEnabled;
}

void ChannelItem::setHighPassEnabled(bool isEnabled)
{
    if(mHighPassEnabled != isEnabled)
    {
        ui->highPass->setEnabled(isEnabled);
        mHighPassEnabled = isEnabled;
        emit highPassEnabledChanged(isEnabled);
    }
}

bool ChannelItem::lowPassEnabled()
{
    return mLowPassEnabled;
}

void ChannelItem::setLowPassEnabled(bool isEnabled)
{
    if(mLowPassEnabled != isEnabled)
    {
        ui->lowPass->setEnabled(isEnabled);
        mLowPassEnabled = isEnabled;
        emit lowPassEnabledChanged(isEnabled);
    }
}

bool ChannelItem::isMuted()
{
    return mIsMuted;
}

void ChannelItem::setMuted(bool isMuted)
{
    if(mIsMuted != isMuted)
    {
        ui->mute->setChecked(isMuted);
        mIsMuted = isMuted;
        emit isMutedChanged(isMuted);
    }
}

SpeakerEnum ChannelItem::sp()
{
    return mSp;
}

void ChannelItem::setSp(SpeakerEnum value)
{
    if(mSp != value)
    {
        ui->sp->setText(CommonController::getInstance().getSpeakerStr(static_cast<uint16_t>(value)));
        mSp = value;
        emit spChanged(value);
    }
}

int ChannelItem::volumn10()
{
    return mVolumn10;
}

void ChannelItem::setVolumn10(int value)
{
    if(mVolumn10 != value)
    {
        mVolumn10 = value;
        ui->volumn->setValue(static_cast<double>(value - 600) / 10);
        ui->volumnSlider->setValue(value - 600);
        emit volumn10Changed(value);
    }
}

bool ChannelItem::isReversed()
{
    return mIsReversed;
}

void ChannelItem::setReversed(bool isReversed)
{
    if(mIsReversed != isReversed)
    {
        mIsReversed = isReversed;
        ui->phase->setChecked(isReversed);
        emit isReversedChanged(isReversed);
    }
}

int ChannelItem::linkChannel()
{
    return mLinkChannel;
}

void ChannelItem::setLinkChannel(int channel)
{
    if(mLinkChannel != channel)
    {
        QString normalQss = "QPushButton{ background-color: #484C54; border: 1px solid #8E8E8E; border-radius: 2px; font-size: 12px; color: #FFFFFF;"
                            "background-image: url(:/Image/link.png); background-position: left center; background-repeat: no-repeat; image: url(:/Image/icn_chblk_normal_arrow.png); }";
        QString linkedQss = "QPushButton{ background-color: #484C54; border: 1px solid #8E8E8E; border-radius: 2px; font-size: 12px; color: #FFFFFF;"
                            "background-image: url(:/Image/link.png); background-position: left center; background-repeat: no-repeat; image: url(:/Image/icn_chblk_link_arrow.png); }";
        QString linkQss = "QPushButton{ background-color: #484C54; border: 1px solid #8E8E8E; border-radius: 2px; font-size: 12px; color: #FFFFFF;"
                          "background-image: url(:/Image/link.png); background-position: left center; background-repeat: no-repeat; }";
        mLinkChannel = channel;
        ui->link->setText((0 > mLinkChannel) ? "" : ("CH" + QString::number(mLinkChannel + 1)));
        ui->link->setStyleSheet((-1 == mLinkChannel) ? normalQss : (-2 == mLinkChannel) ? linkedQss : linkQss);
    }
}

void ChannelItem::setSpeakerEnabled(SpeakerEnum speaker, bool isEnabled)
{
    mSpeakerActions[speaker]->setEnabled(isEnabled);
}

void ChannelItem::setMaskVisible(bool isVisible)
{
    ui->maskItem->setVisible(isVisible);
}
