import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShape"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Shapes/Shape 1.0",
            "QtQuick.Shapes/Shape 1.11",
            "QtQuick.Shapes/Shape 2.0",
            "QtQuick.Shapes/Shape 2.1",
            "QtQuick.Shapes/Shape 2.4",
            "QtQuick.Shapes/Shape 2.7",
            "QtQuick.Shapes/Shape 2.11",
            "QtQuick.Shapes/Shape 6.0",
            "QtQuick.Shapes/Shape 6.3",
            "QtQuick.Shapes/Shape 6.6",
            "QtQuick.Shapes/Shape 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            267,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
        Enum {
            name: "RendererType"
            values: [
                "UnknownRenderer",
                "GeometryRenderer",
                "NvprRenderer",
                "SoftwareRenderer",
                "CurveRenderer"
            ]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Processing"]
        }
        Enum {
            name: "ContainsMode"
            values: ["BoundingRectContains", "FillContains"]
        }
        Enum {
            name: "FillMode"
            values: [
                "NoResize",
                "PreserveAspectFit",
                "PreserveAspectCrop",
                "Stretch"
            ]
        }
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Property {
            name: "rendererType"
            type: "RendererType"
            read: "rendererType"
            notify: "rendererChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 1
        }
        Property {
            name: "vendorExtensionsEnabled"
            type: "bool"
            read: "vendorExtensionsEnabled"
            write: "setVendorExtensionsEnabled"
            notify: "vendorExtensionsEnabledChanged"
            index: 2
        }
        Property {
            name: "preferredRendererType"
            revision: 1542
            type: "RendererType"
            read: "preferredRendererType"
            write: "setPreferredRendererType"
            notify: "preferredRendererTypeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "containsMode"
            revision: 267
            type: "ContainsMode"
            read: "containsMode"
            write: "setContainsMode"
            notify: "containsModeChanged"
            index: 5
        }
        Property {
            name: "boundingRect"
            revision: 1542
            type: "QRectF"
            read: "boundingRect"
            notify: "boundingRectChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "fillMode"
            revision: 1543
            type: "FillMode"
            read: "fillMode"
            write: "setFillMode"
            notify: "fillModeChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "horizontalAlignment"
            revision: 1543
            type: "HAlignment"
            read: "horizontalAlignment"
            write: "setHorizontalAlignment"
            notify: "horizontalAlignmentChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "verticalAlignment"
            revision: 1543
            type: "VAlignment"
            read: "verticalAlignment"
            write: "setVerticalAlignment"
            notify: "verticalAlignmentChanged"
            index: 9
            isFinal: true
        }
        Property { name: "data"; type: "QObject"; isList: true; read: "data"; index: 10; isReadonly: true }
        Signal { name: "rendererChanged" }
        Signal { name: "asynchronousChanged" }
        Signal { name: "vendorExtensionsEnabledChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "preferredRendererTypeChanged"; revision: 1542 }
        Signal { name: "boundingRectChanged"; revision: 1542 }
        Signal { name: "containsModeChanged"; revision: 267 }
        Signal { name: "fillModeChanged"; revision: 1543 }
        Signal { name: "horizontalAlignmentChanged"; revision: 1543 }
        Signal { name: "verticalAlignmentChanged"; revision: 1543 }
        Method { name: "_q_shapePathChanged" }
    }
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShapeConicalGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/ConicalGradient 1.0",
            "QtQuick.Shapes/ConicalGradient 2.0",
            "QtQuick.Shapes/ConicalGradient 2.12",
            "QtQuick.Shapes/ConicalGradient 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 524, 1536]
        Property {
            name: "centerX"
            type: "double"
            read: "centerX"
            write: "setCenterX"
            notify: "centerXChanged"
            index: 0
        }
        Property {
            name: "centerY"
            type: "double"
            read: "centerY"
            write: "setCenterY"
            notify: "centerYChanged"
            index: 1
        }
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            write: "setAngle"
            notify: "angleChanged"
            index: 2
        }
        Signal { name: "centerXChanged" }
        Signal { name: "centerYChanged" }
        Signal { name: "angleChanged" }
    }
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShapeGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QQuickGradient"
        exports: [
            "QtQuick.Shapes/ShapeGradient 1.0",
            "QtQuick.Shapes/ShapeGradient 2.0",
            "QtQuick.Shapes/ShapeGradient 2.12",
            "QtQuick.Shapes/ShapeGradient 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 512, 524, 1536]
        Enum {
            name: "SpreadMode"
            values: ["PadSpread", "ReflectSpread", "RepeatSpread"]
        }
        Property {
            name: "spread"
            type: "SpreadMode"
            read: "spread"
            write: "setSpread"
            notify: "spreadChanged"
            index: 0
        }
        Signal { name: "spreadChanged" }
    }
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShapeLinearGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/LinearGradient 1.0",
            "QtQuick.Shapes/LinearGradient 2.0",
            "QtQuick.Shapes/LinearGradient 2.12",
            "QtQuick.Shapes/LinearGradient 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 524, 1536]
        Property { name: "x1"; type: "double"; read: "x1"; write: "setX1"; notify: "x1Changed"; index: 0 }
        Property { name: "y1"; type: "double"; read: "y1"; write: "setY1"; notify: "y1Changed"; index: 1 }
        Property { name: "x2"; type: "double"; read: "x2"; write: "setX2"; notify: "x2Changed"; index: 2 }
        Property { name: "y2"; type: "double"; read: "y2"; write: "setY2"; notify: "y2Changed"; index: 3 }
        Signal { name: "x1Changed" }
        Signal { name: "y1Changed" }
        Signal { name: "x2Changed" }
        Signal { name: "y2Changed" }
    }
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShapePath"
        accessSemantics: "reference"
        defaultProperty: "pathElements"
        prototype: "QQuickPath"
        exports: [
            "QtQuick.Shapes/ShapePath 1.0",
            "QtQuick.Shapes/ShapePath 1.14",
            "QtQuick.Shapes/ShapePath 2.0",
            "QtQuick.Shapes/ShapePath 2.14",
            "QtQuick.Shapes/ShapePath 6.0",
            "QtQuick.Shapes/ShapePath 6.6",
            "QtQuick.Shapes/ShapePath 6.7",
            "QtQuick.Shapes/ShapePath 6.8",
            "QtQuick.Shapes/ShapePath 6.9"
        ]
        exportMetaObjectRevisions: [
            256,
            270,
            512,
            526,
            1536,
            1542,
            1543,
            1544,
            1545
        ]
        Enum {
            name: "FillRule"
            values: ["OddEvenFill", "WindingFill"]
        }
        Enum {
            name: "JoinStyle"
            values: ["MiterJoin", "BevelJoin", "RoundJoin"]
        }
        Enum {
            name: "CapStyle"
            values: ["FlatCap", "SquareCap", "RoundCap"]
        }
        Enum {
            name: "StrokeStyle"
            values: ["SolidLine", "DashLine"]
        }
        Enum {
            name: "PathHints"
            alias: "PathHint"
            isFlag: true
            values: [
                "PathLinear",
                "PathQuadratic",
                "PathConvex",
                "PathFillOnRight",
                "PathSolid",
                "PathNonIntersecting",
                "PathNonOverlappingControlPointTriangles"
            ]
        }
        Property {
            name: "strokeColor"
            type: "QColor"
            read: "strokeColor"
            write: "setStrokeColor"
            notify: "strokeColorChanged"
            index: 0
        }
        Property {
            name: "strokeWidth"
            type: "double"
            read: "strokeWidth"
            write: "setStrokeWidth"
            notify: "strokeWidthChanged"
            index: 1
        }
        Property {
            name: "fillColor"
            type: "QColor"
            read: "fillColor"
            write: "setFillColor"
            notify: "fillColorChanged"
            index: 2
        }
        Property {
            name: "fillRule"
            type: "FillRule"
            read: "fillRule"
            write: "setFillRule"
            notify: "fillRuleChanged"
            index: 3
        }
        Property {
            name: "joinStyle"
            type: "JoinStyle"
            read: "joinStyle"
            write: "setJoinStyle"
            notify: "joinStyleChanged"
            index: 4
        }
        Property {
            name: "miterLimit"
            type: "int"
            read: "miterLimit"
            write: "setMiterLimit"
            notify: "miterLimitChanged"
            index: 5
        }
        Property {
            name: "capStyle"
            type: "CapStyle"
            read: "capStyle"
            write: "setCapStyle"
            notify: "capStyleChanged"
            index: 6
        }
        Property {
            name: "strokeStyle"
            type: "StrokeStyle"
            read: "strokeStyle"
            write: "setStrokeStyle"
            notify: "strokeStyleChanged"
            index: 7
        }
        Property {
            name: "dashOffset"
            type: "double"
            read: "dashOffset"
            write: "setDashOffset"
            notify: "dashOffsetChanged"
            index: 8
        }
        Property {
            name: "dashPattern"
            type: "double"
            isList: true
            read: "dashPattern"
            write: "setDashPattern"
            notify: "dashPatternChanged"
            index: 9
        }
        Property {
            name: "fillGradient"
            type: "QQuickShapeGradient"
            isPointer: true
            read: "fillGradient"
            write: "setFillGradient"
            reset: "resetFillGradient"
            index: 10
        }
        Property {
            name: "scale"
            revision: 270
            type: "QSizeF"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 11
        }
        Property {
            name: "pathHints"
            revision: 1543
            type: "PathHints"
            read: "pathHints"
            write: "setPathHints"
            notify: "pathHintsChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "fillTransform"
            revision: 1544
            type: "QMatrix4x4"
            read: "fillTransform"
            write: "setFillTransform"
            notify: "fillTransformChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "fillItem"
            revision: 1544
            type: "QQuickItem"
            isPointer: true
            read: "fillItem"
            write: "setFillItem"
            notify: "fillItemChanged"
            index: 14
            isFinal: true
        }
        Signal { name: "shapePathChanged" }
        Signal { name: "strokeColorChanged" }
        Signal { name: "strokeWidthChanged" }
        Signal { name: "fillColorChanged" }
        Signal { name: "fillRuleChanged" }
        Signal { name: "joinStyleChanged" }
        Signal { name: "miterLimitChanged" }
        Signal { name: "capStyleChanged" }
        Signal { name: "strokeStyleChanged" }
        Signal { name: "dashOffsetChanged" }
        Signal { name: "dashPatternChanged" }
        Signal { name: "pathHintsChanged"; revision: 1543 }
        Signal { name: "fillTransformChanged"; revision: 1544 }
        Signal { name: "fillItemChanged"; revision: 1544 }
        Method { name: "_q_fillGradientChanged" }
        Method { name: "_q_fillItemDestroyed" }
    }
    Component {
        file: "private/qquickshape_p.h"
        name: "QQuickShapeRadialGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QQuickShapeGradient"
        exports: [
            "QtQuick.Shapes/RadialGradient 1.0",
            "QtQuick.Shapes/RadialGradient 2.0",
            "QtQuick.Shapes/RadialGradient 2.12",
            "QtQuick.Shapes/RadialGradient 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 524, 1536]
        Property {
            name: "centerX"
            type: "double"
            read: "centerX"
            write: "setCenterX"
            notify: "centerXChanged"
            index: 0
        }
        Property {
            name: "centerY"
            type: "double"
            read: "centerY"
            write: "setCenterY"
            notify: "centerYChanged"
            index: 1
        }
        Property {
            name: "centerRadius"
            type: "double"
            read: "centerRadius"
            write: "setCenterRadius"
            notify: "centerRadiusChanged"
            index: 2
        }
        Property {
            name: "focalX"
            type: "double"
            read: "focalX"
            write: "setFocalX"
            notify: "focalXChanged"
            index: 3
        }
        Property {
            name: "focalY"
            type: "double"
            read: "focalY"
            write: "setFocalY"
            notify: "focalYChanged"
            index: 4
        }
        Property {
            name: "focalRadius"
            type: "double"
            read: "focalRadius"
            write: "setFocalRadius"
            notify: "focalRadiusChanged"
            index: 5
        }
        Signal { name: "centerXChanged" }
        Signal { name: "centerYChanged" }
        Signal { name: "focalXChanged" }
        Signal { name: "focalYChanged" }
        Signal { name: "centerRadiusChanged" }
        Signal { name: "focalRadiusChanged" }
    }
}
