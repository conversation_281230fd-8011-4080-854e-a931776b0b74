#ifndef COMMONCONTROLLER_H
#define COMMONCONTROLLER_H

#include <QObject>
#include <QVariant>
#include <QQmlPropertyMap>
#include <QFile>
#include <QtNetwork/QNetworkAccessManager>
#include <QtNetwork/QNetworkReply>

#include "dataCenter/DataCenterController.h"
#include "version.h"

class CommonController : public QObject
{
    Q_OBJECT
    // Q_PROPERTY(int masterGain READ masterGain WRITE setMasterGain NOTIFY masterGainChanged)

private:
    explicit CommonController(QObject *parent = nullptr);

public:
    static CommonController& getInstance()
    {
        static CommonController instance;
        return instance;
    }

    const QMap<SpeakerEnum, QString> spStrMap = {
        {SpeakerEnum::SP_DEFAULT, "---"},

        {SpeakerEnum::FL_TWEETER,   "FL_Tweeter"},
        {SpeakerEnum::FL_MIDRANGE,  "FL_Midrange"},
        {SpeakerEnum::FL_WOOFER,    "FL_Woofer"},
        {SpeakerEnum::FL_M_T,       "FL_M&&T"},
        {SpeakerEnum::FL_M_WF,      "FL_M&&WF"},
        {SpeakerEnum::FL_FULL,      "FL_Full"},

        {SpeakerEnum::FR_TWEETER,   "FR_Tweeter"},
        {SpeakerEnum::FR_MIDRANGE,  "FR_Midrange"},
        {SpeakerEnum::FR_WOOFER,    "FR_Woofer"},
        {SpeakerEnum::FR_M_T,       "FR_M&&T"},
        {SpeakerEnum::FR_M_WF,      "FR_M&&WF"},
        {SpeakerEnum::FR_FULL,      "FR_Full"},

        {SpeakerEnum::RL_TWEETER,   "RL_Tweeter"},
        {SpeakerEnum::RL_MIDRANGE,  "RL_Midrange"},
        {SpeakerEnum::RL_WOOFER,    "RL_Woofer"},
        {SpeakerEnum::RL_M_WF,      "RL_M&&WF"},
        {SpeakerEnum::RL_FULL,      "RL_Full"},

        {SpeakerEnum::RR_TWEETER,   "RR_Tweeter"},
        {SpeakerEnum::RR_MIDRANGE,  "RR_Midrange"},
        {SpeakerEnum::RR_WOOFER,    "RR_Woofer"},
        {SpeakerEnum::RR_M_WF,      "RR_M&&WF"},
        {SpeakerEnum::RR_FULL,      "RR_Full"},

        {SpeakerEnum::C_TWEETER,    "C_Tweeter"},
        {SpeakerEnum::C_M_WF,       "C_M&&WF"},
        {SpeakerEnum::C_FULL,       "C_Full"},

        {SpeakerEnum::L_SUBWOOFER,  "L-Subwoofer"},
        {SpeakerEnum::R_SUBWOOFER,  "R-Subwoofer"},
        {SpeakerEnum::SUBWOOFER,    "Subwoofer"},

        {SpeakerEnum::USER_CONFIG_1, "User config-1"},
        {SpeakerEnum::USER_CONFIG_2, "User config-2"}
    };

    const QMap<SpeakerEnum, int> spIdMap = {
        {SpeakerEnum::SP_DEFAULT, -1},

        {SpeakerEnum::FL_TWEETER,   0},
        {SpeakerEnum::FL_MIDRANGE,  1},
        {SpeakerEnum::FL_WOOFER,    2},
        {SpeakerEnum::FL_M_T,       0},
        {SpeakerEnum::FL_M_WF,      2},
        {SpeakerEnum::FL_FULL,      1},

        {SpeakerEnum::FR_TWEETER,   6},
        {SpeakerEnum::FR_MIDRANGE,  7},
        {SpeakerEnum::FR_WOOFER,    8},
        {SpeakerEnum::FR_M_T,       6},
        {SpeakerEnum::FR_M_WF,      8},
        {SpeakerEnum::FR_FULL,      7},

        {SpeakerEnum::RL_TWEETER,   3},
        {SpeakerEnum::RL_MIDRANGE,  4},
        {SpeakerEnum::RL_WOOFER,    5},
        {SpeakerEnum::RL_M_WF,      5},
        {SpeakerEnum::RL_FULL,      4},

        {SpeakerEnum::RR_TWEETER,   9},
        {SpeakerEnum::RR_MIDRANGE,  10},
        {SpeakerEnum::RR_WOOFER,    11},
        {SpeakerEnum::RR_M_WF,      11},
        {SpeakerEnum::RR_FULL,      10},

        {SpeakerEnum::C_TWEETER,    16},
        {SpeakerEnum::C_M_WF,       15},
        {SpeakerEnum::C_FULL,       15},

        {SpeakerEnum::L_SUBWOOFER,  12},
        {SpeakerEnum::R_SUBWOOFER,  14},
        {SpeakerEnum::SUBWOOFER,    13},

        {SpeakerEnum::USER_CONFIG_1, 17},
        {SpeakerEnum::USER_CONFIG_2, 18}
    };

    //判断key是否可选择：当前值和value做 & 操作后等于0，则表示对应value的key可选择。
    //选择key后，将当前值+value。
    //取消key后，将当前值-value。
    const QMap<SpeakerEnum, uint32_t> spChosenBitsMap = {
        {SpeakerEnum::SP_DEFAULT,       0},

        {SpeakerEnum::FL_TWEETER,       0x00000001},
        {SpeakerEnum::FL_MIDRANGE,      0x00000002},
        {SpeakerEnum::FL_WOOFER,        0x00000004},
        {SpeakerEnum::FL_M_T,           0x00000003},
        {SpeakerEnum::FL_M_WF,          0x00000006},
        {SpeakerEnum::FL_FULL,          0x00000007},

        {SpeakerEnum::FR_TWEETER,       0x00000008},
        {SpeakerEnum::FR_MIDRANGE,      0x00000010},
        {SpeakerEnum::FR_WOOFER,        0x00000020},
        {SpeakerEnum::FR_M_T,           0x00000018},
        {SpeakerEnum::FR_M_WF,          0x00000030},
        {SpeakerEnum::FR_FULL,          0x00000038},

        {SpeakerEnum::RL_TWEETER,       0x00000040},
        {SpeakerEnum::RL_MIDRANGE,      0x00000080},
        {SpeakerEnum::RL_WOOFER,        0x00000100},
        {SpeakerEnum::RL_M_WF,          0x00000180},
        {SpeakerEnum::RL_FULL,          0x000001C0},

        {SpeakerEnum::RR_TWEETER,       0x00000200},
        {SpeakerEnum::RR_MIDRANGE,      0x00000400},
        {SpeakerEnum::RR_WOOFER,        0x00000800},
        {SpeakerEnum::RR_M_WF,          0x00000C00},
        {SpeakerEnum::RR_FULL,          0x00000E00},

        {SpeakerEnum::C_TWEETER,        0x00001000},
        {SpeakerEnum::C_M_WF,           0x00002000},
        {SpeakerEnum::C_FULL,           0x00003000},

        {SpeakerEnum::L_SUBWOOFER,      0x00004000},
        {SpeakerEnum::R_SUBWOOFER,      0x00008000},
        {SpeakerEnum::SUBWOOFER,        0x0000C000},

        {SpeakerEnum::USER_CONFIG_1,    0x00010000},

        {SpeakerEnum::USER_CONFIG_2,    0x00020000}
    };

    // EqLinkChannelStruct mEqLinkChannels[OUTPUT_CHANNEL_MAX];
    // // -1为未链接；大于-1为链接channel； -2为被链接; channelStates为被链接时各个channel是否为链接者
    // struct EqLinkChannelStruct
    // {
    //     int linkChannel = -1;
    //     bool channelStates[10] = {false};
    // };

    Q_INVOKABLE void reconnectDevice();

    Q_INVOKABLE QString getSpeakerStr(uint16_t type);
    Q_INVOKABLE int getSpeakerId(uint16_t type);
    Q_INVOKABLE QMap<SpeakerEnum, bool> getSpeakerMap();
    Q_INVOKABLE void setHpLpFreqBySpeakerChanged(int channel, uint16_t speaker);

    // Version information methods
    Q_INVOKABLE QString getVersionString() const;
    Q_INVOKABLE QString getVersionFull() const;
    Q_INVOKABLE QString getVersionTag() const;
    Q_INVOKABLE QString getGitCommit() const;
    Q_INVOKABLE QString getGitBranch() const;
    Q_INVOKABLE QString getBuildTimestamp() const;
    Q_INVOKABLE int getVersionMajor() const;
    Q_INVOKABLE int getVersionMinor() const;
    Q_INVOKABLE int getVersionPatch() const;
    Q_INVOKABLE bool isVersionAtLeast(int major, int minor, int patch) const;

    Q_INVOKABLE void setEqLink(int channel, int linkChannel);
    Q_INVOKABLE int getEqLinkStatus(int channel);

    Q_INVOKABLE void setSelectedChannel(uint8_t channel);

    Q_INVOKABLE void setHighRcaEnable(uint8_t type);
    Q_INVOKABLE void setAuxEnable(uint8_t enable);
    Q_INVOKABLE void setBtEnable(uint8_t enable);
    Q_INVOKABLE void setSpdifEnable(uint8_t enable);
    Q_INVOKABLE void setUsbEnable(uint8_t enable);

    Q_INVOKABLE void setOutputChannelType(uint8_t channel, uint16_t type);
    Q_INVOKABLE void setOutputChannelGain(uint8_t channel, uint16_t gain);
    Q_INVOKABLE void setOutputChannelPhase(uint8_t channel, uint8_t phase);
    Q_INVOKABLE void setOutputChannelMute(uint8_t channel, uint8_t mute);
    Q_INVOKABLE void setOutputChannelDelay(uint8_t channel, uint16_t delay);
    Q_INVOKABLE void setOutputChannelDelayGroup(uint8_t channel, uint8_t group);
    Q_INVOKABLE void setOutputChannelEqSet(uint8_t channel, uint8_t set);
    Q_INVOKABLE void setOutputChannelEqType(uint8_t channel, uint8_t type);
    Q_INVOKABLE void setOutputChannelLinkType(uint8_t channel, uint8_t type);
    Q_INVOKABLE void setOutputChannelLinkChannel(uint8_t channel, uint8_t linkChannel);
    Q_INVOKABLE void setOutputChannelLink(uint8_t channel, uint8_t type, uint8_t linkChannel);
    Q_INVOKABLE void setOutputChannelHpFreq(uint8_t channel, uint16_t freq);
    Q_INVOKABLE void setOutputChannelHpSlope(uint8_t channel, uint8_t slope);
    Q_INVOKABLE void setOutputChannelHpType(uint8_t channel, uint8_t type);
    Q_INVOKABLE void setOutputChannelHpEnable(uint8_t channel, uint8_t enable);
    Q_INVOKABLE void setOutputChannelLpFreq(uint8_t channel, uint16_t freq);
    Q_INVOKABLE void setOutputChannelLpSlope(uint8_t channel, uint8_t slope);
    Q_INVOKABLE void setOutputChannelLpType(uint8_t channel, uint8_t type);
    Q_INVOKABLE void setOutputChannelLpEnable(uint8_t channel, uint8_t enable);
    Q_INVOKABLE void setOutputChannelHpLpLinkCopy(uint8_t channel, uint8_t linkChannel);

    Q_INVOKABLE void setMasterGain(uint16_t gain);
    Q_INVOKABLE void setMasterMute(uint8_t mute);
    Q_INVOKABLE void setDspMute(uint8_t mute);
    Q_INVOKABLE void setDspGain(uint8_t gain);
    Q_INVOKABLE void setBassMute(uint8_t mute);
    Q_INVOKABLE void setBassLevel(uint8_t level);
    Q_INVOKABLE void setSource(uint8_t musicSource);
    Q_INVOKABLE void setMemory(uint8_t memory);
    Q_INVOKABLE void setMix(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t enable, uint8_t gain);
    Q_INVOKABLE void setEqFreq(uint8_t channel, uint8_t band, uint16_t freq);
    Q_INVOKABLE void setEqQvalue(uint8_t channel, uint8_t band, uint16_t qValue);
    Q_INVOKABLE void setEqGain(uint8_t channel, uint8_t band, uint16_t gain);
    Q_INVOKABLE void setEqEnable(uint8_t channel, uint8_t band, uint8_t enable);
    Q_INVOKABLE void setEqType(uint8_t channel, uint8_t band, uint8_t type);
    Q_INVOKABLE void setEqLinkCopy(uint8_t channel, uint8_t linkChannel);
    // Q_INVOKABLE void setEqLink();
    Q_INVOKABLE void setMemoryEnable(uint8_t memory, uint8_t type, uint8_t enable);
    Q_INVOKABLE void setMemoryName(uint8_t memory, uint8_t type, QString name);
    Q_INVOKABLE void setMemoryCopy(uint8_t typeSrc, uint8_t memorySrc, uint8_t typeDes, uint8_t memoryDes, QString name);
    Q_INVOKABLE void setDelayGroup(uint8_t group, int delayDiff);   //delayDiff = targetDelay - currentDelay
    Q_INVOKABLE void setPresetMode(uint8_t mode);
    Q_INVOKABLE void setPresetLevel(uint8_t level);
    Q_INVOKABLE void setDisconnectBt(); // send 00: disconnect->connect

    Q_INVOKABLE void setNaviMixEnable(uint8_t enable);
    Q_INVOKABLE void setNaviSensitivity(uint8_t value);
    Q_INVOKABLE void setNaviAttenuation(uint8_t value);
    Q_INVOKABLE void setNaviDuration(uint8_t value);
    Q_INVOKABLE void setExternalAttenuation(uint8_t value);
    Q_INVOKABLE void setExternalPolarity(uint8_t value);
    Q_INVOKABLE void setExternalDspAttenuation(uint8_t value);
    Q_INVOKABLE void setDelayUnit(uint8_t value);
    Q_INVOKABLE void setHighGain(uint8_t value);
    Q_INVOKABLE void setRcaGain(uint8_t value);
    Q_INVOKABLE void setAuxGain(uint8_t value);
    Q_INVOKABLE void setBtGain(uint8_t value);
    Q_INVOKABLE void setSpdifGain(uint8_t value);
    Q_INVOKABLE void setUsbGain(uint8_t value);

    Q_INVOKABLE void setResetFactory();

    QString saveFile();
    void setSaveFile(QString file);

    int savePackageCount();
    void setSavePackageCount(int count);

    void querySaveData();
    void sendLoadData(const QByteArray &data);

    void upgradeDsp(const QByteArray &data);
    void upgradeRemote(const QByteArray &data);

    Q_INVOKABLE void setRemoteBrightness(uint8_t value);
    Q_INVOKABLE void setRemoteDimmer(uint8_t value);
    Q_INVOKABLE void setRemotePolarity(uint8_t value);
    Q_INVOKABLE void setRemoteDimmerBrightness(uint8_t value);
    Q_INVOKABLE void setRemoteModel(uint8_t value);
    Q_INVOKABLE void setRemoteShortCut(uint8_t id, uint8_t type, uint8_t memory);

private:
    void initData();

signals:
    void channelListClicked(int channel, bool isChecked);
    // void speakerChanged(int channel, uint oldType, uint type);
    void speakerChanged(int channel, int speaker, bool isEnabled);
    void eqLinkPopupShown();
    void openWarningPopup(int warningNum);

    void deviceConnection();
    void initFinish();
    void showToolUpdate(bool isShown);
    void showMainWindow();
    void quitApp();
    void eqLinkChannelChanged(int channel, int linkChannel);

    void uiDataMapValueChanged(const QString &key, const QVariant &value);
    void dataMapValueChanged(const QString &key, const QVariant &value);
    void channelDataMapsValueChanged(int channel, const QString &key, const QVariant &value);
    void mixDataMapsValueChanged(int type, int inputChannel, int outputChannel, const QString &key, const QVariant &value);
    void eqDataMapsValueChanged(int outputChannel, int band, const QString &key, const QVariant &value);
    void memorySettingDataMapsValueChanged(int type, int memory, const QString &key, const QVariant &value);

public:
    QQmlPropertyMap mUiDataMap;
    QQmlPropertyMap mUiEqSaveGainMaps[MEMORY_MAX * MEMORY_TYPE_MAX];
    QQmlPropertyMap mUiEqSaveQMaps[MEMORY_MAX * MEMORY_TYPE_MAX];

    QQmlPropertyMap mDataMap;
    QQmlPropertyMap mChannelDataMaps[OUTPUT_CHANNEL_MAX];
    QQmlPropertyMap mMixHighDataMaps[MIX_HIGH_CHANNEL_MAX][OUTPUT_CHANNEL_MAX];
    QQmlPropertyMap mMixRCADataMaps[MIX_RCA_CHANNEL_MAX][OUTPUT_CHANNEL_MAX];
    QQmlPropertyMap mMixDSPDataMaps[MIX_DSP_CHANNEL_MAX][OUTPUT_CHANNEL_MAX];
    QQmlPropertyMap mEqDataMaps[OUTPUT_CHANNEL_MAX][EQ_BAND_MAX];
    QQmlPropertyMap mMemorySettingDataMaps[MEMORY_TYPE_MAX][MEMORY_MAX];
    QQmlPropertyMap mRemoteShortCutMaps[REMOTE_SHORTCUT_MAX];

    QMap<int, bool> mLineDisplayState;
    // QVector<int> mEqLinkChannels;  // 0为未链接；大于0为链接channel+1； 小于0为被链接-(channel+1)
    struct EqLinkStruct
    {
        int status = -1;  // -1为未链接；大于-1为链接channel； -2为被链接
        bool linkedChannels[OUTPUT_CHANNEL_MAX] = {false};
    };
    EqLinkStruct mEqLinkChannels[OUTPUT_CHANNEL_MAX];

private:
    QTimer* mInitTimer;
    QNetworkAccessManager *mNetManager;
    QString mSaveFile;
    int mSavePackageCount;
    QByteArray mSaveData;
    uint32_t mSpChosenValue;
};

#endif // COMMONCONTROLLER_H
