#include "butterworth_filter.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 斜率dB/oct转阶数（6dB/oct=1阶，12=2阶，...，48=8阶）
static int slope_to_order(int slope) {
    int order = slope / 6;
    if (order < 1) order = 1;
    if (order > 8) order = 8;
    return order;
}

/**
 * @brief 计算巴特沃斯高阶滤波器每个二阶节的系数（双线性变换法）
 * @param order 滤波器阶数
 * @param section 二阶节编号，-1表示计算一阶节系数（用于奇数阶滤波器）
 * @param fs 采样率
 * @param fc 截止频率
 * @param coeffs 输出系数结构体
 * @param type 滤波器类型（低通/高通）
 */
void butterworth_section_coeffs(int order, int section, double fs, double fc, BiquadCoeffs* coeffs, int type) {
    // section==-1时输出一阶节系数（用于奇数阶滤波器）
    if (order % 2 != 0 && section == -1) {
        double K = tan(PI * fc / fs);
        double norm = K + 1.0;
        double b0, b1, a0, a1;
        if (type == FILTER_TYPE_BUTTERWORTH_LP) {
            b0 = K / norm;
            b1 = K / norm;
        } else {
            b0 = 1.0 / norm;
            b1 = -1.0 / norm;
        }
        a0 = 1.0;
        a1 = (K - 1.0) / norm;
        coeffs->b1st[0] = b0;
        coeffs->b1st[1] = b1;
        coeffs->a1st[0] = a0;
        coeffs->a1st[1] = a1;
        CURVE_LOG("[Butterworth-1st] order:%d, fc:%.2f, b: %.8f %.8f, a: %.8f %.8f\n", order, fc, b0, b1, a0, a1);
        return;
    }
    // 标准巴特沃斯极点分布
    double theta = PI * (2.0 * (section + 1) + order - 1) / (2.0 * order);
    double sigma = -cos(theta);
    double omega = sin(theta);
    double K = tan(PI * fc / fs);
    double K2 = K * K;
    double norm, b0, b1, b2, a0, a1, a2;
    if (type == FILTER_TYPE_BUTTERWORTH_LP) {
        norm = 1.0 + 2.0 * sigma * K + K2;
        b0 = K2 / norm;
        b1 = 2.0 * b0;
        b2 = b0;
        a0 = 1.0;
        a1 = 2.0 * (K2 - 1.0) / norm;
        a2 = (1.0 - 2.0 * sigma * K + K2) / norm;
    } else {
        norm = 1.0 + 2.0 * sigma * K + K2;
        // 修正高通分子系数推导，严格按(1 - z^-1)^2型
        b0 = 1.0 / norm;
        b1 = -2.0 / norm;
        b2 = 1.0 / norm;
        a0 = 1.0;
        a1 = 2.0 * (K2 - 1.0) / norm;
        a2 = (1.0 - 2.0 * sigma * K + K2) / norm;
    }
    coeffs->b[0] = b0;
    coeffs->b[1] = b1;
    coeffs->b[2] = b2;
    coeffs->a[0] = a0;
    coeffs->a[1] = a1;
    coeffs->a[2] = a2;
    CURVE_LOG("[Butterworth-section] order:%d, section:%d, theta:%.6f, sigma:%.6f, K:%.6f, norm:%.6f, b: %.8f %.8f %.8f, a: %.8f %.8f %.8f\n", order, section, theta, sigma, K, norm, b0, b1, b2, a0, a1, a2);
}

/**
 * @brief 计算巴特沃斯（Butterworth）滤波器在各频率点的复数响应
 * @param type 滤波器类型（5=低通，6=高通）
 * @param fc 截止频率
 * @param slope_rate 斜率（dB/oct），支持6/12/24/48等
 * @param freq_list 频率点数组
 * @param resp 输出复数响应（累乘）
 *
 * 实现说明：
 * 采用标准多阶二阶节级联，每节极点不同，幅频/相位与专业音频软件一致。
 * 日志输出每阶系数和关键频点响应。
 */
void butterworth_filter_optimized_response(int type, double fc, int slope_rate,
                                      const DoubleArray* freq_list,
                                      double_complex* resp) {
    int order = slope_to_order(slope_rate);
    int sections = order / 2;
    BiquadCoeffs coeffs;
    char logbuf[256];
    // 一阶滤波器
    if (order == 1) {
        butterworth_section_coeffs(order, -1, FS, fc, &coeffs, type);
        calc_single_biquad_resp(freq_list, FS, &coeffs, resp, 1);
        CURVE_LOG("[Butterworth] Response calculation finished, fc=%.2f, order=%d\n", fc, order);
        return;
    }
    // 奇数阶：先叠加一阶节
    if (order % 2 != 0) {
        butterworth_section_coeffs(order, -1, FS, fc, &coeffs, type);
        calc_single_biquad_resp(freq_list, FS, &coeffs, resp, 1);
    }
    // 叠加所有二阶节
    for (int sec = 0; sec < sections; ++sec) {
        butterworth_section_coeffs(order, sec, FS, fc, &coeffs, type);
        snprintf(logbuf, sizeof(logbuf), "[Butterworth] order:%d, section:%d, fc:%.2f", order, sec, fc);
        CURVE_LOG("%s\n", logbuf);
        calc_single_biquad_resp(freq_list, FS, &coeffs, resp, 2);
    }
    CURVE_LOG("[Butterworth] Response calculation finished, fc=%.2f, order=%d\n", fc, order);
}