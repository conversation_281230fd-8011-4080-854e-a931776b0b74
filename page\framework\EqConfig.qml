import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    id: control
    implicitWidth: 1110
    implicitHeight: 234
    color: "#3C4048"
    border.width: 0.5
    border.color: "#5C6068"

    property int channel: dataMap["uiDataMap"]["selectedChannel"]

    property bool peqChecked: (0 === dataMap["channel" + channel + "DataMap"]["eqType"])

    onPeqCheckedChanged: {
        peq.state = peqChecked ? "peq" : "geq"
    }

    Rectangle {
        id: titles
        width: 56
        height: parent.height
        color: "transparent"
        border.width: 0.5
        border.color: "#5C6068"

        Column {
            x: 6
            y: 6
            spacing: 8

            // MyCheckBox {
            //     id: peq
            //     height: 14
            //     text: "PEQ"
            //     checked: true
            // }

            Button {
                id: peq
                width: 44
                height: 14
                text: "PEQ"
                spacing: 5
                font.family: "Segoe UI"
                font.pixelSize: 12
                opacity: enabled ? 1.0 : 0.3
                leftPadding: 0
                state: "peq"

                indicator: Rectangle {
                    implicitWidth: 14
                    implicitHeight: 14
                    x: peq.leftPadding
                    y: parent.height / 2 - height / 2
                    color: "#484C54"
                    border.color: "#646870"
                    border.width: 1

                    Image {
                        id: indicatorImg
                        anchors.centerIn: parent
                        source: "qrc:/Image/checked.png"
                    }
                }

                contentItem: Text {
                    text: peq.text
                    font: peq.font
                    color: "#E8E8E8"
                    verticalAlignment: Text.AlignVCenter
                    leftPadding: peq.indicator.width + peq.spacing
                }

                background: Item {

                }

                states: [
                    State {
                        name: "peq"
                        PropertyChanges {
                            target: indicatorImg
                            visible: true
                        }
                    },
                    State {
                        name: "geq"
                        PropertyChanges {
                            target: indicatorImg
                            visible: false
                        }
                    }
                ]

                onClicked: {
                    if("peq" === state)
                    {
                        commonCtrl.openWarningPopup(2)
                        // state = "geq"
                        // commonCtrl.setOutputChannelEqType(channel, 1)
                    }
                    else
                    {
                        state = "peq"
                        commonCtrl.setOutputChannelEqType(channel, 0)
                    }
                }
            }

            MyCheckBox {
                id: byp
                height: 14
                text: "BYP"
                checked: (0 !== dataMap["uiDataMap"]["channel" + channel + "AllBypassState"])

                signal btnClicked(var checked)

                onClicked: {
                    dataMap["uiDataMap"]["channel" + channel + "AllBypassState"] = checked ? 1 : 0
                    commonCtrl.setOutputChannelEqSet(channel, (checked ? 2 : 1))
                    btnClicked(checked)
                }
            }
        }

        ButtonB {
            id: reset
            x: 6
            y: 52
            width: 44
            height: 20
            text: "RESET"

            onClicked: {
                if(2 === dataMap["channel" + channel + "DataMap"]["linkType"])
                {
                    commonCtrl.openWarningPopup(4)
                }
                else
                {
                    commonCtrl.openWarningPopup(3)
                }
            }
        }

        Column {
            anchors.left: parent.left
            anchors.bottom: parent.bottom
            spacing: 0

            Repeater {
                model: ["Freq", "Gain", "Q", "Type", "Byp"]

                Text {
                    width: 56
                    height: 25
                    color: "#C8C8C8"
                    font.family: "Segoe UI"
                    font.pixelSize: 12
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    text: modelData
                }
            }
        }
    }

    Row {
        anchors.left: titles.right
        anchors.top: parent.top
        width: parent.width - titles.width
        height: parent.height
        spacing: 0

        Repeater {
            id: eqItems
            model: 31

            EqBandItem {
                id: eqItem
                width: parent.width / 31
                height: parent.height

                band: modelData

                Connections {
                    target: byp
                    function onBtnClicked(checked) {
                        eqItem.setBypass(checked)
                    }
                }
            }
        }
    }
}
