#include "CommonController.h"
#include <QTime>
#include <QCoreApplication>

CommonController::CommonController(QObject *parent)
    : QObject{parent}
    , mSaveFile("")
    , mSavePackageCount(0)
    , mSpChosenValue(0)
{
    mLineDisplayState.clear();

    initData();
    // reconnectDevice();

    mNetManager = new QNetworkAccessManager(this);
    QUrl url("https://www.pioneer-carglobal.com/dspamp/software/version-pc.txt");  // 服务器的文件地址
    QNetworkRequest request(url);
    request.setTransferTimeout(5000);
    QNetworkReply *reply = mNetManager->get(request);
    connect(reply, &QNetworkReply::finished, [=]() {
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray data = reply->readAll();  // 获取文件数据
            // 处理文件数据...
            bool versionRet = false;
            QByteArrayView beginIndexStr("DSP Controller-v");
            int beginPos = data.lastIndexOf(beginIndexStr);
            if(-1 != beginPos)
            {
                beginPos = beginPos + beginIndexStr.length();
                data.slice(beginPos);
                QByteArrayView endIndexStr(".exe");
                int endPos = data.lastIndexOf(endIndexStr);
                if(-1 != endPos)
                {
                    data.truncate(endPos);

                    //获取最新版本号todo。无网络时，如何处理？超时时间？
                    QString newPcVersion = QString(data);
                    // mUiDataMap.insert("newPcVersion", newPcVersion);
                    //对比pc版本号
                    // QString currentPcVersion = QCoreApplication::applicationVersion();
                    // versionRet = (currentPcVersion != newPcVersion);
                    QStringList newPcVersionList = newPcVersion.split(".");
                    if(3 == newPcVersionList.count())
                    {
                        versionRet = !isVersionAtLeast(newPcVersionList.at(0).toInt(),
                                                       newPcVersionList.at(1).toInt(),
                                                       newPcVersionList.at(2).toInt());
                    }
                    else
                    {
                        qDebug() << "newPcVersionList.count() is not 3, " << newPcVersionList.count();
                    }
                }
                else
                {
                    qDebug() << "no .exe in data";
                }
            }
            else
            {
                qDebug() << "no DSP Controller-v in data";
            }

            //pc升级弹窗signal
            emit showToolUpdate(versionRet);
        }
        else
        {
            // 请求失败，处理错误信息...
            if (reply->error() == QNetworkReply::TimeoutError) {
                qDebug() << "Request timed out";
            } else {
                qDebug() << "Error:" << reply->errorString();
            }

            emit showToolUpdate(false);
        }
        reply->deleteLater();
    });

    connect(&USBManager::getInstance(), &USBManager::receivedErrorSig, [&](int error){
        if(0 > error)
        {
            qDebug()<<"hid error "<<error;
            mUiDataMap.insert("hidConnection", 0);
            emit uiDataMapValueChanged("hidConnection", 0);
        }
    });

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::deviceTypeChanged, [this](QString typeStr)
                {
                    mDataMap.insert("deviceType", typeStr);
                    emit dataMapValueChanged("deviceType", typeStr);

                    int level = 0;
                    if("DEQ-7000A" == typeStr)
                    {
                        level = 2;
                    }
                    else if("DEQ-2000A" == typeStr)
                    {
                        level = 1;
                    }
                    else
                    {

                    }

                    mUiDataMap.insert("deviceLevel", level);
                    emit uiDataMapValueChanged("deviceLevel", level);
                    emit deviceConnection();

                    DataCenterController::getInstance().initData(level);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mainVersionChanged, [this](uint8_t version)
                {
                    mDataMap.insert("mainVersion", version);
                    emit dataMapValueChanged("mainVersion", version);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::subVersionChanged, [this](uint8_t version)
                {
                    mDataMap.insert("subVersion", version);
                    emit dataMapValueChanged("subVersion", version);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mainGainChanged, [this](uint16_t gain)
                {
                    mDataMap.insert("masterGain", gain);
                    // emit dataMapValueChanged("masterGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mainMuteChanged, [this](uint8_t isMute)
                {
                    mDataMap.insert("masterMute", isMute);
                    // emit dataMapValueChanged("masterMute", (0 == isMute) ? false : true);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::usbConnectedChanged, [this](uint8_t isConnected)
                {
                    mDataMap.insert("usbConnected", isConnected);
                    emit dataMapValueChanged("usbConnected", isConnected);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::btConnectedChanged, [this](uint8_t isConnected)
                {
                    mDataMap.insert("btConnected", isConnected);
                    emit dataMapValueChanged("btConnected", isConnected);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::aptxConnectedChanged, [this](uint8_t isConnected)
                {
                    mDataMap.insert("aptxConnected", isConnected);
                    emit dataMapValueChanged("aptxConnected", isConnected);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::uacConnectedChanged, [this](uint8_t isConnected)
                {
                    mDataMap.insert("uacConnected", isConnected);
                    emit dataMapValueChanged("uacConnected", isConnected);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mainUnitSupportedChanged, [this](uint8_t type)
                {
                    mDataMap.insert("mainUnitEnabledType", type);
                    emit dataMapValueChanged("mainUnitEnabledType", type);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::auxSupportedChanged, [this](uint8_t enabled)
                {
                    mDataMap.insert("auxEnabled", enabled);
                    emit dataMapValueChanged("auxEnabled", enabled);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::btSupportedChanged, [this](uint8_t enabled)
                {
                    mDataMap.insert("btEnabled", enabled);
                    emit dataMapValueChanged("btEnabled", enabled);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::spdifSupportedChanged, [this](uint8_t enabled)
                {
                    mDataMap.insert("spdifEnabled", enabled);
                    emit dataMapValueChanged("spdifEnabled", enabled);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::usbAudioSupportedChanged, [this](uint8_t enabled)
                {
                    mDataMap.insert("usbEnabled", enabled);
                    emit dataMapValueChanged("usbEnabled", enabled);
                });
    }

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelTypeChanged, [&](uint8_t channel, uint16_t type)
                {
                    int oldSpeaker = spIdMap.value(static_cast<SpeakerEnum>(mChannelDataMaps[channel]["type"].toUInt()));
                    if(-1 != oldSpeaker)
                    {
                        mUiDataMap.insert(QString("speaker%1Channel").arg(oldSpeaker), -1);
                        emit uiDataMapValueChanged(QString("speaker%1Channel").arg(oldSpeaker), -1);
                    }

                    mSpChosenValue = mSpChosenValue - spChosenBitsMap.value(static_cast<SpeakerEnum>(mChannelDataMaps[channel]["type"].toUInt()));
                    mSpChosenValue = mSpChosenValue + spChosenBitsMap.value(static_cast<SpeakerEnum>(type));

                    QMap<SpeakerEnum, bool> spMap = getSpeakerMap();
                    for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
                    {
                        for(SpeakerEnum sp: spMap.keys())
                        {
                            if(SpeakerEnum::SP_DEFAULT != sp)
                            {
                                emit speakerChanged(channel, static_cast<int>(sp), spMap.value(sp));
                            }
                        }
                    }

                    mChannelDataMaps[channel].insert("type", type);
                    emit channelDataMapsValueChanged(channel, "type", type);

                    int speaker = spIdMap.value(static_cast<SpeakerEnum>(type));
                    if(-1 != speaker)
                    {
                        mUiDataMap.insert(QString("speaker%1Channel").arg(speaker), channel);
                        emit uiDataMapValueChanged(QString("speaker%1Channel").arg(speaker), channel);
                    }
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelGainChanged, [&](uint8_t channel, uint16_t gain)
                {
                    mChannelDataMaps[channel].insert("gain", gain);
                    emit channelDataMapsValueChanged(channel, "gain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelPositiveNegativeChanged, [&](uint8_t channel, uint8_t positiveNegative)
                {
                    mChannelDataMaps[channel].insert("positiveNegative", positiveNegative);
                    emit channelDataMapsValueChanged(channel, "positiveNegative", positiveNegative);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelMuteChanged, [&](uint8_t channel, uint8_t mute)
                {
                    mChannelDataMaps[channel].insert("mute", mute);
                    emit channelDataMapsValueChanged(channel, "mute", mute);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelDelayChanged, [&](uint8_t channel, uint16_t delay)
                {
                    mChannelDataMaps[channel].insert("delay", delay);
                    emit channelDataMapsValueChanged(channel, "delay", delay);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelDelayGroupChanged, [&](uint8_t channel, uint8_t group)
                {
                    mChannelDataMaps[channel].insert("delayGroup", group);
                    emit channelDataMapsValueChanged(channel, "delayGroup", group);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelEqSetChanged, [&](uint8_t channel, uint8_t eqSet)
                {
                    mChannelDataMaps[channel].insert("eqSet", eqSet);
                    emit channelDataMapsValueChanged(channel, "eqSet", eqSet);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelEqTypeChanged, [&](uint8_t channel, uint8_t eqType)
                {
                    mChannelDataMaps[channel].insert("eqType", eqType);
                    emit channelDataMapsValueChanged(channel, "eqType", eqType);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLinkTypeChanged, [&](uint8_t channel, uint8_t linkType)
                {
                    mChannelDataMaps[channel].insert("linkType", linkType);
                    emit channelDataMapsValueChanged(channel, "linkType", linkType);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLinkChannelChanged, [&](uint8_t channel, uint8_t linkChannel)
                {
                    mChannelDataMaps[channel].insert("linkChannel", linkChannel);
                    emit channelDataMapsValueChanged(channel, "linkChannel", linkChannel);

                    switch(mChannelDataMaps[channel]["linkType"].toUInt())
                    {
                    case 0:
                    {
                        if(channel < OUTPUT_CHANNEL_MAX)
                        {
                            mEqLinkChannels[channel].status = -1;
                            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                                mEqLinkChannels[channel].linkedChannels[index] = false;
                                mEqLinkChannels[index].linkedChannels[channel] = false;
                            }
                            emit eqLinkChannelChanged(channel, -1);
                        }
                        break;
                    }
                    case 1:
                    {
                        if((channel < OUTPUT_CHANNEL_MAX) && (linkChannel < OUTPUT_CHANNEL_MAX))
                        {
                            mEqLinkChannels[channel].status = linkChannel;
                            mEqLinkChannels[linkChannel].status = -2;
                            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                                mEqLinkChannels[index].linkedChannels[channel] = false;
                            }
                            mEqLinkChannels[linkChannel].linkedChannels[channel] = true;
                            emit eqLinkChannelChanged(channel, linkChannel);
                            emit eqLinkChannelChanged(linkChannel, -2);
                        }
                        break;
                    }
                    case 2:
                    {
                        mEqLinkChannels[channel].status = -2;
                        for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                            mEqLinkChannels[index].linkedChannels[channel] = false;
                        }
                        emit eqLinkChannelChanged(channel, -2);
                        break;
                    }
                    }
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelHpFreqChanged, [&](uint8_t channel, uint16_t hpFreq)
                {
                    mChannelDataMaps[channel].insert("hpFreq", hpFreq);
                    emit channelDataMapsValueChanged(channel, "hpFreq", hpFreq);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelHpSlopeChanged, [&](uint8_t channel, uint8_t hpSlope)
                {
                    mChannelDataMaps[channel].insert("hpSlope", hpSlope);
                    emit channelDataMapsValueChanged(channel, "hpSlope", hpSlope);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelHpTypeChanged, [&](uint8_t channel, uint8_t hpType)
                {
                    mChannelDataMaps[channel].insert("hpType", hpType);
                    emit channelDataMapsValueChanged(channel, "hpType", hpType);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelHpEnableChanged, [&](uint8_t channel, uint8_t hpEnable)
                {
                    mChannelDataMaps[channel].insert("hpEnable", hpEnable);
                    emit channelDataMapsValueChanged(channel, "hpEnable", hpEnable);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLpFreqChanged, [&](uint8_t channel, uint16_t lpFreq)
                {
                    mChannelDataMaps[channel].insert("lpFreq", lpFreq);
                    emit channelDataMapsValueChanged(channel, "lpFreq", lpFreq);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLpSlopeChanged, [&](uint8_t channel, uint8_t lpSlope)
                {
                    mChannelDataMaps[channel].insert("lpSlope", lpSlope);
                    emit channelDataMapsValueChanged(channel, "lpSlope", lpSlope);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLpTypeChanged, [&](uint8_t channel, uint8_t lpType)
                {
                    mChannelDataMaps[channel].insert("lpType", lpType);
                    emit channelDataMapsValueChanged(channel, "lpType", lpType);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::outputChannelLpEnableChanged, [&](uint8_t channel, uint8_t lpEnable)
                {
                    mChannelDataMaps[channel].insert("lpEnable", lpEnable);
                    emit channelDataMapsValueChanged(channel, "lpEnable", lpEnable);
                });
    }

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::dspGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("dspGain", gain);
                    emit dataMapValueChanged("dspGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::dspMuteChanged, [&](uint8_t isMute)
                {
                    mDataMap.insert("dspMute", isMute);
                    emit dataMapValueChanged("dspMute", isMute);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::bassLevelChanged, [&](uint8_t level)
                {
                    mDataMap.insert("bassLevel", level);
                    emit dataMapValueChanged("bassLevel", level);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::bassMuteChanged, [&](uint8_t isMute)
                {
                    mDataMap.insert("bassMute", isMute);
                    emit dataMapValueChanged("bassMute", isMute);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::currentSourceChanged, [&](uint8_t source)
                {
                    mDataMap.insert("currentSource", source);
                    emit dataMapValueChanged("currentSource", source);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::currentMemoryChanged, [&](uint8_t memory)
                {
                    if(mDataMap["currentMemory"].toUInt() != memory)
                    {
                        int deviceLevel = mUiDataMap["deviceLevel"].toInt();
                        DataCenterController::getInstance().updateDataByMemoryChange(deviceLevel);
                    }

                    mDataMap.insert("currentMemory", memory);
                    emit dataMapValueChanged("currentMemory", memory);

                    mUiDataMap.insert("selectedMemoryType", (MEMORY_MAX > memory) ? 0 : 1);
                    mUiDataMap.insert("selectedMemoryId", (MEMORY_MAX > memory) ? memory : (memory - MEMORY_MAX));
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mixGainChanged, [&](uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t gain)
                {
                    switch (source) {
                    case static_cast<uint8_t>(MixTypeEnum::HIGH):
                        mMixHighDataMaps[inputChannel][outputChannel].insert("gain", gain);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "gain", gain);
                        break;
                    case static_cast<uint8_t>(MixTypeEnum::RCA):
                        mMixRCADataMaps[inputChannel][outputChannel].insert("gain", gain);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "gain", gain);
                        break;
                    case static_cast<uint8_t>(MixTypeEnum::DSP):
                        mMixDSPDataMaps[inputChannel][outputChannel].insert("gain", gain);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "gain", gain);
                        break;
                    default:
                        break;
                    }
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::mixEnableChanged, [&](uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t enable)
                {
                    switch (source) {
                    case static_cast<uint8_t>(MixTypeEnum::HIGH):
                        mMixHighDataMaps[inputChannel][outputChannel].insert("enable", enable);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "enable", (0 == enable) ? false : true);
                        break;
                    case static_cast<uint8_t>(MixTypeEnum::RCA):
                        mMixRCADataMaps[inputChannel][outputChannel].insert("enable", enable);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "enable", (0 == enable) ? false : true);
                        break;
                    case static_cast<uint8_t>(MixTypeEnum::DSP):
                        mMixDSPDataMaps[inputChannel][outputChannel].insert("enable", enable);
                        // emit mixDataMapsValueChanged(source, inputChannel, outputChannel, "enable", (0 == enable) ? false : true);
                        break;
                    default:
                        break;
                    }
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::eqFreqChanged, [&](uint8_t channel, uint8_t band, uint16_t freq)
                {
                    mEqDataMaps[channel][band].insert("freq", freq);
                    emit eqDataMapsValueChanged(channel, band, "freq", freq);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::eqQValueChanged, [&](uint8_t channel, uint8_t band, uint16_t qValue)
                {
                    mEqDataMaps[channel][band].insert("qValue", qValue);
                    emit eqDataMapsValueChanged(channel, band, "qValue", qValue);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::eqGainChanged, [&](uint8_t channel, uint8_t band, uint16_t gain)
                {
                    mEqDataMaps[channel][band].insert("gain", gain);
                    emit eqDataMapsValueChanged(channel, band, "gain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::eqTypeChanged, [&](uint8_t channel, uint8_t band, uint8_t type)
                {
                    mEqDataMaps[channel][band].insert("type", type);
                    emit eqDataMapsValueChanged(channel, band, "type", type);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::eqEnableChanged, [&](uint8_t channel, uint8_t band, uint16_t enable)
                {
                    mEqDataMaps[channel][band].insert("enable", enable);
                    emit eqDataMapsValueChanged(channel, band, "enable", enable);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::memoryEnableChanged, [&](uint8_t type, uint8_t memoryId, uint8_t enable)
                {
                    // int memory = (MEMORY_MAX > memoryId) ? memoryId : (memoryId - MEMORY_MAX);
                    mMemorySettingDataMaps[type][memoryId].insert("enable", enable);
                    emit memorySettingDataMapsValueChanged(type, memoryId, "enable", enable);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::memoryNameChanged, [&](uint8_t type, uint8_t memoryId, QString name)
                {
                    // int memory = (MEMORY_MAX > memoryId) ? memoryId : (memoryId - MEMORY_MAX);
                    mMemorySettingDataMaps[type][memoryId].insert("name", name);
                    emit memorySettingDataMapsValueChanged(type, memoryId, "name", name);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::presetModeChanged, [&](uint8_t mode)
                {
                    mDataMap.insert("presetMode", mode);
                    emit dataMapValueChanged("presetMode", mode);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::presetLevelChanged, [&](uint8_t level)
                {
                    mDataMap.insert("presetLevel", level);
                    emit dataMapValueChanged("presetLevel", level);
                });
    }

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::naviMixEnableChanged, [&](uint8_t isEnabled)
                {
                    mDataMap.insert("naviMixEnable", isEnabled);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::naviSensitivityChanged, [&](uint8_t sensitivity)
                {
                    mDataMap.insert("naviSensitivity", sensitivity);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::naviAttenuationChanged, [&](uint8_t attenuation)
                {
                    mDataMap.insert("naviAttenuation", attenuation);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::naviDurationChanged, [&](uint8_t duration)
                {
                    mDataMap.insert("naviDuration", duration);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::externalAttenuationChanged, [&](uint8_t attenuation)
                {
                    mDataMap.insert("externalAttenuation", attenuation);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::externalPolarityChanged, [&](uint8_t polarity)
                {
                    mDataMap.insert("externalPolarity", polarity);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::externalDspAttenuationChanged, [&](uint8_t attenuation)
                {
                    mDataMap.insert("externalDspAttenuation", attenuation);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::delayUnitChanged, [&](uint8_t unit)
                {
                    mDataMap.insert("delayUnit", unit);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::highGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("highGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::rcaGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("rcaGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::auxGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("auxGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::btGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("btGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::spdifGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("spdifGain", gain);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::usbGainChanged, [&](uint8_t gain)
                {
                    mDataMap.insert("usbGain", gain);
                });

        connect(&DataCenterController::getInstance(), &DataCenterController::savePackageCountChanged, [&](int count, const QByteArray data) {
            qInfo() << "savePackageCountChanged, count " << count << " mSavePackageCount " << mSavePackageCount;
            if(0 == mSavePackageCount)
            {
                mSavePackageCount = count;
                mSaveData.clear();
                mSaveData.append(data);
                DataCenterController::getInstance().querySaveData(1);
            }
            else
            {
                qDebug() << "savePackageCountChanged, mSavePackageCount is " << mSavePackageCount;
            }
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::savePackageDataChanged, [&](uint16_t index, const QByteArray data) {
            qDebug() << "savePackageDataChanged, index " << index << " mSavePackageCount " << mSavePackageCount;
            if(mSavePackageCount >= index)
            {
                mSaveData.append(data);

                if(mSavePackageCount == index)
                {
                    if(!mSaveFile.isEmpty())
                    {
                        QByteArray header("PioneerDSP");

                        QByteArray dataLengthArray;
                        uint dataLength = mSaveData.length();
                        dataLengthArray.resize(sizeof(dataLength));
                        memcpy(dataLengthArray.data(), &dataLength, sizeof(dataLength));
                        header.append(dataLengthArray);

                        uint8_t crcCode = DataCenterController::calCrc8(mSaveData);
                        header.append(crcCode);

                        mSaveData.prepend(header);

                        QFile file(mSaveFile);
                        if(!file.open(QIODevice::WriteOnly | QIODevice::Truncate))
                        {
                            qDebug() << QString("Failed to open file \"%1\" for save!").arg(mSaveFile);
                        }
                        file.write(mSaveData);
                        mSaveFile = "";
                        mSavePackageCount = 0;
                    }
                }
                else
                {
                    DataCenterController::getInstance().querySaveData(index + 1);
                }
            }
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::loadPackageFinished, [&]() {
            int level = mUiDataMap["deviceLevel"].toInt();
            DataCenterController::getInstance().initData(level);
        });
    }

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::dspMainVersionChanged, [&](uint8_t version)
                {
                    mDataMap.insert("dspMainVersion", version);
                    emit dataMapValueChanged("dspMainVersion", version);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::dspSubVersionChanged, [&](uint8_t version)
                {
                    mDataMap.insert("dspSubVersion", version);
                    emit dataMapValueChanged("dspSubVersion", version);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteMainVersionChanged, [&](uint8_t version)
                {
                    mDataMap.insert("remoteMainVersion", version);
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteSubVersionChanged, [&](uint8_t version)
                {
                    mDataMap.insert("remoteSubVersion", version);
                    // 服务器的文件地址
                    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
                    QUrl url(QString("https://www.pioneer-carglobal.com/dspamp/firmware/version-%1.txt")
                                 .arg((1 == deviceLevel) ? "l" : "h"));
                    QNetworkRequest request(url);
                    request.setTransferTimeout(5000);
                    QNetworkReply *reply = mNetManager->get(request);
                    connect(reply, &QNetworkReply::finished, [=]() {
                        mUiDataMap.insert("newDspVersion", "");
                        if (reply->error() == QNetworkReply::NoError) {
                            QByteArray data = reply->readAll();  // 获取文件数据
                            // 处理文件数据...
                            QByteArrayView beginIndexStr("MCU_");
                            int beginPos = data.lastIndexOf(beginIndexStr);
                            if(-1 != beginPos)
                            {
                                beginPos = beginPos + beginIndexStr.length();
                                data.slice(beginPos);
                                QString newDspVersion = QString(data);
                                QStringList versionList = newDspVersion.split(".");
                                newDspVersion = QString::number(versionList[0].toInt()) + "." + QString::number(versionList[1].toInt());
                                mUiDataMap.insert("newDspVersion", newDspVersion);
                                emit dataMapValueChanged("newDspVersion", newDspVersion);
                            }
                            else
                            {
                                qDebug() << "no MCU_ in data";
                            }
                        }
                        else
                        {
                            // 处理错误
                            if (reply->error() == QNetworkReply::TimeoutError) {
                                qDebug() << "Request timed out";
                            } else {
                                qDebug() << "Error:" << reply->errorString();
                            }
                        }
                        emit initFinish();
                        reply->deleteLater();
                    });
                });
        connect(&DataCenterController::getInstance(), &DataCenterController::upgradeError, [&](uint8_t code) {
            qDebug()<<"dsp upgrade error "<<code;
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::upgradeProgress, [&](uint8_t progress) {
            qInfo()<<"dsp upgrade progress "<<progress;
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::upgradeStatus, [&](uint8_t status) {
            if(0 == status)
            {
                emit CommonController::getInstance().openWarningPopup(16);
            }
            else if(1 == status)
            {
                DataCenterController::getInstance().getUpgradeDspRemoteRet0503();
            }
            else if(4 == status)
            {
                emit CommonController::getInstance().openWarningPopup(17);
            }
            else
            {

            }
        });
    }

    {
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteBrightnessChanged, [&](uint8_t value) {
            mDataMap.insert("remoteBrightness", value);
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteDimmerChanged, [&](uint8_t value) {
            mDataMap.insert("remoteDimmer", value);
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::remotePolarityChanged, [&](uint8_t value) {
            mDataMap.insert("remotePolarity", value);
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteDimmerBrightnessChanged, [&](uint8_t value) {
            mDataMap.insert("remoteDimmerBrightness", value);
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteModelChanged, [&](uint8_t value) {
            mDataMap.insert("remoteModel", value);
        });
        connect(&DataCenterController::getInstance(), &DataCenterController::remoteShortCutChanged, [&](uint8_t id, uint8_t type, uint8_t memory) {
            mRemoteShortCutMaps[id].insert("type", type);
            mRemoteShortCutMaps[id].insert("memory", memory);
        });
    }
}

void CommonController::initData()
{
    mSaveData.clear();

    // mUiDataMap
    {
        //0:":/en_US.qm"; 1:":/jp_JP.qm"
        QLocale::Language language = QLocale::system().language();
        mUiDataMap.insert("language", (QLocale::Japanese == language) ? 1 : 0);
        mUiDataMap.insert("hidConnection", 0);
        mUiDataMap.insert("deviceLevel", 0);
        // mUiDataMap.insert("newPcVersion", "");
        mUiDataMap.insert("newDspVersion", "");
        mUiDataMap.insert("selectedChannel", 0);
        mUiDataMap.insert("selectedBand", 0);
        mUiDataMap.insert("selectedMemoryType", 0);
        mUiDataMap.insert("selectedMemoryId", 0);
        mUiDataMap.insert("eqLinkPopupChannel", 0);
        for(int speaker = 0; speaker < 19; speaker++)
        {
            mUiDataMap.insert(QString("speaker%1Channel").arg(speaker), -1);
        }
        mUiDataMap.insert("warningNum", 0);
        for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
        {
            mUiDataMap.insert(QString("channel%1AllBypassState").arg(channel), 0);
        }

        connect(&mUiDataMap, &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                {
                    qDebug() << "mUiDataMap"  << " Property " << key << " changed to:" << value;
                    emit uiDataMapValueChanged(key, value);
                });
        mDataMap.insert("uiDataMap", QVariant::fromValue(&mUiDataMap));
    }

    // mUiEqSaveGainMap
    {
        for(int memory = 0; memory < (MEMORY_MAX * MEMORY_TYPE_MAX); memory++)
        {
            for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
            {
                for(int band = 0; band < EQ_BAND_MAX; band++)
                {
                    mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(channel).arg(band), 600);
                    mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(channel).arg(band), 4320);
                    mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(channel).arg(band), 2000);
                }
            }
            mDataMap.insert(QString("uiEqSaveGainMemory%1").arg(memory), QVariant::fromValue(&mUiEqSaveGainMaps[memory]));
            mDataMap.insert(QString("uiEqSaveQMemory%1").arg(memory), QVariant::fromValue(&mUiEqSaveQMaps[memory]));
        }
    }

    // mDataMap
    {
        mDataMap.insert("deviceType", tr("Demo mode"));
        mDataMap.insert("mainVersion", 0);
        mDataMap.insert("subVersion", 0);
        mDataMap.insert("masterGain", 0);
        mDataMap.insert("masterMute", 0);
        mDataMap.insert("usbConnected", 0);
        mDataMap.insert("btConnected", 0);
        mDataMap.insert("aptxConnected", 0);
        mDataMap.insert("uacConnected", 0);
        mDataMap.insert("mainUnitEnabledType", 1);
        mDataMap.insert("auxEnabled", 0);
        mDataMap.insert("btEnabled", 1);
        mDataMap.insert("spdifEnabled", 0);
        mDataMap.insert("usbEnabled", 0);

        connect(&mDataMap, &QQmlPropertyMap::valueChanged, [](const QString &key, const QVariant &value)
                {
                    qDebug() << "mDataMap Property" << key << "changed to:" << value;
                });
    }

    {
        for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
        {
            mChannelDataMaps[channel].insert("type", 0);
            mChannelDataMaps[channel].insert("gain", 0);
            mChannelDataMaps[channel].insert("positiveNegative", 0);
            mChannelDataMaps[channel].insert("mute", 0);
            mChannelDataMaps[channel].insert("delay", 0);
            mChannelDataMaps[channel].insert("delayGroup", 0);
            mChannelDataMaps[channel].insert("eqSet", 0);
            mChannelDataMaps[channel].insert("eqType", 0);
            mChannelDataMaps[channel].insert("linkType", 0);
            mChannelDataMaps[channel].insert("linkChannel", 0);
            mChannelDataMaps[channel].insert("hpFreq", ((channel < 8) ? 80 : 20));
            mChannelDataMaps[channel].insert("hpSlope", 2);
            mChannelDataMaps[channel].insert("hpType", 0);
            mChannelDataMaps[channel].insert("hpEnable", 0);
            mChannelDataMaps[channel].insert("lpFreq", ((channel < 8) ? 40000 : 63));
            mChannelDataMaps[channel].insert("lpSlope", 2);
            mChannelDataMaps[channel].insert("lpType", 0);
            mChannelDataMaps[channel].insert("lpEnable", 0);

            connect(&mChannelDataMaps[channel], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                    {
                        qDebug() << "mChannelDataMaps " << QString::number(channel) << " Property " << key << " changed to:" << value;
                    });

            mDataMap.insert(QString("channel%1DataMap").arg(channel), QVariant::fromValue(&mChannelDataMaps[channel]));
        }
    }

    {
        mDataMap.insert("dspGain", 0);
        mDataMap.insert("dspMute", 0);
        mDataMap.insert("bassLevel", 0);
        mDataMap.insert("bassMute", 0);
        mDataMap.insert("currentSource", 0);
        mDataMap.insert("currentMemory", -1);
        for(int inputChannel = 0; inputChannel < MIX_HIGH_CHANNEL_MAX; inputChannel++)
        {
            for(int outputChannel = 0; outputChannel < OUTPUT_CHANNEL_MAX; outputChannel++)
            {
                mMixHighDataMaps[inputChannel][outputChannel].insert("gain", 0);
                mMixHighDataMaps[inputChannel][outputChannel].insert("enable", 0);

                connect(&mMixHighDataMaps[inputChannel][outputChannel], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                        {
                            qDebug() << "mMixHighDataMaps in" << QString::number(inputChannel) << " out" << QString::number(outputChannel)
                            << " Property " << key << " changed to:" << value;
                        });

                mDataMap.insert(QString("mixHighIn%1Out%2").arg(inputChannel).arg(outputChannel), QVariant::fromValue(&mMixHighDataMaps[inputChannel][outputChannel]));
            }
        }
        for(int inputChannel = 0; inputChannel < MIX_RCA_CHANNEL_MAX; inputChannel++)
        {
            for(int outputChannel = 0; outputChannel < OUTPUT_CHANNEL_MAX; outputChannel++)
            {
                mMixRCADataMaps[inputChannel][outputChannel].insert("gain", 0);
                mMixRCADataMaps[inputChannel][outputChannel].insert("enable", false);

                connect(&mMixRCADataMaps[inputChannel][outputChannel], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                        {
                            qDebug() << "mMixRCADataMaps in" << QString::number(inputChannel) << " out" << QString::number(outputChannel)
                            << " Property " << key << " changed to:" << value;
                        });

                mDataMap.insert(QString("mixRCAIn%1Out%2").arg(inputChannel).arg(outputChannel), QVariant::fromValue(&mMixRCADataMaps[inputChannel][outputChannel]));
            }
        }
        for(int inputChannel = 0; inputChannel < MIX_DSP_CHANNEL_MAX; inputChannel++)
        {
            for(int outputChannel = 0; outputChannel < OUTPUT_CHANNEL_MAX; outputChannel++)
            {
                mMixDSPDataMaps[inputChannel][outputChannel].insert("gain", 0);
                mMixDSPDataMaps[inputChannel][outputChannel].insert("enable", false);

                connect(&mMixDSPDataMaps[inputChannel][outputChannel], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                        {
                            qDebug() << "mMixDSPDataMaps in" << QString::number(inputChannel) << " out" << QString::number(outputChannel)
                            << " Property " << key << " changed to:" << value;
                        });

                mDataMap.insert(QString("mixDSPIn%1Out%2").arg(inputChannel).arg(outputChannel), QVariant::fromValue(&mMixDSPDataMaps[inputChannel][outputChannel]));
            }
        }

        QList<int> initFreqs = {20, 25, 32, 40, 50, 63, 80, 100, 125, 160, 200, 250, 315, 400, 500, 630, 800, 1000,
                                1250, 1600, 2000, 2500, 3150, 4000, 5000, 6300, 8000, 10000, 12500, 16000, 20000};
        for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
        {
            for(int band = 0; band < EQ_BAND_MAX; band++)
            {
                mEqDataMaps[channel][band].insert("freq", initFreqs[band]);
                mEqDataMaps[channel][band].insert("qValue", 4320);
                mEqDataMaps[channel][band].insert("gain", 600);
                mEqDataMaps[channel][band].insert("type", 0);
                mEqDataMaps[channel][band].insert("enable", 0);

                connect(&mEqDataMaps[channel][band], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                        {
                            qDebug() << "mEqDataMaps channel" << QString::number(channel) << " band" << QString::number(band)
                            << " Property " << key << " changed to:" << value;
                        });

                mDataMap.insert(QString("eqChannel%1Band%2").arg(channel).arg(band), QVariant::fromValue(&mEqDataMaps[channel][band]));
            }
        }

        for(int type = 0; type < MEMORY_TYPE_MAX; type++)
        {
            for(int index = 0; index < MEMORY_MAX; index++)
            {
                mMemorySettingDataMaps[type][index].insert("enable", 0);
                mMemorySettingDataMaps[type][index].insert("name", "");

                connect(&mMemorySettingDataMaps[type][index], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                        {
                            qDebug() << "mMemorySettingDataMaps type" << QString::number(type) << " index" << QString::number(index)
                            << " Property " << key << " changed to:" << value;
                        });

                mDataMap.insert(QString("memorySettingType%1Index%2").arg(type).arg(index), QVariant::fromValue(&mMemorySettingDataMaps[type][index]));
            }
        }
        mDataMap.insert("presetMode", 0);
        mDataMap.insert("presetLevel", 0);
    }

    {
        mDataMap.insert("naviMixEnable", 0);
        mDataMap.insert("naviSensitivity", 0);
        mDataMap.insert("naviAttenuation", 0);
        mDataMap.insert("naviDuration", 0);
        mDataMap.insert("externalAttenuation", 0);
        mDataMap.insert("externalPolarity", 0);
        mDataMap.insert("externalDspAttenuation", 0);
        mDataMap.insert("delayUnit", 2);
        mDataMap.insert("highGain", 0);
        mDataMap.insert("rcaGain", 0);
        mDataMap.insert("auxGain", 0);
        mDataMap.insert("btGain", 0);
        mDataMap.insert("spdifGain", 0);
        mDataMap.insert("usbGain", 0);
    }

    {
        mDataMap.insert("dspMainVersion", 0);
        mDataMap.insert("dspSubVersion", 0);
        mDataMap.insert("remoteMainVersion", 0);
        mDataMap.insert("remoteSubVersion", 0);
    }

    {
        mDataMap.insert("remoteBrightness", 0);
        mDataMap.insert("remoteDimmer", 0);
        mDataMap.insert("remotePolarity", 0);
        mDataMap.insert("remoteDimmerBrightness", 0);
        mDataMap.insert("remoteModel", 0);
        for(int index = 0; index < REMOTE_SHORTCUT_MAX; index++)
        {
            mRemoteShortCutMaps[index].insert("type", 0);
            mRemoteShortCutMaps[index].insert("memory", 0);

            connect(&mRemoteShortCutMaps[index], &QQmlPropertyMap::valueChanged, [&](const QString &key, const QVariant &value)
                    {
                        qDebug() << "mRemoteShortCutMaps index" << QString::number(index) << " Property " << key << " changed to:" << value;
                    });

            mDataMap.insert(QString("remoteShortCut%1").arg(index), QVariant::fromValue(&mRemoteShortCutMaps[index]));
        }
    }
}

void CommonController::reconnectDevice()
{
    USBManager::getInstance().connectDevice();
    DataCenterController::getInstance().connectRequest0101();
    mUiDataMap.insert("hidConnection", 1);
    emit uiDataMapValueChanged("hidConnection", 1);
}

QString CommonController::getSpeakerStr(uint16_t type)
{
    return spStrMap.value(static_cast<SpeakerEnum>(type));
}

int CommonController::getSpeakerId(uint16_t type)
{
    return spIdMap.value(static_cast<SpeakerEnum>(type));
}

QMap<SpeakerEnum, bool> CommonController::getSpeakerMap()
{
    QMap<SpeakerEnum, bool> spMap;

    for(SpeakerEnum sp: spChosenBitsMap.keys())
    {
        spMap.insert(sp, (0 == (mSpChosenValue & spChosenBitsMap.value(sp))));
    }

    return spMap;
}

void CommonController::setHpLpFreqBySpeakerChanged(int channel, uint16_t speaker)
{
    if(speaker != mChannelDataMaps[channel]["type"].toUInt())
    {
        // uint16_t hpFreq = mChannelDataMaps[channel]["hpFreq"].toUInt();
        uint16_t lpFreq = mChannelDataMaps[channel]["lpFreq"].toUInt();

        switch (static_cast<SpeakerEnum>(speaker)) {
        case SpeakerEnum::FL_TWEETER:
        case SpeakerEnum::FR_TWEETER:
        case SpeakerEnum::RL_TWEETER:
        case SpeakerEnum::RR_TWEETER:
        case SpeakerEnum::C_TWEETER:
        {
            if(lpFreq < 3400)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)3400);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)3400);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::FL_MIDRANGE:
        case SpeakerEnum::FR_MIDRANGE:
        case SpeakerEnum::RL_MIDRANGE:
        case SpeakerEnum::RR_MIDRANGE:
        {
            if(lpFreq < 600)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)1900);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)600);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)600);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)1900);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::FL_WOOFER:
        case SpeakerEnum::FR_WOOFER:
        case SpeakerEnum::RL_WOOFER:
        case SpeakerEnum::RR_WOOFER:
        {
            if(lpFreq < 70)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)320);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)70);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)70);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)320);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::FL_M_T:
        case SpeakerEnum::FR_M_T:
        {
            if(lpFreq < 600)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)600);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)600);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::FL_M_WF:
        case SpeakerEnum::FR_M_WF:
        case SpeakerEnum::RL_M_WF:
        case SpeakerEnum::RR_M_WF:
        case SpeakerEnum::C_M_WF:
        {
            if(lpFreq < 70)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)1900);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)70);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)70);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)1900);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::FL_FULL:
        case SpeakerEnum::FR_FULL:
        case SpeakerEnum::RL_FULL:
        case SpeakerEnum::RR_FULL:
        case SpeakerEnum::C_FULL:
        {
            if(lpFreq < 80)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)80);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)80);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::SUBWOOFER:
        case SpeakerEnum::L_SUBWOOFER:
        case SpeakerEnum::R_SUBWOOFER:
        {
            if(lpFreq < 20)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)70);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)20);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)20);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)70);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        case SpeakerEnum::USER_CONFIG_1:
        case SpeakerEnum::USER_CONFIG_2:
        {
            if(lpFreq < 20)
            {
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)70);
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)20000);
            }
            else
            {
                setOutputChannelHpFreq((uint8_t)channel, (uint16_t)20);
                setOutputChannelLpFreq((uint8_t)channel, (uint16_t)20000);
            }
            setOutputChannelHpEnable((uint8_t)channel, 1);
            setOutputChannelLpEnable((uint8_t)channel, 1);
            break;
        }
        default:
        {
            setOutputChannelType((uint8_t)channel, speaker);
            setOutputChannelHpEnable((uint8_t)channel, 0);
            setOutputChannelLpEnable((uint8_t)channel, 0);
            return;
        }
        }

        setOutputChannelType((uint8_t)channel, speaker);
    }
}

void CommonController::setEqLink(int channel, int linkChannel)
{
    if(-1 == linkChannel)
    {
        //只有从通道可以断开link
        if(-1 < mEqLinkChannels[channel].status)
        {
            uint8_t oldLinkedChannel = mEqLinkChannels[channel].status;
            mEqLinkChannels[channel].status = -1;
            mEqLinkChannels[oldLinkedChannel].linkedChannels[channel] = false;
            int oldLinkedChannelStatus = -1;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[oldLinkedChannel].linkedChannels[index])
                {
                    oldLinkedChannelStatus = -2;
                    break;
                }
            }
            emit eqLinkChannelChanged(channel, -1);
            emit eqLinkChannelChanged(oldLinkedChannel, oldLinkedChannelStatus);

            setOutputChannelLink(channel, 0, 0xFF);
            setOutputChannelLink(oldLinkedChannel, ((-1 == oldLinkedChannelStatus) ? 0 : 2), 0xFF);
        }
    }
    else
    {
        //linkChannel不能是channel，也不能是从通道
        if((linkChannel != channel) && (0 > mEqLinkChannels[linkChannel].status))
        {
            setEqLink(channel, -1);
            // setEqLink(linkChannel, -1);

            mEqLinkChannels[channel].status = linkChannel;
            mEqLinkChannels[linkChannel].status = -2;
            mEqLinkChannels[linkChannel].linkedChannels[channel] = true;
            emit eqLinkChannelChanged(channel, linkChannel);
            emit eqLinkChannelChanged(linkChannel, -2);

            setOutputChannelLink(channel, 1, linkChannel);
            setOutputChannelLink(linkChannel, 2, 0xFF);
            // update hpf/lpf and 31 bands data
            // setOutputChannelHpLpLinkCopy(channel, (linkChannel - 1));
            setEqLinkCopy(channel, linkChannel);
        }
    }
}

int CommonController::getEqLinkStatus(int channel)
{
    return mEqLinkChannels[channel].status;
}

void CommonController::setSelectedChannel(uint8_t channel)
{
    mUiDataMap.insert("selectedChannel", channel);
    emit uiDataMapValueChanged("selectedChannel", channel);
}

void CommonController::setHighRcaEnable(uint8_t type)
{
    DeviceOperation0809 data;
    data.mainUnit = type;
    data.aux = 0xFF;
    data.bt = 0xFF;
    data.spdif = 0xFF;
    data.usbAudio = 0xFF;
    DataCenterController::getInstance().setMusicSourceList0108(data);

    mDataMap.insert("mainUnitEnabledType", type);
    emit dataMapValueChanged("mainUnitEnabledType", type);
}

void CommonController::setAuxEnable(uint8_t enable)
{
    DeviceOperation0809 data;
    data.mainUnit = 0xFF;
    data.aux = enable;
    data.bt = 0xFF;
    data.spdif = 0xFF;
    data.usbAudio = 0xFF;
    DataCenterController::getInstance().setMusicSourceList0108(data);

    mDataMap.insert("auxEnabled", enable);
    emit dataMapValueChanged("auxEnabled", enable);
}

void CommonController::setBtEnable(uint8_t enable)
{
    DeviceOperation0809 data;
    data.mainUnit = 0xFF;
    data.aux = 0xFF;
    data.bt = enable;
    data.spdif = 0xFF;
    data.usbAudio = 0xFF;
    DataCenterController::getInstance().setMusicSourceList0108(data);

    mDataMap.insert("btEnabled", enable);
    emit dataMapValueChanged("btEnabled", enable);
}

void CommonController::setSpdifEnable(uint8_t enable)
{
    DeviceOperation0809 data;
    data.mainUnit = 0xFF;
    data.aux = 0xFF;
    data.bt = 0xFF;
    data.spdif = enable;
    data.usbAudio = 0xFF;
    DataCenterController::getInstance().setMusicSourceList0108(data);

    mDataMap.insert("spdifEnabled", enable);
    emit dataMapValueChanged("spdifEnabled", enable);
}

void CommonController::setUsbEnable(uint8_t enable)
{
    DeviceOperation0809 data;
    data.mainUnit = 0xFF;
    data.aux = 0xFF;
    data.bt = 0xFF;
    data.spdif = 0xFF;
    data.usbAudio = enable;
    DataCenterController::getInstance().setMusicSourceList0108(data);

    mDataMap.insert("usbEnabled", enable);
    emit dataMapValueChanged("usbEnabled", enable);
}

void CommonController::setOutputChannelType(uint8_t channel, uint16_t type)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.type.value = type;
    DataCenterController::getInstance().setOutputChannel0201(data);

    int oldSpeaker = spIdMap.value(static_cast<SpeakerEnum>(mChannelDataMaps[channel]["type"].toUInt()));
    if(-1 != oldSpeaker)
    {
        mUiDataMap.insert(QString("speaker%1Channel").arg(oldSpeaker), -1);
        emit uiDataMapValueChanged(QString("speaker%1Channel").arg(oldSpeaker), -1);
    }

    mSpChosenValue = mSpChosenValue - spChosenBitsMap.value(static_cast<SpeakerEnum>(mChannelDataMaps[channel]["type"].toUInt()));
    mSpChosenValue = mSpChosenValue + spChosenBitsMap.value(static_cast<SpeakerEnum>(type));

    QMap<SpeakerEnum, bool> spMap = getSpeakerMap();
    for(int channel = 0; channel < OUTPUT_CHANNEL_MAX; channel++)
    {
        for(SpeakerEnum sp: spMap.keys())
        {
            if(SpeakerEnum::SP_DEFAULT != sp)
            {
                emit speakerChanged(channel, static_cast<int>(sp), spMap.value(sp));
            }
        }
    }

    mChannelDataMaps[channel].insert("type", type);
    emit channelDataMapsValueChanged(channel, "type", type);

    int speaker = spIdMap.value(static_cast<SpeakerEnum>(type));
    if(-1 != speaker)
    {
        mUiDataMap.insert(QString("speaker%1Channel").arg(speaker), channel);
        emit uiDataMapValueChanged(QString("speaker%1Channel").arg(speaker), channel);
    }
}

void CommonController::setOutputChannelGain(uint8_t channel, uint16_t gain)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.gain = gain;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("gain", gain);
}

void CommonController::setOutputChannelPhase(uint8_t channel, uint8_t phase)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.positiveNegative = phase;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("positiveNegative", phase);
    emit channelDataMapsValueChanged(channel, "positiveNegative", phase);
}

void CommonController::setOutputChannelMute(uint8_t channel, uint8_t mute)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.mute = mute;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("mute", mute);
    emit channelDataMapsValueChanged(channel, "mute", mute);
}

void CommonController::setOutputChannelDelay(uint8_t channel, uint16_t delay)
{
    if(0 == mChannelDataMaps[channel]["delayGroup"].toUInt())
    {
        ChannelOperation0102 data;
        memset(&data, 0xFF, sizeof(data));
        data.channel = channel;
        data.delay = delay;
        DataCenterController::getInstance().setOutputChannel0201(data);

        mChannelDataMaps[channel].insert("delay", delay);
    }
    else
    {
        int delayDiff = static_cast<int>(delay) - static_cast<int>(mChannelDataMaps[channel]["delay"].toUInt());
        setDelayGroup(mChannelDataMaps[channel]["delayGroup"].toUInt(), delayDiff);
    }
}

void CommonController::setOutputChannelDelayGroup(uint8_t channel, uint8_t group)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.delayGroup = group;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("delayGroup", group);

    // if(0 != group)
    // {
    //     setDelayGroup(mChannelDataMaps[channel]["delayGroup"].toUInt(), mChannelDataMaps[channel]["delay"].toUInt());
    // }
}

void CommonController::setOutputChannelEqSet(uint8_t channel, uint8_t set)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.eqSet = set;
    DataCenterController::getInstance().setOutputChannel0201(data);
    mChannelDataMaps[channel].insert("eqSet", set);

    if(0 == set)
    {
        uint8_t memory = mDataMap["currentMemory"].toUInt();

        if(-1 != mEqLinkChannels[channel].status)
        {
            if(-1 < mEqLinkChannels[channel].status)
            {
                setEqLink(channel, -1);
            }
            else if(-2 == mEqLinkChannels[channel].status)
            {
                for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index)
                {
                    if(mEqLinkChannels[channel].linkedChannels[index])
                    {
                        ChannelOperation0102 data;
                        memset(&data, 0xFF, sizeof(data));
                        data.channel = index;
                        data.eqSet = set;
                        DataCenterController::getInstance().setOutputChannel0201(data);
                        mChannelDataMaps[index].insert("eqSet", set);

                        mUiDataMap.insert(QString("channel%1AllBypassState").arg(index), 0);
                        for(int band = 0; band < EQ_BAND_MAX; band++)
                        {
                            mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(index).arg(band), 600);
                            mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(channel).arg(band), 4320);
                            mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(channel).arg(band), 2000);
                        }
                    }
                }
            }
            else
            {

            }
        }

        mUiDataMap.insert(QString("channel%1AllBypassState").arg(channel), 0);
        for(int band = 0; band < EQ_BAND_MAX; band++)
        {
            mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(channel).arg(band), 600);
            mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(channel).arg(band), 4320);
            mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(channel).arg(band), 2000);
        }

        int deviceLevel = mUiDataMap["deviceLevel"].toInt();
        DataCenterController::getInstance().updateDataByEqResetChange(deviceLevel);
    }
    else if((1 == set) || (2 == set))
    {
        if(-1 != mEqLinkChannels[channel].status)
        {
            if(-1 < mEqLinkChannels[channel].status)
            {
                int linkChannel = mEqLinkChannels[channel].status;
                for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index)
                {
                    if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                    {
                        mUiDataMap.insert(QString("channel%1AllBypassState").arg(index),
                                          mUiDataMap[QString("channel%1AllBypassState").arg(channel)].toUInt());
                    }
                }
                mUiDataMap.insert(QString("channel%1AllBypassState").arg(linkChannel),
                                  mUiDataMap[QString("channel%1AllBypassState").arg(channel)].toUInt());
            }
            else if(-2 == mEqLinkChannels[channel].status)
            {
                for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index)
                {
                    if(mEqLinkChannels[channel].linkedChannels[index])
                    {
                        mUiDataMap.insert(QString("channel%1AllBypassState").arg(index),
                                          mUiDataMap[QString("channel%1AllBypassState").arg(channel)].toUInt());
                    }
                }
            }
        }
    }
    else
    {

    }
}

void CommonController::setOutputChannelEqType(uint8_t channel, uint8_t type)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.eqType = type;
    DataCenterController::getInstance().setOutputChannel0201(data);

    // if(1 == type)
    // {
    // int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    // DataCenterController::getInstance().updateDataByEqTypeChange(deviceLevel);
    // }

    mChannelDataMaps[channel].insert("eqType", type);
    emit channelDataMapsValueChanged(channel, "eqType", type);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index)
            {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    ChannelOperation0102 data;
                    memset(&data, 0xFF, sizeof(data));
                    data.channel = index;
                    data.eqType = type;
                    DataCenterController::getInstance().setOutputChannel0201(data);

                    mChannelDataMaps[index].insert("eqType", type);
                    emit channelDataMapsValueChanged(index, "eqType", type);
                }
            }
            ChannelOperation0102 data;
            memset(&data, 0xFF, sizeof(data));
            data.channel = linkChannel;
            data.eqType = type;
            DataCenterController::getInstance().setOutputChannel0201(data);

            mChannelDataMaps[linkChannel].insert("eqType", type);
            emit channelDataMapsValueChanged(linkChannel, "eqType", type);
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    ChannelOperation0102 data;
                    memset(&data, 0xFF, sizeof(data));
                    data.channel = index;
                    data.eqType = type;
                    DataCenterController::getInstance().setOutputChannel0201(data);

                    mChannelDataMaps[index].insert("eqType", type);
                    emit channelDataMapsValueChanged(index, "eqType", type);
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }

    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    DataCenterController::getInstance().updateDataByEqTypeChange(deviceLevel);
}

void CommonController::setOutputChannelLinkType(uint8_t channel, uint8_t type)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.linkType = type;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("linkType", type);
}

void CommonController::setOutputChannelLinkChannel(uint8_t channel, uint8_t linkChannel)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.linkChannel = linkChannel;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("linkChannel", linkChannel);
}

void CommonController::setOutputChannelLink(uint8_t channel, uint8_t type, uint8_t linkChannel)
{
    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.linkType = type;
    data.linkChannel = linkChannel;
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("linkType", type);
    mChannelDataMaps[channel].insert("linkChannel", linkChannel);
}

void CommonController::setOutputChannelHpFreq(uint8_t channel, uint16_t freq)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.hpFreq = freq;
    data.hpSlope = (uint8_t)mChannelDataMaps[channel]["hpSlope"].toUInt();
    data.hpType = (uint8_t)mChannelDataMaps[channel]["hpType"].toUInt();
    data.hpEnable = (uint8_t)mChannelDataMaps[channel]["hpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("hpFreq", freq);
    emit channelDataMapsValueChanged(channel, "hpFreq", freq);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.hpFreq = freq;
    //     data.hpSlope = (uint8_t)mChannelDataMaps[linkChannel]["hpSlope"].toUInt();
    //     data.hpType = (uint8_t)mChannelDataMaps[linkChannel]["hpType"].toUInt();
    //     data.hpEnable = (uint8_t)mChannelDataMaps[linkChannel]["hpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("hpFreq", freq);
    //     emit channelDataMapsValueChanged(linkChannel, "hpFreq", freq);
    // }
}

void CommonController::setOutputChannelHpSlope(uint8_t channel, uint8_t slope)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.hpFreq = (uint16_t)mChannelDataMaps[channel]["hpFreq"].toUInt();
    data.hpSlope = slope;
    data.hpType = (uint8_t)mChannelDataMaps[channel]["hpType"].toUInt();
    data.hpEnable = (uint8_t)mChannelDataMaps[channel]["hpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("hpSlope", slope);
    emit channelDataMapsValueChanged(channel, "hpSlope", slope);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.hpFreq = (uint16_t)mChannelDataMaps[linkChannel]["hpFreq"].toUInt();
    //     data.hpSlope = slope;
    //     data.hpType = (uint8_t)mChannelDataMaps[linkChannel]["hpType"].toUInt();
    //     data.hpEnable = (uint8_t)mChannelDataMaps[linkChannel]["hpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("hpSlope", slope);
    //     emit channelDataMapsValueChanged(linkChannel, "hpSlope", slope);
    // }
}

void CommonController::setOutputChannelHpType(uint8_t channel, uint8_t type)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.hpFreq = (uint16_t)mChannelDataMaps[channel]["hpFreq"].toUInt();
    data.hpSlope = (uint8_t)mChannelDataMaps[channel]["hpSlope"].toUInt();
    data.hpType = type;
    data.hpEnable = (uint8_t)mChannelDataMaps[channel]["hpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("hpType", type);
    emit channelDataMapsValueChanged(channel, "hpType", type);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.hpFreq = (uint16_t)mChannelDataMaps[linkChannel]["hpFreq"].toUInt();
    //     data.hpSlope = (uint8_t)mChannelDataMaps[linkChannel]["hpSlope"].toUInt();
    //     data.hpType = type;
    //     data.hpEnable = (uint8_t)mChannelDataMaps[linkChannel]["hpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("hpType", type);
    //     emit channelDataMapsValueChanged(linkChannel, "hpType", type);
    // }
}

void CommonController::setOutputChannelHpEnable(uint8_t channel, uint8_t enable)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.hpFreq = (uint16_t)mChannelDataMaps[channel]["hpFreq"].toUInt();
    data.hpSlope = (uint8_t)mChannelDataMaps[channel]["hpSlope"].toUInt();
    data.hpType = (uint8_t)mChannelDataMaps[channel]["hpType"].toUInt();
    data.hpEnable = enable;
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("hpEnable", enable);
    emit channelDataMapsValueChanged(channel, "hpEnable", enable);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.hpFreq = (uint16_t)mChannelDataMaps[linkChannel]["hpFreq"].toUInt();
    //     data.hpSlope = (uint8_t)mChannelDataMaps[linkChannel]["hpSlope"].toUInt();
    //     data.hpType = (uint8_t)mChannelDataMaps[linkChannel]["hpType"].toUInt();
    //     data.hpEnable = enable;
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("hpEnable", enable);
    //     emit channelDataMapsValueChanged(linkChannel, "hpEnable", enable);
    // }
}

void CommonController::setOutputChannelLpFreq(uint8_t channel, uint16_t freq)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.lpFreq = freq;
    data.lpSlope = (uint8_t)mChannelDataMaps[channel]["lpSlope"].toUInt();
    data.lpType = (uint8_t)mChannelDataMaps[channel]["lpType"].toUInt();
    data.lpEnable = (uint8_t)mChannelDataMaps[channel]["lpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("lpFreq", freq);
    emit channelDataMapsValueChanged(channel, "lpFreq", freq);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.lpFreq = freq;
    //     data.lpSlope = (uint8_t)mChannelDataMaps[linkChannel]["lpSlope"].toUInt();
    //     data.lpType = (uint8_t)mChannelDataMaps[linkChannel]["lpType"].toUInt();
    //     data.lpEnable = (uint8_t)mChannelDataMaps[linkChannel]["lpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("lpFreq", freq);
    //     emit channelDataMapsValueChanged(linkChannel, "lpFreq", freq);
    // }
}

void CommonController::setOutputChannelLpSlope(uint8_t channel, uint8_t slope)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.lpFreq = (uint16_t)mChannelDataMaps[channel]["lpFreq"].toUInt();
    data.lpSlope = slope;
    data.lpType = (uint8_t)mChannelDataMaps[channel]["lpType"].toUInt();
    data.lpEnable = (uint8_t)mChannelDataMaps[channel]["lpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("lpSlope", slope);
    emit channelDataMapsValueChanged(channel, "lpSlope", slope);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.lpFreq = (uint16_t)mChannelDataMaps[linkChannel]["lpFreq"].toUInt();
    //     data.lpSlope = slope;
    //     data.lpType = (uint8_t)mChannelDataMaps[linkChannel]["lpType"].toUInt();
    //     data.lpEnable = (uint8_t)mChannelDataMaps[linkChannel]["lpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("lpSlope", slope);
    //     emit channelDataMapsValueChanged(linkChannel, "lpSlope", slope);
    // }
}

void CommonController::setOutputChannelLpType(uint8_t channel, uint8_t type)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.lpFreq = (uint16_t)mChannelDataMaps[channel]["lpFreq"].toUInt();
    data.lpSlope = (uint8_t)mChannelDataMaps[channel]["lpSlope"].toUInt();
    data.lpType = type;
    data.lpEnable = (uint8_t)mChannelDataMaps[channel]["lpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("lpType", type);
    emit channelDataMapsValueChanged(channel, "lpType", type);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.lpFreq = (uint16_t)mChannelDataMaps[linkChannel]["lpFreq"].toUInt();
    //     data.lpSlope = (uint8_t)mChannelDataMaps[linkChannel]["lpSlope"].toUInt();
    //     data.lpType = type;
    //     data.lpEnable = (uint8_t)mChannelDataMaps[linkChannel]["lpEnable"].toUInt();
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("lpType", type);
    //     emit channelDataMapsValueChanged(linkChannel, "lpType", type);
    // }
}

void CommonController::setOutputChannelLpEnable(uint8_t channel, uint8_t enable)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.lpFreq = (uint16_t)mChannelDataMaps[channel]["lpFreq"].toUInt();
    data.lpSlope = (uint8_t)mChannelDataMaps[channel]["lpSlope"].toUInt();
    data.lpType = (uint8_t)mChannelDataMaps[channel]["lpType"].toUInt();
    data.lpEnable = enable;
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[channel].insert("lpEnable", enable);
    emit channelDataMapsValueChanged(channel, "lpEnable", enable);

    // int linkChannel = qAbs(mEqLinkChannels[channel]) - 1;
    // if(0 != (linkChannel + 1))
    // {
    //     ChannelOperation0708 data;
    //     memset(&data, 0xFF, sizeof(data));
    //     data.channel = linkChannel;
    //     data.lpFreq = (uint16_t)mChannelDataMaps[linkChannel]["lpFreq"].toUInt();
    //     data.lpSlope = (uint8_t)mChannelDataMaps[linkChannel]["lpSlope"].toUInt();
    //     data.lpType = (uint8_t)mChannelDataMaps[linkChannel]["lpType"].toUInt();
    //     data.lpEnable = enable;
    //     DataCenterController::getInstance().setCrossOver0207(data);

    //     mChannelDataMaps[linkChannel].insert("lpEnable", enable);
    //     emit channelDataMapsValueChanged(linkChannel, "lpEnable", enable);
    // }
}

void CommonController::setOutputChannelHpLpLinkCopy(uint8_t channel, uint8_t linkChannel)
{
    ChannelOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = linkChannel;
    data.hpFreq = (uint16_t)mChannelDataMaps[channel]["hpFreq"].toUInt();
    data.hpSlope = (uint8_t)mChannelDataMaps[channel]["hpSlope"].toUInt();
    data.hpType = (uint8_t)mChannelDataMaps[channel]["hpType"].toUInt();
    data.hpEnable = (uint8_t)mChannelDataMaps[channel]["hpEnable"].toUInt();
    data.lpFreq = (uint16_t)mChannelDataMaps[channel]["lpFreq"].toUInt();
    data.lpSlope = (uint8_t)mChannelDataMaps[channel]["lpSlope"].toUInt();
    data.lpType = (uint8_t)mChannelDataMaps[channel]["lpType"].toUInt();
    data.lpEnable = (uint8_t)mChannelDataMaps[channel]["lpEnable"].toUInt();
    DataCenterController::getInstance().setCrossOver0207(data);

    mChannelDataMaps[linkChannel].insert("hpFreq", data.hpFreq);
    emit channelDataMapsValueChanged(linkChannel, "hpFreq", data.hpFreq);
    mChannelDataMaps[linkChannel].insert("hpSlope", data.hpSlope);
    emit channelDataMapsValueChanged(linkChannel, "hpSlope", data.hpSlope);
    mChannelDataMaps[linkChannel].insert("hpType", data.hpType);
    emit channelDataMapsValueChanged(linkChannel, "hpType", data.hpType);
    mChannelDataMaps[linkChannel].insert("hpEnable", data.hpEnable);
    emit channelDataMapsValueChanged(linkChannel, "hpEnable", data.hpEnable);
    mChannelDataMaps[linkChannel].insert("lpFreq", data.lpFreq);
    emit channelDataMapsValueChanged(linkChannel, "lpFreq", data.lpFreq);
    mChannelDataMaps[linkChannel].insert("lpSlope", data.lpSlope);
    emit channelDataMapsValueChanged(linkChannel, "lpSlope", data.lpSlope);
    mChannelDataMaps[linkChannel].insert("lpType", data.lpType);
    emit channelDataMapsValueChanged(linkChannel, "lpType", data.lpType);
    mChannelDataMaps[linkChannel].insert("lpEnable", data.lpEnable);
    emit channelDataMapsValueChanged(linkChannel, "lpEnable", data.lpEnable);
}

void CommonController::setMasterGain(uint16_t gain)
{
    AudioOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.mainGain = gain;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("masterGain", gain);
}

void CommonController::setMasterMute(uint8_t mute)
{
    AudioOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.mainMute = mute;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("masterMute", mute);
}

void CommonController::setDspMute(uint8_t mute)
{
    AudioOperation0102 data;
    data.mainMute = 0xFF;
    data.mainGain = 0xFFFF;
    data.dspMute = mute;
    data.dspGain = 0xFF;
    data.bassMute = 0xFF;
    data.bassLevel = 0xFF;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("dspMute", mute);
}

void CommonController::setDspGain(uint8_t gain)
{
    AudioOperation0102 data;
    data.mainMute = 0xFF;
    data.mainGain = 0xFFFF;
    data.dspMute = 0xFF;
    data.dspGain = gain;
    data.bassMute = 0xFF;
    data.bassLevel = 0xFF;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("dspGain", gain);
}

void CommonController::setBassMute(uint8_t mute)
{
    AudioOperation0102 data;
    data.mainMute = 0xFF;
    data.mainGain = 0xFFFF;
    data.dspMute = 0xFF;
    data.dspGain = 0xFF;
    data.bassMute = mute;
    data.bassLevel = 0xFF;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("bassMute", mute);
}

void CommonController::setBassLevel(uint8_t level)
{
    AudioOperation0102 data;
    data.mainMute = 0xFF;
    data.mainGain = 0xFFFF;
    data.dspMute = 0xFF;
    data.dspGain = 0xFF;
    data.bassMute = 0xFF;
    data.bassLevel = level;
    DataCenterController::getInstance().setMainGain0301(data);

    mDataMap.insert("bassLevel", level);
}

void CommonController::setSource(uint8_t source)
{
    AudioOperation0304 data;
    data.source = source;
    data.memory = 0xFF;
    DataCenterController::getInstance().setMusicSource0303(data);

    DataCenterController::getInstance().getMusicSource0304();

    mDataMap.insert("currentSource", source);
}

void CommonController::setMemory(uint8_t memory)
{
    AudioOperation0304 data;
    data.source = mDataMap["currentSource"].toUInt();
    data.memory = memory;
    DataCenterController::getInstance().setMusicSource0303(data);

    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    DataCenterController::getInstance().updateDataByMemoryChange(deviceLevel);

    mDataMap.insert("currentMemory", memory);
    mUiDataMap.insert("selectedMemoryType", (MEMORY_MAX > memory) ? 0 : 1);
    mUiDataMap.insert("selectedMemoryId", (MEMORY_MAX > memory) ? memory : (memory - MEMORY_MAX));
}

void CommonController::setMix(uint8_t source, uint8_t inputChannel, uint8_t outputChannel, uint8_t enable, uint8_t gain)
{
    AudioOperation0506 data;
    data.source = source;
    data.startChannel = inputChannel;
    data.num = 0x01;
    data.gains.clear();
    Mix tempMix;
    tempMix.value = 0xFF;

    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    int outputChannelMax = (1 == deviceLevel) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;
    for(int index = 0; index < outputChannelMax; index++)
    {
        data.gains.push_back(tempMix);
    }
    data.gains[outputChannel].bits.enable = enable;
    data.gains[outputChannel].bits.gain = gain;
    DataCenterController::getInstance().setChannelMix0305(data);

    switch (source) {
    case 0:
        mMixHighDataMaps[inputChannel][outputChannel].insert("enable", enable);
        mMixHighDataMaps[inputChannel][outputChannel].insert("gain", gain);
        break;
    case 1:
        mMixRCADataMaps[inputChannel][outputChannel].insert("enable", enable);
        mMixRCADataMaps[inputChannel][outputChannel].insert("gain", gain);
        break;
    case 2:
        mMixDSPDataMaps[inputChannel][outputChannel].insert("enable", enable);
        mMixDSPDataMaps[inputChannel][outputChannel].insert("gain", gain);
        break;
    default:
        break;
    }
}

void CommonController::setEqFreq(uint8_t channel, uint8_t band, uint16_t freq)
{
    AudioOperation0708 data;
    data.channel = channel;
    data.startBand = band;
    data.num = 0x01;
    data.parms.clear();
    EqParm tempPram;
    tempPram.freq = freq;
    tempPram.qValue = mEqDataMaps[channel][band]["qValue"].toUInt();
    tempPram.gain = mEqDataMaps[channel][band]["gain"].toUInt();
    tempPram.type.bits.type = mEqDataMaps[channel][band]["type"].toUInt();
    tempPram.type.bits.enable = mEqDataMaps[channel][band]["enable"].toUInt();
    data.parms.push_back(tempPram);
    DataCenterController::getInstance().setEQ0307(data);

    mEqDataMaps[channel][band].insert("freq", freq);
    emit eqDataMapsValueChanged(channel, band, "freq", freq);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = freq;
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("freq", freq);
                    emit eqDataMapsValueChanged(index, band, "freq", freq);
                }
            }

            AudioOperation0708 data;
            data.channel = linkChannel;
            data.startBand = band;
            data.num = 0x01;
            data.parms.clear();
            EqParm tempPram;
            tempPram.freq = freq;
            tempPram.qValue = mEqDataMaps[linkChannel][band]["qValue"].toUInt();
            tempPram.gain = mEqDataMaps[linkChannel][band]["gain"].toUInt();
            tempPram.type.bits.type = mEqDataMaps[linkChannel][band]["type"].toUInt();
            tempPram.type.bits.enable = mEqDataMaps[linkChannel][band]["enable"].toUInt();
            data.parms.push_back(tempPram);
            DataCenterController::getInstance().setEQ0307(data);

            mEqDataMaps[linkChannel][band].insert("freq", freq);
            emit eqDataMapsValueChanged(linkChannel, band, "freq", freq);
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = freq;
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("freq", freq);
                    emit eqDataMapsValueChanged(index, band, "freq", freq);
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }
}

void CommonController::setEqQvalue(uint8_t channel, uint8_t band, uint16_t qValue)
{
    AudioOperation0708 data;
    data.channel = channel;
    data.startBand = band;
    data.num = 0x01;
    data.parms.clear();
    EqParm tempPram;
    tempPram.freq = mEqDataMaps[channel][band]["freq"].toUInt();
    tempPram.qValue = qValue;
    tempPram.gain = mEqDataMaps[channel][band]["gain"].toUInt();
    tempPram.type.bits.type = mEqDataMaps[channel][band]["type"].toUInt();
    tempPram.type.bits.enable = mEqDataMaps[channel][band]["enable"].toUInt();
    data.parms.push_back(tempPram);
    DataCenterController::getInstance().setEQ0307(data);

    mEqDataMaps[channel][band].insert("qValue", qValue);
    emit eqDataMapsValueChanged(channel, band, "qValue", qValue);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = qValue;
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("qValue", qValue);
                    emit eqDataMapsValueChanged(index, band, "qValue", qValue);
                }
            }

            AudioOperation0708 data;
            data.channel = linkChannel;
            data.startBand = band;
            data.num = 0x01;
            data.parms.clear();
            EqParm tempPram;
            tempPram.freq = mEqDataMaps[linkChannel][band]["freq"].toUInt();
            tempPram.qValue = qValue;
            tempPram.gain = mEqDataMaps[linkChannel][band]["gain"].toUInt();
            tempPram.type.bits.type = mEqDataMaps[linkChannel][band]["type"].toUInt();
            tempPram.type.bits.enable = mEqDataMaps[linkChannel][band]["enable"].toUInt();
            data.parms.push_back(tempPram);
            DataCenterController::getInstance().setEQ0307(data);

            mEqDataMaps[linkChannel][band].insert("qValue", qValue);
            emit eqDataMapsValueChanged(linkChannel, band, "qValue", qValue);
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = qValue;
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("qValue", qValue);
                    emit eqDataMapsValueChanged(index, band, "qValue", qValue);
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }
}

void CommonController::setEqGain(uint8_t channel, uint8_t band, uint16_t gain)
{
    AudioOperation0708 data;
    data.channel = channel;
    data.startBand = band;
    data.num = 0x01;
    data.parms.clear();
    EqParm tempPram;
    tempPram.freq = mEqDataMaps[channel][band]["freq"].toUInt();
    tempPram.qValue = mEqDataMaps[channel][band]["qValue"].toUInt();
    tempPram.gain = gain;
    tempPram.type.bits.type = mEqDataMaps[channel][band]["type"].toUInt();
    uint8_t enable = (600 == gain) ? 0 : 1;
    tempPram.type.bits.enable = enable;
    data.parms.push_back(tempPram);
    DataCenterController::getInstance().setEQ0307(data);

    mEqDataMaps[channel][band].insert("gain", gain);
    emit eqDataMapsValueChanged(channel, band, "gain", gain);
    mEqDataMaps[channel][band].insert("enable", enable);
    emit eqDataMapsValueChanged(channel, band, "enable", enable);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = gain;
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    uint8_t enable = (600 == gain) ? 0 : 1;
                    tempPram.type.bits.enable = enable;
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("gain", gain);
                    emit eqDataMapsValueChanged(index, band, "gain", gain);
                    mEqDataMaps[index][band].insert("enable", enable);
                    emit eqDataMapsValueChanged(index, band, "enable", enable);

                    if(600 == gain)
                    {
                        uint8_t memory = mDataMap["currentMemory"].toUInt();
                        mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(index).arg(band),
                                                         mUiEqSaveGainMaps[memory][QString("Channel%1Band%2").arg(channel).arg(band)].toUInt());
                    }
                }
            }

            AudioOperation0708 data;
            data.channel = linkChannel;
            data.startBand = band;
            data.num = 0x01;
            data.parms.clear();
            EqParm tempPram;
            tempPram.freq = mEqDataMaps[linkChannel][band]["freq"].toUInt();
            tempPram.qValue = mEqDataMaps[linkChannel][band]["qValue"].toUInt();
            tempPram.gain = gain;
            tempPram.type.bits.type = mEqDataMaps[linkChannel][band]["type"].toUInt();
            uint8_t enable = (600 == gain) ? 0 : 1;
            tempPram.type.bits.enable = enable;
            data.parms.push_back(tempPram);
            DataCenterController::getInstance().setEQ0307(data);

            mEqDataMaps[linkChannel][band].insert("gain", gain);
            emit eqDataMapsValueChanged(linkChannel, band, "gain", gain);
            mEqDataMaps[linkChannel][band].insert("enable", enable);
            emit eqDataMapsValueChanged(linkChannel, band, "enable", enable);

            if(600 == gain)
            {
                uint8_t memory = mDataMap["currentMemory"].toUInt();
                mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(linkChannel).arg(band),
                                                 mUiEqSaveGainMaps[memory][QString("Channel%1Band%2").arg(channel).arg(band)].toUInt());
            }
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = gain;
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    uint8_t enable = (600 == gain) ? 0 : 1;
                    tempPram.type.bits.enable = enable;
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("gain", gain);
                    emit eqDataMapsValueChanged(index, band, "gain", gain);
                    mEqDataMaps[index][band].insert("enable", enable);
                    emit eqDataMapsValueChanged(index, band, "enable", enable);

                    if(600 == gain)
                    {
                        uint8_t memory = mDataMap["currentMemory"].toUInt();
                        mUiEqSaveGainMaps[memory].insert(QString("Channel%1Band%2").arg(index).arg(band),
                                                         mUiEqSaveGainMaps[memory][QString("Channel%1Band%2").arg(channel).arg(band)].toUInt());
                    }
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }
}

void CommonController::setEqEnable(uint8_t channel, uint8_t band, uint8_t enable)
{
    AudioOperation0708 data;
    data.channel = channel;
    data.startBand = band;
    data.num = 0x01;
    data.parms.clear();
    EqParm tempPram;
    tempPram.freq = mEqDataMaps[channel][band]["freq"].toUInt();
    tempPram.qValue = mEqDataMaps[channel][band]["qValue"].toUInt();
    tempPram.gain = mEqDataMaps[channel][band]["gain"].toUInt();
    tempPram.type.bits.type = mEqDataMaps[channel][band]["type"].toUInt();
    tempPram.type.bits.enable = enable;
    data.parms.push_back(tempPram);
    DataCenterController::getInstance().setEQ0307(data);

    mEqDataMaps[channel][band].insert("enable", enable);
    emit eqDataMapsValueChanged(channel, band, "enable", enable);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = enable;
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("enable", enable);
                    emit eqDataMapsValueChanged(index, band, "enable", enable);
                }
            }

            AudioOperation0708 data;
            data.channel = linkChannel;
            data.startBand = band;
            data.num = 0x01;
            data.parms.clear();
            EqParm tempPram;
            tempPram.freq = mEqDataMaps[linkChannel][band]["freq"].toUInt();
            tempPram.qValue = mEqDataMaps[linkChannel][band]["qValue"].toUInt();
            tempPram.gain = mEqDataMaps[linkChannel][band]["gain"].toUInt();
            tempPram.type.bits.type = mEqDataMaps[linkChannel][band]["type"].toUInt();
            tempPram.type.bits.enable = enable;
            data.parms.push_back(tempPram);
            DataCenterController::getInstance().setEQ0307(data);

            mEqDataMaps[linkChannel][band].insert("enable", enable);
            emit eqDataMapsValueChanged(linkChannel, band, "enable", enable);
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = mEqDataMaps[index][band]["type"].toUInt();
                    tempPram.type.bits.enable = enable;
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    mEqDataMaps[index][band].insert("enable", enable);
                    emit eqDataMapsValueChanged(index, band, "enable", enable);
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }
}

void CommonController::setEqType(uint8_t channel, uint8_t band, uint8_t type)
{
    AudioOperation0708 data;
    data.channel = channel;
    data.startBand = band;
    data.num = 0x01;
    data.parms.clear();
    EqParm tempPram;
    tempPram.freq = mEqDataMaps[channel][band]["freq"].toUInt();
    tempPram.qValue = mEqDataMaps[channel][band]["qValue"].toUInt();
    tempPram.gain = mEqDataMaps[channel][band]["gain"].toUInt();
    tempPram.type.bits.type = type;
    tempPram.type.bits.enable = mEqDataMaps[channel][band]["enable"].toUInt();
    data.parms.push_back(tempPram);

    // uint8_t oldType = mEqDataMaps[channel][band]["type"].toUInt();
    // if(((0x00 == oldType) || (0x0B == oldType)) && ((0x07 == type) || (0x08 == type)))
    // {
    //     uint8_t memory = mDataMap["currentMemory"].toUInt();
    //     mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(channel).arg(band), tempPram.qValue);
    //     if(2000 < tempPram.qValue)
    //     {
    //         setEqQvalue(channel, band, mUiEqSaveQMaps[memory][QString("Channel%1Band%2HsLs").arg(channel).arg(band)].toUInt());
    //     }
    // }
    // else if(((0x07 == oldType) || (0x08 == oldType)) && ((0x00 == type) || (0x0B == type)))
    // {
    //     uint8_t memory = mDataMap["currentMemory"].toUInt();
    //     mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(channel).arg(band), tempPram.qValue);
    //     setEqQvalue(channel, band, mUiEqSaveQMaps[memory][QString("Channel%1Band%2PeakAp").arg(channel).arg(band)].toUInt());
    // }

    DataCenterController::getInstance().setEQ0307(data);

    mEqDataMaps[channel][band].insert("type", type);
    emit eqDataMapsValueChanged(channel, band, "type", type);

    if(-1 != mEqLinkChannels[channel].status)
    {
        if(-1 < mEqLinkChannels[channel].status)
        {
            int linkChannel = mEqLinkChannels[channel].status;
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if((mEqLinkChannels[linkChannel].linkedChannels[index]) && (index != channel))
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = type;
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    uint8_t memory = mDataMap["currentMemory"].toUInt();
                    for(int band = 0; band < EQ_BAND_MAX; band++)
                    {
                        mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(index).arg(band),
                                                      mUiEqSaveQMaps[memory][QString("Channel%1Band%2PeakAp").arg(channel).arg(band)].toUInt());
                        mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(index).arg(band),
                                                      mUiEqSaveQMaps[memory][QString("Channel%1Band%2HsLs").arg(channel).arg(band)].toUInt());
                    }

                    mEqDataMaps[index][band].insert("type", type);
                    emit eqDataMapsValueChanged(index, band, "type", type);
                }
            }

            AudioOperation0708 data;
            data.channel = linkChannel;
            data.startBand = band;
            data.num = 0x01;
            data.parms.clear();
            EqParm tempPram;
            tempPram.freq = mEqDataMaps[linkChannel][band]["freq"].toUInt();
            tempPram.qValue = mEqDataMaps[linkChannel][band]["qValue"].toUInt();
            tempPram.gain = mEqDataMaps[linkChannel][band]["gain"].toUInt();
            tempPram.type.bits.type = type;
            tempPram.type.bits.enable = mEqDataMaps[linkChannel][band]["enable"].toUInt();
            data.parms.push_back(tempPram);
            DataCenterController::getInstance().setEQ0307(data);

            uint8_t memory = mDataMap["currentMemory"].toUInt();
            for(int band = 0; band < EQ_BAND_MAX; band++)
            {
                mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(linkChannel).arg(band),
                                              mUiEqSaveQMaps[memory][QString("Channel%1Band%2PeakAp").arg(channel).arg(band)].toUInt());
                mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(linkChannel).arg(band),
                                              mUiEqSaveQMaps[memory][QString("Channel%1Band%2HsLs").arg(channel).arg(band)].toUInt());
            }

            mEqDataMaps[linkChannel][band].insert("type", type);
            emit eqDataMapsValueChanged(linkChannel, band, "type", type);
        }
        else if(-2 == mEqLinkChannels[channel].status)
        {
            for (int index = 0; index < OUTPUT_CHANNEL_MAX; ++index) {
                if(mEqLinkChannels[channel].linkedChannels[index])
                {
                    AudioOperation0708 data;
                    data.channel = index;
                    data.startBand = band;
                    data.num = 0x01;
                    data.parms.clear();
                    EqParm tempPram;
                    tempPram.freq = mEqDataMaps[index][band]["freq"].toUInt();
                    tempPram.qValue = mEqDataMaps[index][band]["qValue"].toUInt();
                    tempPram.gain = mEqDataMaps[index][band]["gain"].toUInt();
                    tempPram.type.bits.type = type;
                    tempPram.type.bits.enable = mEqDataMaps[index][band]["enable"].toUInt();
                    data.parms.push_back(tempPram);
                    DataCenterController::getInstance().setEQ0307(data);

                    uint8_t memory = mDataMap["currentMemory"].toUInt();
                    for(int band = 0; band < EQ_BAND_MAX; band++)
                    {
                        mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2PeakAp").arg(index).arg(band),
                                                      mUiEqSaveQMaps[memory][QString("Channel%1Band%2PeakAp").arg(channel).arg(band)].toUInt());
                        mUiEqSaveQMaps[memory].insert(QString("Channel%1Band%2HsLs").arg(index).arg(band),
                                                      mUiEqSaveQMaps[memory][QString("Channel%1Band%2HsLs").arg(channel).arg(band)].toUInt());
                    }

                    mEqDataMaps[index][band].insert("type", type);
                    emit eqDataMapsValueChanged(index, band, "type", type);
                }
            }
        }
        else
        {
            qDebug()<<"mEqLinkChannels["<<channel<<"].status "<<mEqLinkChannels[channel].status;
        }
    }
}

void CommonController::setEqLinkCopy(uint8_t channel, uint8_t linkChannel)
{
    for(int band = 0; band < EQ_BAND_MAX; band++)
    {
        AudioOperation0708 data;
        data.channel = channel;
        data.startBand = band;
        data.num = 0x01;
        data.parms.clear();

        EqParm tempPram;
        tempPram.freq = mEqDataMaps[linkChannel][band]["freq"].toUInt();
        tempPram.qValue = mEqDataMaps[linkChannel][band]["qValue"].toUInt();
        tempPram.gain = mEqDataMaps[linkChannel][band]["gain"].toUInt();
        tempPram.type.bits.enable = mEqDataMaps[linkChannel][band]["enable"].toUInt();
        tempPram.type.bits.type = mEqDataMaps[linkChannel][band]["type"].toUInt();
        data.parms.push_back(tempPram);

        DataCenterController::getInstance().setEQ0307(data);

        mEqDataMaps[channel][band].insert("type", tempPram.type.bits.type);
        emit eqDataMapsValueChanged(channel, band, "type", tempPram.type.bits.type);
        mEqDataMaps[channel][band].insert("freq", tempPram.freq);
        emit eqDataMapsValueChanged(channel, band, "freq", tempPram.freq);
        mEqDataMaps[channel][band].insert("qValue", tempPram.qValue);
        emit eqDataMapsValueChanged(channel, band, "qValue", tempPram.qValue);
        mEqDataMaps[channel][band].insert("gain", tempPram.gain);
        emit eqDataMapsValueChanged(channel, band, "gain", tempPram.gain);
        mEqDataMaps[channel][band].insert("enable", tempPram.type.bits.enable);
        emit eqDataMapsValueChanged(channel, band, "enable", tempPram.type.bits.enable);

        uint8_t currentMemory = mDataMap["currentMemory"].toUInt();
        mUiEqSaveGainMaps[currentMemory].insert(QString("Channel%1Band%2").arg(channel).arg(band),
                                                mUiEqSaveGainMaps[currentMemory][QString("Channel%1Band%2").arg(linkChannel).arg(band)].toUInt());
        mUiEqSaveQMaps[currentMemory].insert(QString("Channel%1Band%2PeakAp").arg(channel).arg(band),
                                             mUiEqSaveQMaps[currentMemory][QString("Channel%1Band%2PeakAp").arg(linkChannel).arg(band)].toUInt());
        mUiEqSaveQMaps[currentMemory].insert(QString("Channel%1Band%2HsLs").arg(channel).arg(band),
                                             mUiEqSaveQMaps[currentMemory][QString("Channel%1Band%2HsLs").arg(linkChannel).arg(band)].toUInt());
    }
    mUiDataMap.insert(QString("channel%1AllBypassState").arg(channel), mUiDataMap[QString("channel%1AllBypassState").arg(linkChannel)].toUInt());

    ChannelOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.channel = channel;
    data.eqType = mChannelDataMaps[linkChannel]["eqType"].toUInt();
    DataCenterController::getInstance().setOutputChannel0201(data);

    mChannelDataMaps[channel].insert("eqType", data.eqType);
    emit channelDataMapsValueChanged(channel, "eqType", data.eqType);

    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    DataCenterController::getInstance().updateDataByEqTypeChange(deviceLevel);
}

void CommonController::setMemoryEnable(uint8_t memory, uint8_t type, uint8_t enable)
{
    AudioOperation0A0B data;
    data.startMemory = (0 == type) ? memory : (memory + MEMORY_MAX);
    data.type = type;
    data.num = 0x01;
    data.memories.clear();
    MemorySetting tempMemory;
    memset(&tempMemory, 0xFF, sizeof(tempMemory));
    tempMemory.enable = enable;
    memset(&tempMemory.name, 0x00, sizeof(tempMemory.name));
    QString name = mMemorySettingDataMaps[type][memory]["name"].toString();
    // auto fromUtf16 = QStringEncoder(QStringEncoder::Utf16);
    // QByteArray encodedString = fromUtf16(name);
    // memcpy(tempMemory.name, encodedString.data(),
    //        ((32 < encodedString.length()) ? 32 : encodedString.length()));
    memcpy(tempMemory.name, name.toUtf8().data(),
           ((32 < name.toUtf8().length()) ? 32 : name.toUtf8().length()));
    data.memories.push_back(tempMemory);
    DataCenterController::getInstance().setMemory030a(data);

    mMemorySettingDataMaps[type][memory].insert("enable", enable);
    emit memorySettingDataMapsValueChanged(type, memory, "enable", enable);
}

void CommonController::setMemoryName(uint8_t memory, uint8_t type, QString name)
{
    AudioOperation0A0B data;
    data.startMemory = (0 == type) ? memory : (memory + MEMORY_MAX);
    data.type = type;
    data.num = 0x01;
    data.memories.clear();
    MemorySetting tempMemory;
    memset(&tempMemory, 0xFF, sizeof(tempMemory));
    memset(&tempMemory.name, 0x00, sizeof(tempMemory.name));
    // auto fromUtf16 = QStringEncoder(QStringEncoder::Utf16);
    // QByteArray encodedString = fromUtf16(name);
    // memcpy(tempMemory.name, encodedString.data(),
    //        ((32 < encodedString.length()) ? 32 : encodedString.length()));
    memcpy(tempMemory.name, name.toUtf8().data(),
           ((32 < name.toUtf8().length()) ? 32 : name.toUtf8().length()));
    data.memories.push_back(tempMemory);
    DataCenterController::getInstance().setMemory030a(data);

    mMemorySettingDataMaps[type][memory].insert("name", name);
    emit memorySettingDataMapsValueChanged(type, memory, "name", name);
}

void CommonController::setMemoryCopy(uint8_t typeSrc, uint8_t memorySrc, uint8_t typeDes, uint8_t memoryDes, QString name)
{
    AudioOperation0C data;
    data.type = typeSrc;
    data.memorySrc = (0 == typeSrc) ? memorySrc : (memorySrc + MEMORY_MAX);
    data.memoryDes = (0 == typeDes) ? memoryDes : (memoryDes + MEMORY_MAX);
    memset(&data.name, 0x00, sizeof(data.name));
    // auto fromUtf16 = QStringEncoder(QStringEncoder::Utf16);
    // QByteArray encodedString = fromUtf16(name);
    // memcpy(data.name, encodedString.data(),
    //        ((32 < encodedString.length()) ? 32 : encodedString.length()));
    memcpy(data.name, name.toUtf8().data(),
           ((32 < name.toUtf8().length()) ? 32 : name.toUtf8().length()));
    DataCenterController::getInstance().setMemoryCopy030c(data);

    mMemorySettingDataMaps[typeDes][memoryDes].insert("name", name);
    emit memorySettingDataMapsValueChanged(typeDes, memoryDes, "name", name);
}

void CommonController::setDelayGroup(uint8_t group, int delayDiff)
{
    AudioOperation0D data;
    data.delaySettings.clear();

    if(0 != group)
    {
        int outputChannelMax = (1 == mUiDataMap["deviceLevel"].toUInt()) ? OUTPUT_CHANNEL_L_MAX : OUTPUT_CHANNEL_MAX;
        for (int channel = 0; channel < outputChannelMax; channel++) {
            if((mChannelDataMaps[channel]["delayGroup"].toUInt() == group))
            {
                int delay = 0;
                delay = delayDiff + static_cast<int>(mChannelDataMaps[channel]["delay"].toUInt());
                delay = (0 > delay) ? 0 : ((25000 < delay) ? 25000 : delay);

                DelaySetting tempPram;
                tempPram.channel = channel;
                tempPram.delayTime = static_cast<uint16_t>(delay);
                data.delaySettings.push_back(tempPram);
                mChannelDataMaps[channel].insert("delay", static_cast<uint16_t>(delay));
            }
        }
    }
    data.num = data.delaySettings.size();
    DataCenterController::getInstance().setDelayGroup030d(data);
}

void CommonController::setPresetMode(uint8_t mode)
{
    AudioOperation0E0F data;
    data.level = 0xFF;
    data.mode = mode;
    DataCenterController::getInstance().setPreset030e(data);

    mDataMap.insert("presetMode", mode);
    emit dataMapValueChanged("presetMode", mode);
}

void CommonController::setPresetLevel(uint8_t level)
{
    AudioOperation0E0F data;
    data.level = level;
    data.mode = 0xFF;
    DataCenterController::getInstance().setPreset030e(data);

    mDataMap.insert("presetLevel", level);
    emit dataMapValueChanged("presetLevel", level);
}

void CommonController::setDisconnectBt()
{
    DataCenterController::getInstance().setDisconnectBt0310();
}

void CommonController::setNaviMixEnable(uint8_t enable)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.naviMixEnable = enable;
    data.naviSensitivity = (uint8_t)mDataMap["naviSensitivity"].toUInt();
    data.naviAttenuation = (uint8_t)mDataMap["naviAttenuation"].toUInt();
    data.naviDuration = (uint8_t)mDataMap["naviDuration"].toUInt();
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("naviMixEnable", enable);
}

void CommonController::setNaviSensitivity(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.naviMixEnable = (uint8_t)mDataMap["naviMixEnable"].toUInt();
    data.naviSensitivity = value;
    data.naviAttenuation = (uint8_t)mDataMap["naviAttenuation"].toUInt();
    data.naviDuration = (uint8_t)mDataMap["naviDuration"].toUInt();
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("naviSensitivity", value);
}

void CommonController::setNaviAttenuation(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.naviMixEnable = (uint8_t)mDataMap["naviMixEnable"].toUInt();
    data.naviSensitivity = (uint8_t)mDataMap["naviSensitivity"].toUInt();
    data.naviAttenuation = value;
    data.naviDuration = (uint8_t)mDataMap["naviDuration"].toUInt();
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("naviAttenuation", value);
}

void CommonController::setNaviDuration(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.naviMixEnable = (uint8_t)mDataMap["naviMixEnable"].toUInt();
    data.naviSensitivity = (uint8_t)mDataMap["naviSensitivity"].toUInt();
    data.naviAttenuation = (uint8_t)mDataMap["naviAttenuation"].toUInt();
    data.naviDuration = value;
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("naviDuration", value);
}

void CommonController::setExternalAttenuation(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.externalAttenuation = value;
    data.externalPolarity = (uint8_t)mDataMap["externalPolarity"].toUInt();
    data.externalDspAttenuation = (uint8_t)mDataMap["externalDspAttenuation"].toUInt();
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("externalAttenuation", value);
}

void CommonController::setExternalPolarity(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.externalAttenuation = (uint8_t)mDataMap["externalAttenuation"].toUInt();
    data.externalPolarity = value;
    data.externalDspAttenuation = (uint8_t)mDataMap["externalDspAttenuation"].toUInt();
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("externalPolarity", value);
}

void CommonController::setExternalDspAttenuation(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.externalAttenuation = (uint8_t)mDataMap["externalAttenuation"].toUInt();
    data.externalPolarity = (uint8_t)mDataMap["externalPolarity"].toUInt();
    data.externalDspAttenuation = value;
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("externalDspAttenuation", value);
}

void CommonController::setDelayUnit(uint8_t value)
{
    SystemOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.delayUnit = value;
    DataCenterController::getInstance().setSystemConfig0403(data);

    mDataMap.insert("delayUnit", value);
}

void CommonController::setHighGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.highGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("highGain", value);
}

void CommonController::setRcaGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.rcaGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("rcaGain", value);
}

void CommonController::setAuxGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.auxGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("auxGain", value);
}

void CommonController::setBtGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.btGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("btGain", value);
}

void CommonController::setSpdifGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.spdifGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("spdifGain", value);
}

void CommonController::setUsbGain(uint8_t value)
{
    SystemOperation0708 data;
    memset(&data, 0xFF, sizeof(data));
    data.usbGain = value;
    DataCenterController::getInstance().setSystemConfig0407(data);

    mDataMap.insert("usbGain", value);
}

void CommonController::setResetFactory()
{
    DataCenterController::getInstance().setInitialization0409();

    int level = mUiDataMap["deviceLevel"].toInt();
    DataCenterController::getInstance().initData(level);
}

QString CommonController::saveFile()
{
    return mSaveFile;
}

void CommonController::setSaveFile(QString file)
{
    mSaveFile = file;
}

int CommonController::savePackageCount()
{
    return mSavePackageCount;
}

void CommonController::setSavePackageCount(int count)
{
    mSavePackageCount = count;
}

void CommonController::querySaveData()
{
    DataCenterController::getInstance().querySaveData(0);
}

void CommonController::sendLoadData(const QByteArray &data)
{
    int deviceLevel = mUiDataMap["deviceLevel"].toInt();
    int ret = DataCenterController::getInstance().sendLoadData(data, deviceLevel);
    if(-1 == ret)
    {
        emit openWarningPopup(12);
    }
    else if(-2 == ret)
    {
        emit openWarningPopup(13);
    }
}

void CommonController::upgradeDsp(const QByteArray &data)
{
    DataCenterController::getInstance().upgradeDspRemote0502(0, data);
}

void CommonController::upgradeRemote(const QByteArray &data)
{
    DataCenterController::getInstance().upgradeDspRemote0502(2, data);
}

void CommonController::setRemoteBrightness(uint8_t value)
{
    RemoteOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.brightness = value;
    DataCenterController::getInstance().setRemoteBrightness0601(data);

    mDataMap.insert("remoteBrightness", value);
}

void CommonController::setRemoteDimmer(uint8_t value)
{
    RemoteOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.dimmer = value;
    DataCenterController::getInstance().setRemoteBrightness0601(data);

    mDataMap.insert("remoteDimmer", value);
}

void CommonController::setRemotePolarity(uint8_t value)
{
    RemoteOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.polarity = value;
    DataCenterController::getInstance().setRemoteBrightness0601(data);

    mDataMap.insert("remotePolarity", value);
}

void CommonController::setRemoteDimmerBrightness(uint8_t value)
{
    RemoteOperation0102 data;
    memset(&data, 0xFF, sizeof(data));
    data.dimmerBrightness = value;
    DataCenterController::getInstance().setRemoteBrightness0601(data);

    mDataMap.insert("remoteDimmerBrightness", value);
}

void CommonController::setRemoteModel(uint8_t value)
{
    RemoteOperation0304 data;
    memset(&data, 0xFF, sizeof(data));
    data.model = value;
    DataCenterController::getInstance().setRemoteModel0603(data);

    mDataMap.insert("remoteModel", value);
}

void CommonController::setRemoteShortCut(uint8_t id, uint8_t type, uint8_t memory)
{
    RemoteOperation0506 data;
    data.num = 1;
    data.infos.clear();
    RemoteShortCutInfo info;
    info.id = id;
    info.type = type;
    info.memory = ((0 == type) || (1 == type) || (2 == type)) ? memory : (memory + MEMORY_MAX);
    data.infos.push_back(info);
    DataCenterController::getInstance().setRemoteShortCut0605(data);

    mRemoteShortCutMaps[id].insert("type", type);
    mRemoteShortCutMaps[id].insert("memory", memory);
}

// Version information methods implementation
QString CommonController::getVersionString() const
{
    return QString(VERSION_STRING);
}

QString CommonController::getVersionFull() const
{
    return QString(VERSION_FULL);
}

QString CommonController::getVersionTag() const
{
    return QString(VERSION_TAG);
}

QString CommonController::getGitCommit() const
{
    return QString(GIT_COMMIT);
}

QString CommonController::getGitBranch() const
{
    return QString(GIT_BRANCH);
}

QString CommonController::getBuildTimestamp() const
{
    return QString(BUILD_TIMESTAMP);
}

int CommonController::getVersionMajor() const
{
    return VERSION_MAJOR;
}

int CommonController::getVersionMinor() const
{
    return VERSION_MINOR;
}

int CommonController::getVersionPatch() const
{
    return VERSION_PATCH;
}

bool CommonController::isVersionAtLeast(int major, int minor, int patch) const
{
    return VERSION_AT_LEAST(major, minor, patch);
}
