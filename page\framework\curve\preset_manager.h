#pragma once

#include <QObject>
#include <QString>
#include <QMap>
#include <QVector>
#include "datamodel.h"

/**
 * @brief 音效预设管理器
 * 管理不同类型和级别的音效预设，将预设数据映射到32~36号不可见点
 */
class PresetManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 预设类型枚举
     */
    enum PresetType {
        Flat = 0,       // 平坦
        SuperBass = 1,  // 超重低音
        Powerful = 2,   // 强劲
        Vocal = 3,      // 人声
        Natural = 4     // 自然
    };

    /**
     * @brief 预设级别枚举
     */
    enum PresetLevel {
        Level1 = 1,     // 级别1
        Level2 = 2,     // 级别2
        Level3 = 3,     // 级别3
        Level4 = 4,     // 级别4
        Level5 = 5      // 级别5
    };

    /**
     * @brief 预设数据结构
     * 包含5个频率点的增益值，对应32~36号不可见点
     */
    struct PresetData {
        double gain100Hz;   // 100Hz对应32号点
        double gain315Hz;   // 315Hz对应33号点
        double gain1250Hz;  // 1250Hz对应34号点
        double gain3150Hz;  // 3150Hz对应35号点
        double gain8000Hz;  // 8000Hz对应36号点

        // 默认构造函数
        PresetData() : gain100Hz(0.0), gain315Hz(0.0), gain1250Hz(0.0),
                      gain3150Hz(0.0), gain8000Hz(0.0) {}

        // 带参构造函数
        PresetData(double g100, double g315, double g1250, double g3150, double g8000)
            : gain100Hz(g100), gain315Hz(g315), gain1250Hz(g1250),
              gain3150Hz(g3150), gain8000Hz(g8000) {}
    };

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit PresetManager(QObject* parent = nullptr);

    /**
     * @brief 获取预设数据
     * @param type 预设类型
     * @param level 预设级别
     * @return 预设数据，如果不存在则返回默认值（全0）
     */
    PresetData getPresetData(PresetType type, PresetLevel level) const;

    /**
     * @brief 将预设数据转换为不可见点数据
     * @param presetData 预设数据
     * @return 5个不可见点的数据向量（32~36号点）
     */
    QVector<DataModel::AdjustablePointData> convertToInvisiblePoints(const PresetData& presetData) const;

    /**
     * @brief 应用预设到指定通道
     * @param dataModel 数据模型指针
     * @param curveIndex 曲线索引（通道）
     * @param type 预设类型
     * @param level 预设级别
     * @return 是否应用成功
     */
    bool applyPreset(DataModel* dataModel, int curveIndex, PresetType type, PresetLevel level);

    /**
     * @brief 获取预设类型名称
     * @param type 预设类型
     * @return 类型名称字符串
     */
    static QString getPresetTypeName(PresetType type);

    /**
     * @brief 获取预设级别名称
     * @param level 预设级别
     * @return 级别名称字符串
     */
    static QString getPresetLevelName(PresetLevel level);

    /**
     * @brief 从字符串获取预设类型
     * @param typeName 类型名称
     * @return 预设类型，如果不存在则返回Flat
     */
    static PresetType getPresetTypeFromString(const QString& typeName);

    /**
     * @brief 从字符串获取预设级别
     * @param levelName 级别名称
     * @return 预设级别，如果不存在则返回Level1
     */
    static PresetLevel getPresetLevelFromString(const QString& levelName);

    /**
     * @brief 获取所有可用的预设类型
     * @return 预设类型列表
     */
    static QVector<PresetType> getAllPresetTypes();

    /**
     * @brief 获取所有可用的预设级别
     * @return 预设级别列表
     */
    static QVector<PresetLevel> getAllPresetLevels();

signals:
    /**
     * @brief 预设应用成功信号
     * @param curveIndex 曲线索引
     * @param type 预设类型
     * @param level 预设级别
     */
    void presetApplied(int curveIndex, PresetType type, PresetLevel level);

private:
    /**
     * @brief 初始化预设数据
     */
    void initPresetData();

    /**
     * @brief 生成预设键值
     * @param type 预设类型
     * @param level 预设级别
     * @return 键值字符串
     */
    QString generatePresetKey(PresetType type, PresetLevel level) const;

private:
    // 预设数据存储：键为"type_level"，值为PresetData
    QMap<QString, PresetData> m_presets;

    // 频率映射：预设文件中的频率对应32~36号点的实际频率
    static const double PRESET_FREQUENCIES[5];  // 100, 315, 1250, 3150, 8000 Hz
    static const double INVISIBLE_POINT_FREQUENCIES[5];  // 32~36号点的实际频率
    static const double DEFAULT_Q_VALUE;  // 默认Q值
    static const DataModel::EQType DEFAULT_EQ_TYPE;  // 默认EQ类型
};