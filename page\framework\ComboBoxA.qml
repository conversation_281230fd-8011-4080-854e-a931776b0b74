import QtQuick
import QtQuick.Controls.Basic

ComboBox {
    id: control
    implicitHeight: 24
    leftPadding: 6
    rightPadding: 6
    font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
    font.pixelSize: 12
    opacity: enabled ? 1 : 0.3

    function setItemEnabled(index, enable)
    {
        if(isCompleted)
        {
            if(control.popup.contentItem.itemAtIndex(index))
            {
                control.popup.contentItem.itemAtIndex(index).enabled = enable
            }
        }
    }

    property bool isCompleted: false

    Component.onCompleted: {
        isCompleted = true
    }

    delegate: ItemDelegate {
        id: delegate
        implicitWidth: control.width
        implicitHeight: control.height
        highlighted: control.highlightedIndex === index
        leftPadding: control.leftPadding
        rightPadding: control.rightPadding

        required property var model
        required property int index

        contentItem: Item {
            Text {
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font: control.font
                elide: Text.ElideRight
                verticalAlignment: Text.AlignVCenter
                text: delegate.model[control.textRole]
                opacity: enabled ? 1 : 0.3
            }
        }

        background: Rectangle {
            color: delegate.hovered ? "#646870" : "#484C54"
        }
    }

    indicator: Item {
        anchors.verticalCenter: control.verticalCenter
        anchors.right: control.right
        width: 24
        height: control.height
        Image {
            anchors.centerIn: parent
            source: "qrc:/Image/down.png"
        }
    }

    contentItem: Text {
        // height: 8
        color: "#E8E8E8"
        font: control.font
        verticalAlignment: Text.AlignVCenter
        elide: Text.ElideRight
        text: control.displayText
    }

    background: Rectangle {
        color: control.hovered ? "#5C6068" : control.pressed ? "#3C4048" : "#484C54"
        border.color: control.hovered ? "#7C8088" : "#646870"
        border.width: 1
    }

    popup: Popup {
        y: control.height - 1
        width: control.width
        height: Math.min(contentItem.implicitHeight, control.Window.height - topMargin - bottomMargin)
        padding: 1

        contentItem: ListView {
            clip: true
            implicitHeight: contentHeight
            model: control.delegateModel
            currentIndex: control.highlightedIndex
            ScrollIndicator.vertical: ScrollIndicator { }
        }

        background: Rectangle {
            border.width: 1
            border.color: "#646870"
            color: "#484C54"
        }
    }
}
