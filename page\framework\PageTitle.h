#ifndef PAGETITLE_H
#define PAGETITLE_H

#include <QFrame>
#include <QButtonGroup>
#include <QActionGroup>

namespace Ui {
class PageTitle;
}

class PageTitle : public QFrame
{
    Q_OBJECT

public:
    explicit PageTitle(QWidget *parent = nullptr);
    ~PageTitle();

    enum ENDeviceConnection
    {
        DISCONNECTION,
        DEQ2000A,
        DEQ7000A
    };

    enum ENButton
    {
        MIN,
        MAX,
        CLOSE,
        // GENERAL,
        // MEMORY,
        // LANGUAGES,
        JAPANESE,
        ENGLISH,
        DSP_SETTINGS,
        SOURCES,
        UPDATE_PC,
        UPDATE_DSP,
        // UPDATE_REMOTE,
        INFO,
        RESET,
        MEMORY,
        SETTINGS_SAVE,
        SETTINGS_LOAD,
        MIX,
        DELAY
    };

    void setDeviceType(QString type, int iconIndex = 1);
    // ENDeviceConnection deviceConnection();
    // void setDeviceConnection(ENDeviceConnection type);
    void setMenuVisible(<PERSON>N<PERSON><PERSON>on action, bool isVisible);
    void setMenuEnabled(<PERSON>N<PERSON><PERSON><PERSON> action, bool isEnabled);
    bool isMaxChecked();
    void setMaxChecked(bool isChecked);

    void changeEvent(QEvent * event) override;

signals:
    void btnClicked(int);

private:
    Ui::PageTitle *ui;

    // ENDeviceConnection mDeviceConnection;
    QMenu* mLanguageMenu;
    QMap<ENButton, QAction*> mMenuActions;
    QActionGroup* mLangGroup;
    QButtonGroup* mBtnGroup;
};

#endif // PAGETITLE_H
