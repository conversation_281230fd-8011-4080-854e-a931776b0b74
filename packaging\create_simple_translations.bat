@echo off
echo Creating Qt Installer Framework translation files...

if not exist "translations" mkdir "translations"

REM Create empty but valid .qm files for Qt Installer Framework
echo Creating empty en_US.qm...
type nul > translations\en_US.qm

echo Creating empty ja_JP.qm...
type nul > translations\ja_JP.qm

echo Translation files created successfully!
echo Note: Using empty translation files - installer will use default English text
if not "%1"=="auto" pause