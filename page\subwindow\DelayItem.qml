import QtQuick
import QtQuick.Controls.Basic
import "../framework"

Rectangle {
    id: control
    width: 166
    height: width
    color: "transparent"
    border.width: (channel !== dataMap["uiDataMap"]["selectedChannel"]) ? 1 : 2
    border.color: (channel !== dataMap["uiDataMap"]["selectedChannel"]) ? "#5C6068" : colorA[channel]
    opacity: enabled ? 1: 0.3
    visible: (channel > 5) ? ((1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true) : true

    readonly property var colorA: ["#00C8E8", "#00F090", "#D744D6", "#A870FF", "#E0A000",
        "#D0D000", "#0088FF", "#FF80C0", "#00B0A0", "#F05878"]
    readonly property var speakerStr: ["FL-Tweeter", "FL-M＆T", "FL-Full", "FL-Midrange", "FL-M&WF", "FL-Woofer",
        "RL-Tweeter", "RL-Midrange", "RL-Full", "RL-M&WF", "RL-Woofer", "FR-Tweeter",
        "FR-M＆T", "FR-Full", "FR-Midrange", "FR-M&WF", "FR-Woofer", "RR-Tweeter",
        "RR-Midrange", "RR-Full", "RR-M&WF", "RR-Woofer", "L-Subwoofer", "Subwoofer",
        "R-Subwoofer", "C-Full", "C-Woofer", "C-Tweeter", "User Config-1", "User Config-2"]
    property var delayUnitStr: [" cm", " inch", " msec"]

    property int channel: 0
    property bool hpEnabled: dataMap["channel" + channel + "DataMap"]["hpEnable"]
    property bool lpEnabled: dataMap["channel" + channel + "DataMap"]["lpEnable"]
    property int speakId: visible ? dataMap["channel" + channel + "DataMap"]["type"] : 0
    property int gain: visible ? dataMap["channel" + channel + "DataMap"]["gain"] : 0
    property int delayUnit: 0

    property alias uiMute: muteBtn.checked
    property alias uiPhase: phaseBtn.checked
    property alias uiLinkId: linkComboBox.currentIndex
    property int uiDelay: delay

    property bool mute: visible ? (0 !== dataMap["channel" + channel + "DataMap"]["mute"]) : false
    property bool phase: visible ? (0 === dataMap["channel" + channel + "DataMap"]["positiveNegative"]) : false
    property int link: visible ? dataMap["channel" + channel + "DataMap"]["delayGroup"] : -1
    property int delay: visible ? (dataMap["channel" + channel + "DataMap"]["delay"] / 10).toFixed(0) : 0

    readonly property int fromValue: decimalToInt(0, delayInput.decimalFactor)
    readonly property int toValue: decimalToInt(20, delayInput.decimalFactor)

    onDelayUnitChanged: {
        delayInput.setText(uiDelay)
    }

    onUiMuteChanged: {
        if(uiMute !== mute)
        {
            commonCtrl.setOutputChannelMute(channel, (uiMute ? 1 : 0))
        }
    }
    onUiPhaseChanged: {
        if(uiPhase !== phase)
        {
            commonCtrl.setOutputChannelPhase(channel, (uiPhase ? 0 : 1))
        }
    }
    onUiLinkIdChanged: {
        if(uiLinkId !== link)
        {
            commonCtrl.setOutputChannelDelayGroup(channel, uiLinkId)
        }
    }
    onUiDelayChanged: {
        delayInput.value = uiDelay
        delaySlider.value = uiDelay

        if(uiDelay !== delay)
        {
            commonCtrl.setOutputChannelDelay(channel, uiDelay * 10)
        }
    }

    onMuteChanged: {
        if(uiMute !== mute)
        {
            uiMute = mute
        }
    }
    onPhaseChanged: {
        if(uiPhase !== phase)
        {
            uiPhase = phase
        }
    }
    onLinkChanged: {
        uiLinkId = link
    }
    onDelayChanged: {
        uiDelay = delay
    }

    function decimalToInt(realValue, factor) {
        return realValue * factor
    }

    Text {
        x: 10
        y: 12
        height: 8
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "CH"
    }

    Text {
        x: 32
        y: 8
        height: 12
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 16
        font.bold: true
        verticalAlignment: Text.AlignVCenter
        text: channel + 1
    }

    Row {
        id: hplp
        anchors.right: parent.right
        anchors.rightMargin: 10
        anchors.top: parent.top
        anchors.topMargin: 8
        spacing: 2

        Image {
            id: hpImg
            source: control.hpEnabled ? "qrc:/Image/highPassOn.png" : "qrc:/Image/highPassOff.png"
        }

        Image {
            id: lpImg
            source: control.lpEnabled ? "qrc:/Image/lowPassOn.png" : "qrc:/Image/lowPassOff.png"
        }
    }

    ButtonB {
        id: muteBtn
        anchors.left: parent.left
        anchors.leftMargin: 10
        anchors.top: hplp.bottom
        anchors.topMargin: 10
        width: 24
        height: 24
        checkable: true
        checked: false

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: muteBtn.checked ? "qrc:/Image/mute.png" : "qrc:/Image/unmute.png"
            }
        }
    }

    Text {
        id: speakIdText
        anchors.left: muteBtn.right
        anchors.leftMargin: 10
        anchors.verticalCenter: muteBtn.verticalCenter
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: commonCtrl.getSpeakerStr(speakId)
        // text: ("-" === commonCtrl.getSpeakerStr(speakId)) ? "---" : commonCtrl.getSpeakerStr(speakId)
    }

    ComboBoxB {
        id: linkComboBox
        anchors.right: parent.right
        anchors.rightMargin: 10
        anchors.top: muteBtn.bottom
        anchors.topMargin: 7
        width: 53
        height: 24
        model: ["---", "A", "B", "C", "D", "E"]

        contentItem: Text {
            color: "#E8E8E8"
            font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
            font.pixelSize: 12
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            elide: Text.ElideRight
            text: linkComboBox.displayText
        }

        background: Rectangle {
            color: linkComboBox.hovered ? "#6C7078" : linkComboBox.pressed ? "#545860" : "#5C6068"
            border.color: linkComboBox.hovered ? "#7C8088" : "#747880"
            border.width: 1

            Image {
                anchors.left: parent.left
                anchors.leftMargin: 3
                anchors.verticalCenter: parent.verticalCenter
                source: "qrc:/Image/icn_chblk_link.png"
            }
        }
    }

    ButtonB {
        id: phaseBtn
        anchors.right: linkComboBox.left
        anchors.rightMargin: 3
        anchors.verticalCenter: linkComboBox.verticalCenter
        width: 24
        height: 24
        checkable: true
        checked: false

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: phaseBtn.checked ? "qrc:/Image/phaseOn.png" : "qrc:/Image/phaseOff.png"
            }
        }
    }

    Text {
        id: channelGain
        anchors.right: phaseBtn.left
        anchors.rightMargin: 12
        anchors.verticalCenter: phaseBtn.verticalCenter
        height: 8
        color: "#E8E8E8"
        font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: (gain - 600) / 10 + "dB"
    }

    Rectangle {
        anchors.top: linkComboBox.bottom
        anchors.topMargin: 8
        anchors.horizontalCenter: parent.horizontalCenter
        width: 146
        height: 1
        color: "#5C6068"
    }

    SpinBoxB {
        id: delayInput
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: linkComboBox.bottom
        anchors.topMargin: 17
        width: 96
        height: 24

        from: fromValue
        to: toValue
        value: uiDelay

        property int decimals: 2
        property real realValue: value / decimalFactor
        readonly property int decimalFactor: Math.pow(10, decimals)
        readonly property var delayRe: [/(\d{0,3}(\.\d{0,2})?)?/, /(\d{0,3}(\.\d{0,2})?)?/, /(\d{0,2}(\.\d{0,2})?)?/]

        validator: RegularExpressionValidator { regularExpression: delayInput.delayRe[delayUnit] }
        unit: delayUnitStr[delayUnit]
        // {
        //     switch(delayUnit)
        //     {
        //     case 0:
        //         return " cm"
        //     case 1:
        //         return " inch"
        //     default:
        //         return " msec"
        //     }
        // }

        textFromValue: function(value, locale) {
            switch(delayUnit)
            {
            case 0:
                // return Number(value / decimalFactor * 34).toLocaleString(locale, 'f', (delayInput.decimals)) + delayUnitStr[delayUnit]
                // return (value / decimalFactor * 34).toFixed(decimals).toString() + " " + delayUnitStr[delayUnit]
                return (value / decimalFactor * 34).toFixed(decimals).toString()
            case 1:
                // return (value / decimalFactor * 34 / 2.54).toFixed(decimals).toString() + " " + delayUnitStr[delayUnit]
                return (value / decimalFactor * 34 / 2.54).toFixed(decimals).toString()
            default:
                // return (value / decimalFactor).toFixed(decimals).toString() + " " + delayUnitStr[2]
                return (value / decimalFactor).toFixed(decimals).toString()
            }
        }

        valueFromText: function(text, locale) {
            // let re = delayRe[2]
            // if(delayUnit < 3)
            // {
            //     re = delayRe[delayUnit]
            // }
            switch(delayUnit)
            {
            case 0:
                // return (Number(re.exec(text)[1]) * decimalFactor / 34).toFixed(0)
                return (Number(text) * decimalFactor / 34).toFixed(0)
            case 1:
                // return (Number(re.exec(text)[1]) * decimalFactor / 34 * 2.54).toFixed(0)
                return (Number(text) * decimalFactor / 34 * 2.54).toFixed(0)
            default:
                // return Number(re.exec(text)[1]) * decimalFactor
                return Number(text) * decimalFactor
            }
        }

        onValueChanged: {
            uiDelay = value
        }
    }

    SliderB {
        id: delaySlider
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: delayInput.bottom
        anchors.topMargin: 3
        width: 96
        height: 30

        from: fromValue
        to: toValue
        value: uiDelay
        stepSize: 1
        snapMode: Slider.SnapAlways

        onValueChanged: {
            uiDelay = Math.round(value)
        }
    }

    ButtonB {
        id: gainMinusBtn
        anchors.right: delaySlider.left
        anchors.rightMargin: 8
        anchors.top: delaySlider.top
        anchors.topMargin: 7
        width: 17
        height: width
        autoRepeat: true

        onClicked: {
            delaySlider.decrease()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_minus.png"
            }
        }
    }

    ButtonB {
        id: delayPlusBtn
        anchors.left: delaySlider.right
        anchors.leftMargin: 8
        anchors.top: delaySlider.top
        anchors.topMargin: 7
        width: 17
        height: width
        autoRepeat: true

        onClicked: {
            delaySlider.increase()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_plus.png"
            }
        }
    }

}
