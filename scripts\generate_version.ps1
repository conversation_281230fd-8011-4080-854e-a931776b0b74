param(
    [string]$OutputDir = "page"
)

# Generate version.h from git tag information
Write-Host "[INFO] Generating version.h..." -ForegroundColor Green

$VersionFile = Join-Path $OutputDir "version.h"

try {
    # Get git tag version
    $VersionTag = git describe --tags --abbrev=0 2>$null
    if ($LASTEXITCODE -ne 0) {
        # No tags found, use default version
        $VersionTag = "v1.0.0"
        $VersionMajor = 1
        $VersionMinor = 0
        $VersionPatch = 0
        Write-Host "[WARNING] No git tags found, using default version: 1.0.0" -ForegroundColor Yellow
    } else {
        # Remove v or V prefix if present
        $VersionClean = $VersionTag -replace '^[vV]', ''
        
        # Parse version components (assuming format: major.minor.patch)
        $VersionParts = $VersionClean -split '\.'
        $VersionMajor = [int]($VersionParts[0] -replace '\D', '')
        $VersionMinor = if ($VersionParts.Length -gt 1) { [int]($VersionParts[1] -replace '\D', '') } else { 0 }
        $VersionPatch = if ($VersionParts.Length -gt 2) { [int]($VersionParts[2] -replace '\D', '') } else { 0 }
        
        Write-Host "[INFO] Using git tag version: $VersionTag ($VersionMajor.$VersionMinor.$VersionPatch)" -ForegroundColor Green
    }
} catch {
    Write-Host "[ERROR] Failed to get git tag: $_" -ForegroundColor Red
    $VersionTag = "v1.0.0"
    $VersionMajor = 1
    $VersionMinor = 0
    $VersionPatch = 0
}

try {
    # Get git commit hash
    $GitCommit = git rev-parse --short HEAD 2>$null
    if ($LASTEXITCODE -ne 0) {
        $GitCommit = "unknown"
        Write-Host "[WARNING] Could not get git commit hash" -ForegroundColor Yellow
    }
} catch {
    $GitCommit = "unknown"
    Write-Host "[WARNING] Could not get git commit hash: $_" -ForegroundColor Yellow
}

try {
    # Get git branch name
    $GitBranch = git rev-parse --abbrev-ref HEAD 2>$null
    if ($LASTEXITCODE -ne 0) {
        $GitBranch = "unknown"
        Write-Host "[WARNING] Could not get git branch name" -ForegroundColor Yellow
    }
} catch {
    $GitBranch = "unknown"
    Write-Host "[WARNING] Could not get git branch name: $_" -ForegroundColor Yellow
}

# Get build timestamp
$BuildTimestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$BuildDate = Get-Date -Format "yyyy-MM-dd"
$BuildTime = Get-Date -Format "HH:mm:ss"

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Generate version.h file content
$VersionContent = @"
// Auto-generated version header file
// Generated on: $BuildTimestamp
// Do not edit this file manually!

#ifndef VERSION_H
#define VERSION_H

// Version information from git tag
#define VERSION_MAJOR $VersionMajor
#define VERSION_MINOR $VersionMinor
#define VERSION_PATCH $VersionPatch

// Version strings
#define VERSION_STRING "$VersionMajor.$VersionMinor.$VersionPatch"
#define VERSION_TAG "$VersionTag"
#define VERSION_FULL "$VersionTag ($GitCommit)"

// Git information
#define GIT_COMMIT "$GitCommit"
#define GIT_BRANCH "$GitBranch"

// Build information
#define BUILD_TIMESTAMP "$BuildTimestamp"
#define BUILD_DATE "$BuildDate"
#define BUILD_TIME "$BuildTime"

// Convenience macros
#define VERSION_AT_LEAST(major, minor, patch) ( \
    (VERSION_MAJOR > (major)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR > (minor)) || \
    (VERSION_MAJOR == (major) && VERSION_MINOR == (minor) && VERSION_PATCH >= (patch)) \
)

#endif // VERSION_H
"@

# Write version.h file
try {
    $VersionContent | Out-File -FilePath $VersionFile -Encoding UTF8
    Write-Host "[INFO] Version header generated: $VersionFile" -ForegroundColor Green
    Write-Host "[INFO] Version: $VersionMajor.$VersionMinor.$VersionPatch" -ForegroundColor Green
    Write-Host "[INFO] Git commit: $GitCommit" -ForegroundColor Green
    Write-Host "[INFO] Git branch: $GitBranch" -ForegroundColor Green
    Write-Host "[INFO] Build time: $BuildTimestamp" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to write version file: $_" -ForegroundColor Red
    exit 1
}
