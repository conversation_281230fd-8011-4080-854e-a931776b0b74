#ifndef MID02HANDLER_H
#define MID02HANDLER_H

#include "DataHandlerAbstract.h"

class Mid02Handler : public DataHandlerAbstract
{
    Q_OBJECT
public:
    explicit Mid02Handler();

    virtual void parseReceivedData(uint8_t id, const QByteArray &data);

    void onInit(int deviceLevel);

    QByteArray send01Data(const ChannelOperation0102 &data);
    QByteArray send02Data(uint8_t channel);
    QByteArray send07Data(const ChannelOperation0708 &data);
    QByteArray send08Data(uint8_t channel);

    const QVector<ChannelOperation0102>& getChannelOperation0102Vector();
    const QVector<ChannelOperation0708>& getChannelOperation0708Vector();

signals:
    void outputChannelTypeChanged(uint8_t channel, uint16_t type);
    void outputChannelGainChanged(uint8_t channel, uint16_t gain);
    void outputChannelPositiveNegativeChanged(uint8_t channel, uint8_t positiveNegative);
    void outputChannelMuteChanged(uint8_t channel, uint8_t mute);
    void outputChannelDelayChanged(uint8_t channel, uint16_t delay);
    void outputChannelDelayGroupChanged(uint8_t channel, uint8_t group);
    void outputChannelEqSetChanged(uint8_t channel, uint8_t eqSet);
    void outputChannelEqTypeChanged(uint8_t channel, uint8_t eqType);
    void outputChannelLinkTypeChanged(uint8_t channel, uint8_t linkType);
    void outputChannelLinkChannelChanged(uint8_t channel, uint8_t linkChannel);

    void outputChannelHpFreqChanged(uint8_t channel, uint16_t hpFreq);
    void outputChannelHpSlopeChanged(uint8_t channel, uint8_t hpSlope);
    void outputChannelHpTypeChanged(uint8_t channel, uint8_t hpType);
    void outputChannelHpEnableChanged(uint8_t channel, uint8_t hpEnable);
    void outputChannelLpFreqChanged(uint8_t channel, uint16_t lpFreq);
    void outputChannelLpSlopeChanged(uint8_t channel, uint8_t lpSlope);
    void outputChannelLpTypeChanged(uint8_t channel, uint8_t lpType);
    void outputChannelLpEnableChanged(uint8_t channel, uint8_t lpEnable);

private:
    void parse02Data(const QByteArray &data);
    void parse08Data(const QByteArray &data);

private:
    QVector<ChannelOperation0102> mChannelOperation02;
    QVector<ChannelOperation0708> mChannelOperation08;
};

#endif // MID02HANDLER_H
