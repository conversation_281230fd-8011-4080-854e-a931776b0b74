import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    id: control
    width: 166
    height: 134
    color: "#3C4048"
    border.width: (channel === dataMap["uiDataMap"]["selectedChannel"]) ? 2 : 1
    border.color: (1 === control.border.width) ? "#5C6068" : colorA[channel]
    visible: (6 > modelData) || (1 !== dataMap["uiDataMap"]["deviceLevel"])

    property int channel: 0

    readonly property var colorA: ["#00C8E8", "#00F090", "#D744D6", "#A870FF", "#E0A000",
        "#D0D000", "#0088FF", "#FF80C0", "#00B0A0", "#F05878"]

    property bool hpEnabled: dataMap["channel" + channel + "DataMap"]["hpEnable"]
    property bool lpEnabled: dataMap["channel" + channel + "DataMap"]["lpEnable"]

    readonly property int fromValue: decimalToInt(-60, gainInput.decimalFactor)
    readonly property int toValue: decimalToInt(0, gainInput.decimalFactor)

    property alias uiMute: muteBtn.checked
    property alias uiPhase: phaseBtn.checked
    property int uiGain: gain

    property bool mute: visible ? (0 !== dataMap["channel" + channel + "DataMap"]["mute"]) : false
    property bool phase: visible ? (0 === dataMap["channel" + channel + "DataMap"]["positiveNegative"]) : false
    property int gain: dataMap["channel" + channel + "DataMap"]["gain"] - 600

    function decimalToInt(realValue, factor) {
        return realValue * factor
    }

    onUiMuteChanged: {
        if(uiMute !== mute)
        {
            commonCtrl.setOutputChannelMute(channel, (uiMute ? 1 : 0))
        }
    }
    onUiPhaseChanged: {
        if(uiPhase !== phase)
        {
            commonCtrl.setOutputChannelPhase(channel, (uiPhase ? 0 : 1))
        }
    }
    onUiGainChanged: {
        gainInput.value = uiGain
        gainSlider.value = uiGain

        if(uiGain !== gain)
        {
            commonCtrl.setOutputChannelGain(channel, (uiGain + 600))
        }
    }

    onMuteChanged: {
        if(uiMute !== mute)
        {
            uiMute = mute
        }
    }
    onPhaseChanged: {
        if(uiPhase !== phase)
        {
            uiPhase = phase
        }
    }
    onGainChanged: {
        if(uiGain !== gain)
        {
            uiGain = gain
        }
    }

    Button {
        anchors.fill: parent
        z: 1
        visible: (1 === control.border.width)
        WheelHandler {
            onWheel: (event)=> {
                         event.accepted = true
                     }
        }

        background: Item {

        }

        onClicked: dataMap["uiDataMap"]["selectedChannel"] = channel
    }

    Text {
        id: titleCh
        anchors.left: parent.left
        anchors.leftMargin: 10
        anchors.top: parent.top
        anchors.topMargin: 12
        width: 16
        height: 9
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 12
        verticalAlignment: Text.AlignVCenter
        text: "CH"
    }

    Text {
        anchors.left: titleCh.right
        anchors.leftMargin: 6
        anchors.top: parent.top
        anchors.topMargin: 8
        width: 10
        height: 13
        color: "#E8E8E8"
        font.family: "Segoe UI"
        font.pixelSize: 16
        font.weight: 700
        verticalAlignment: Text.AlignVCenter
        text: channel + 1
    }

    Row {
        id: hplp
        anchors.right: parent.right
        anchors.rightMargin: 10
        anchors.top: parent.top
        anchors.topMargin: 8
        spacing: 2

        Image {
            id: hpImg
            source: control.hpEnabled ? "qrc:/Image/highPassOn.png" : "qrc:/Image/highPassOff.png"
        }

        Image {
            id: lpImg
            source: control.lpEnabled ? "qrc:/Image/lowPassOn.png" : "qrc:/Image/lowPassOff.png"
        }
    }

    ButtonB {
        id: muteBtn
        anchors.left: parent.left
        anchors.leftMargin: 10
        anchors.top: hplp.bottom
        anchors.topMargin: 10
        width: 24
        height: 24
        checkable: true
        checked: false

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: muteBtn.checked ? "qrc:/Image/mute.png" : "qrc:/Image/unmute.png"
            }
        }
    }

    Button {
        id: spBtn
        anchors.left: muteBtn.right
        anchors.leftMargin: 3
        anchors.verticalCenter: muteBtn.verticalCenter
        width: 119
        height: 24
        font.family: "Segoe UI"
        font.pixelSize: 12
        leftPadding: 6
        rightPadding: 24
        opacity: enabled ? 1 : 0.3
        text: commonCtrl.getSpeakerStr(dataMap["channel" + channel + "DataMap"]["type"])

        contentItem: Text {
            color: "#E8E8E8"
            font: spBtn.font
            verticalAlignment: Text.AlignVCenter
            text: spBtn.text
        }

        background: Rectangle {
            color: spBtn.hovered ? "#6C7078" : spBtn.pressed ? "#545860" : "#5C6068"
            border.color: spBtn.hovered ? "#7C8088" : "#747880"
            border.width: 1

            Image {
                anchors.right: parent.right
                anchors.rightMargin: 6
                anchors.verticalCenter: parent.verticalCenter
                source: "qrc:/Image/down.png"
            }
        }

        onClicked: spMenu.open()
    }

    MyMenu {
        id: spMenu
        x: spBtn.x
        y: spBtn.y + spBtn.height
        width: spBtn.width
        closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutsideParent

        background: Rectangle {
            color: "#484C54"
            border.width: 1
            border.color: "#646870"
        }

        Action {
            text: "---"
            readonly property int speakerEnumId: 0x0000
            onTriggered: {
                // spBtn.text = commonCtrl.getSpeakerStr(speakerEnumId)
                commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
            }
        }

        MyMenu {
            title: "Front"
            width: spBtn.width

            MyMenu {
                title: "Left"
                width: spBtn.width

                Action {
                    id: flTweeter
                    text: "Tweeter"
                    readonly property int speakerEnumId: 0x0101
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: flMidrange
                    text: "Midrange"
                    readonly property int speakerEnumId: 0x0201
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: flWoofer
                    text: "Woofer"
                    readonly property int speakerEnumId: 0x0301
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: flM_T
                    text: "Midrange Tweeter"
                    readonly property int speakerEnumId: 0x0401
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: flM_WF
                    text: "Midrange Woofer"
                    readonly property int speakerEnumId: 0x0501
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: flFull
                    text: "Full"
                    readonly property int speakerEnumId: 0x0601
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
            }

            MyMenu {
                title: "Right"
                width: spBtn.width

                Action {
                    id: frTweeter
                    text: "Tweeter"
                    readonly property int speakerEnumId: 0x0102
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: frMidrange
                    text: "Midrange"
                    readonly property int speakerEnumId: 0x0202
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: frWoofer
                    text: "Woofer"
                    readonly property int speakerEnumId: 0x0302
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: frM_T
                    text: "Midrange Tweeter"
                    readonly property int speakerEnumId: 0x0402
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: frM_WF
                    text: "Midrange Woofer"
                    readonly property int speakerEnumId: 0x0502
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: frFull
                    text: "Full"
                    readonly property int speakerEnumId: 0x0602
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
            }
        }

        MyMenu {
            title: "Rear"
            width: spBtn.width

            MyMenu {
                title: "Left"
                width: spBtn.width

                Action {
                    id: rlTweeter
                    text: "Tweeter"
                    readonly property int speakerEnumId: 0x0103
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rlMidrange
                    text: "Midrange"
                    readonly property int speakerEnumId: 0x0203
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rlWoofer
                    text: "Woofer"
                    readonly property int speakerEnumId: 0x0303
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rlM_WF
                    text: "Midrange Woofer"
                    readonly property int speakerEnumId: 0x0503
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rlFull
                    text: "Full"
                    readonly property int speakerEnumId: 0x0603
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
            }

            MyMenu {
                title: "Right"
                width: spBtn.width

                Action {
                    id: rrTweeter
                    text: "Tweeter"
                    readonly property int speakerEnumId: 0x0104
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rrMidrange
                    text: "Midrange"
                    readonly property int speakerEnumId: 0x0204
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rrWoofer
                    text: "Woofer"
                    readonly property int speakerEnumId: 0x0304
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rrM_WF
                    text: "Midrange Woofer"
                    readonly property int speakerEnumId: 0x0504
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
                Action {
                    id: rrFull
                    text: "Full"
                    readonly property int speakerEnumId: 0x0604
                    onTriggered: {
                        commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                    }
                }
            }
        }

        MyMenu {
            title: "Center"
            width: spBtn.width

            Action {
                id: cTweeter
                text: "Tweeter"
                readonly property int speakerEnumId: 0x0105
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
            Action {
                id: cM_WF
                text: "Midrange Woofer"
                readonly property int speakerEnumId: 0x0305
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
            Action {
                id: cFull
                text: "Full"
                readonly property int speakerEnumId: 0x0605
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
        }

        MyMenu {
            title: "Subwoofer"
            width: spBtn.width

            Action {
                id: lSubwoofer
                text: "L-Subwoofer"
                readonly property int speakerEnumId: 0x0706
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
            Action {
                id: rSubwoofer
                text: "R-Subwoofer"
                readonly property int speakerEnumId: 0x0707
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
            Action {
                id: subwoofer
                text: "Subwoofer"
                readonly property int speakerEnumId: 0x0700
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
        }

        MyMenu {
            title: "User config"
            width: spBtn.width

            Action {
                id: userConfig1
                text: "User config-1"
                readonly property int speakerEnumId: 0x0800
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
            Action {
                id: userConfig2
                text: "User config-2"
                readonly property int speakerEnumId: 0x0900
                onTriggered: {
                    commonCtrl.setHpLpFreqBySpeakerChanged(channel, speakerEnumId)
                }
            }
        }

        Connections {
            target: commonCtrl
            function onSpeakerChanged(channel, speaker, isEnabled) {
                if(channel === control.channel)
                {
                    switch(speaker) {
                    case flTweeter.speakerEnumId:
                    {
                        flTweeter.enabled = isEnabled
                        break
                    }
                    case flMidrange.speakerEnumId:
                    {
                        flMidrange.enabled = isEnabled
                        break
                    }
                    case flWoofer.speakerEnumId:
                    {
                        flWoofer.enabled = isEnabled
                        break
                    }
                    case flM_T.speakerEnumId:
                    {
                        flM_T.enabled = isEnabled
                        break
                    }
                    case flM_WF.speakerEnumId:
                    {
                        flM_WF.enabled = isEnabled
                        break
                    }
                    case flFull.speakerEnumId:
                    {
                        flFull.enabled = isEnabled
                        break
                    }
                    case frTweeter.speakerEnumId:
                    {
                        frTweeter.enabled = isEnabled
                        break
                    }
                    case frMidrange.speakerEnumId:
                    {
                        frMidrange.enabled = isEnabled
                        break
                    }
                    case frWoofer.speakerEnumId:
                    {
                        frWoofer.enabled = isEnabled
                        break
                    }
                    case frM_T.speakerEnumId:
                    {
                        frM_T.enabled = isEnabled
                        break
                    }
                    case frM_WF.speakerEnumId:
                    {
                        frM_WF.enabled = isEnabled
                        break
                    }
                    case frFull.speakerEnumId:
                    {
                        frFull.enabled = isEnabled
                        break
                    }
                    case rlTweeter.speakerEnumId:
                    {
                        rlTweeter.enabled = isEnabled
                        break
                    }
                    case rlMidrange.speakerEnumId:
                    {
                        rlMidrange.enabled = isEnabled
                        break
                    }
                    case rlWoofer.speakerEnumId:
                    {
                        rlWoofer.enabled = isEnabled
                        break
                    }
                    case rlM_WF.speakerEnumId:
                    {
                        rlM_WF.enabled = isEnabled
                        break
                    }
                    case rlFull.speakerEnumId:
                    {
                        rlFull.enabled = isEnabled
                        break
                    }
                    case rrTweeter.speakerEnumId:
                    {
                        rrTweeter.enabled = isEnabled
                        break
                    }
                    case rrMidrange.speakerEnumId:
                    {
                        rrMidrange.enabled = isEnabled
                        break
                    }
                    case rrWoofer.speakerEnumId:
                    {
                        rrWoofer.enabled = isEnabled
                        break
                    }
                    case rrM_WF.speakerEnumId:
                    {
                        rrM_WF.enabled = isEnabled
                        break
                    }
                    case rrFull.speakerEnumId:
                    {
                        rrFull.enabled = isEnabled
                        break
                    }
                    case cTweeter.speakerEnumId:
                    {
                        cTweeter.enabled = isEnabled
                        break
                    }
                    case cM_WF.speakerEnumId:
                    {
                        cM_WF.enabled = isEnabled
                        break
                    }
                    case cFull.speakerEnumId:
                    {
                        cFull.enabled = isEnabled
                        break
                    }
                    case lSubwoofer.speakerEnumId:
                    {
                        lSubwoofer.enabled = isEnabled
                        break
                    }
                    case rSubwoofer.speakerEnumId:
                    {
                        rSubwoofer.enabled = isEnabled
                        break
                    }
                    case subwoofer.speakerEnumId:
                    {
                        subwoofer.enabled = isEnabled
                        break
                    }
                    case userConfig1.speakerEnumId:
                    {
                        userConfig1.enabled = isEnabled
                        break
                    }
                    case userConfig2.speakerEnumId:
                    {
                        userConfig2.enabled = isEnabled
                        break
                    }
                    }
                }
            }
        }
    }

    SpinBoxB {
        id: gainInput
        anchors.left: parent.left
        anchors.leftMargin: 10
        anchors.top: muteBtn.bottom
        anchors.topMargin: 7
        width: 63
        height: 24

        from: fromValue
        to: toValue
        value: uiGain

        property int decimals: 1
        property real realValue: value / decimalFactor
        readonly property int decimalFactor: Math.pow(10, decimals)

        validator: RegularExpressionValidator { regularExpression: /-?(\d{0,2}(\.\d?)?)?/ }
        unit: "dB"

        textFromValue: function(value, locale) {
            return (value / decimalFactor).toFixed(decimals).toString()
        }

        valueFromText: function(text, locale) {
            return Number(text) * decimalFactor
        }

        onValueChanged: {
            uiGain = value
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                // case Qt.Key_Right:
                                {
                                    gainInput.increase()
                                    event.accepted = true
                                    break
                                }
                                case Qt.Key_Down:
                                // case Qt.Key_Left:
                                {
                                    gainInput.decrease()
                                    event.accepted = true
                                    break
                                }
                            }
                        }
    }

    ButtonB {
        id: phaseBtn
        anchors.left: gainInput.right
        anchors.leftMargin: 3
        anchors.verticalCenter: gainInput.verticalCenter
        width: 24
        height: 24
        checkable: true
        checked: false

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: phaseBtn.checked ? "qrc:/Image/phaseOn.png" : "qrc:/Image/phaseOff.png"
            }
        }
    }

    Button {
        id: linkBtn
        anchors.left: phaseBtn.right
        anchors.leftMargin: 3
        anchors.verticalCenter: phaseBtn.verticalCenter
        width: 53
        height: 24
        font.family: "Segoe UI"
        font.pixelSize: 12
        opacity: enabled ? 1 : 0.3
        enabled: (2 !== dataMap["channel" + channel + "DataMap"]["linkType"])

        background: Rectangle {
            color: control.hovered ? "#6C7078" : control.pressed ? "#545860" : "#5C6068"
            border.color: control.hovered ? "#7C8088" : "#747880"
            border.width: 1

            Image {
                id: linkIcon
                anchors.left: parent.left
                anchors.leftMargin: 3
                anchors.verticalCenter: parent.verticalCenter
                source: "qrc:/Image/link.png"
            }

            Image {
                anchors.left: linkIcon.right
                anchors.leftMargin: 2
                anchors.verticalCenter: parent.verticalCenter
                source: (0 === dataMap["channel" + channel + "DataMap"]["linkType"]) ?
                            "qrc:/Image/icn_chblk_normal_arrow.png" :
                            ((2 === dataMap["channel" + channel + "DataMap"]["linkType"]) ?
                                 "qrc:/Image/icn_chblk_link_arrow.png" : "")
                // source:  (2 === dataMap["channel" + channel + "DataMap"]["linkType"]) ?
                //                  "qrc:/Image/icn_chblk_link_arrow.png" : ""
            }

            Text {
                anchors.left: linkIcon.right
                anchors.leftMargin: 2
                anchors.verticalCenter: parent.verticalCenter
                height: 8
                color: "#E8E8E8"
                font: linkBtn.font
                verticalAlignment: Text.AlignVCenter
                text: (1 === dataMap["channel" + channel + "DataMap"]["linkType"]) ?
                          ("CH" + (dataMap["channel" + channel + "DataMap"]["linkChannel"] + 1)) : ""
                // text: (1 === dataMap["channel" + channel + "DataMap"]["linkType"]) ?
                //           ("CH" + (dataMap["channel" + channel + "DataMap"]["linkChannel"] + 1)) :
                //           ((0 === dataMap["channel" + channel + "DataMap"]["linkType"]) ? "---" : "")
            }
        }

        onClicked: {
            dataMap["uiDataMap"]["eqLinkPopupChannel"] = channel
            commonCtrl.eqLinkPopupShown()
        }
    }

    SliderA {
        id: gainSlider
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: gainInput.bottom
        anchors.topMargin: 14
        width: 96
        height: 18

        from: fromValue
        to: toValue
        value: uiGain
        stepSize: 1
        snapMode: Slider.SnapAlways

        onValueChanged: {
            uiGain = Math.round(value)
        }
    }

    ButtonA {
        id: gainMinusBtn
        anchors.right: gainSlider.left
        anchors.rightMargin: 8
        anchors.verticalCenter: gainSlider.verticalCenter
        width: 17
        height: 17
        autoRepeat: true

        onClicked: {
            gainSlider.decrease()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_minus.png"
            }
        }
    }

    ButtonA {
        id: gainPlusBtn
        anchors.left: gainSlider.right
        anchors.leftMargin: 8
        anchors.verticalCenter: gainSlider.verticalCenter

        width: 17
        height: 17
        autoRepeat: true

        onClicked: {
            gainSlider.increase()
        }

        contentItem: Item{
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/icn_btn_plus.png"
            }
        }
    }

}
