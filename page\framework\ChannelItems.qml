import QtQuick
import QtQuick.Controls.Basic
import QtQuick.Layouts

RowLayout {
    implicitWidth: 1062
    height: 134
    spacing: 6

    ButtonA {
        id: leftBtn
        Layout.alignment: Qt.AlignVCenter
        Layout.preferredWidth: 24
        Layout.preferredHeight: 80
        visible: false

        contentItem: Item {
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/pageLeft.png"
            }
        }

        onClicked: {
            btnsView.ScrollBar.horizontal.position = 0
            leftBtn.visible = false
            rightBtn.visible = true
        }
    }

    ScrollView {
        id: btnsView
        Layout.fillWidth: true
        Layout.preferredHeight: 134
        contentHeight: availableHeight
        ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
        ScrollBar.vertical.policy: ScrollBar.AlwaysOff
        ScrollBar.horizontal.interactive: false
        ScrollBar.vertical.interactive: false

        Row {
            spacing: 6

            Repeater {
                model: 10

                ChannelBtn {
                    channel: modelData
                }
            }
        }

        Keys.onPressed: (event)=> {
                            switch(event.key)
                            {
                                case Qt.Key_Up:
                                case Qt.Key_Right:
                                case Qt.Key_Down:
                                case Qt.Key_Left:
                                {
                                    event.accepted = true
                                    break
                                }
                            }
                        }
    }

    ButtonA {
        id: rightBtn
        Layout.alignment: Qt.AlignVCenter
        Layout.preferredWidth: 24
        Layout.preferredHeight: 80
        Layout.rightMargin: 6

        contentItem: Item {
            Image {
                anchors.centerIn: parent
                source: "qrc:/Image/pageRight.png"
            }
        }

        onClicked: {
            btnsView.ScrollBar.horizontal.position = 1
            leftBtn.visible = true
            rightBtn.visible = false
        }
    }
}
