#include <iostream>
#include <cmath>
#include <vector>
#include <iomanip>

// 简化的测试程序，验证shelving filter修复

const double PI = 3.14159265358979323846;

// 模拟DataModel::EQType枚举
enum EQType {
    EQ_TYPE_PEAK = 0x00,
    EQ_TYPE_LS1 = 0x03,
    EQ_TYPE_HS1 = 0x04,
    EQ_TYPE_LS2 = 0x07,
    EQ_TYPE_HS2 = 0x08
};

// 模拟滤波器类型枚举
enum {
    PEQ_PEAKING_FILTER = 0,
    PEQ_LOW_SHELF_FILTER = 1,
    PEQ_HIGH_SHELF_FILTER = 2
};

// 简化的双二阶系数结构
struct BiquadCoeffs {
    double b0, b1, b2;
    double a0, a1, a2;
};

// 简化的calc_iir_filter_coeff函数（仅用于测试shelving filter）
void calc_iir_filter_coeff_test(double fs, int type, double f0, double gain_db, double q_value, BiquadCoeffs* coeff) {
    double omega = 2 * PI * f0 / fs;
    double cos_omega = cos(omega);
    double sin_omega = sin(omega);
    double alpha = sin_omega / (2 * q_value);
    double A = pow(10, gain_db / 40.0); // 平方根倍数
    double beta;

    switch(type) {
        case PEQ_PEAKING_FILTER:
            // 峰值滤波器
            beta = pow(10, gain_db / 40.0);
            coeff->b0 = 1 + alpha * beta;
            coeff->b1 = -2 * cos_omega;
            coeff->b2 = 1 - alpha * beta;
            coeff->a0 = 1 + alpha / beta;
            coeff->a1 = -2 * cos_omega;
            coeff->a2 = 1 - alpha / beta;
            break;
            
        case PEQ_LOW_SHELF_FILTER:
            // 修复后的低架滤波器 - 使用标准Audio EQ Cookbook公式
            beta = 2 * sqrt(A) * alpha;
            coeff->b0 = A * ((A + 1) - (A - 1) * cos_omega + beta);
            coeff->b1 = 2 * A * ((A - 1) - (A + 1) * cos_omega);
            coeff->b2 = A * ((A + 1) - (A - 1) * cos_omega - beta);
            coeff->a0 = (A + 1) + (A - 1) * cos_omega + beta;
            coeff->a1 = -2 * ((A - 1) + (A + 1) * cos_omega);
            coeff->a2 = (A + 1) + (A - 1) * cos_omega - beta;
            break;
            
        case PEQ_HIGH_SHELF_FILTER:
            // 修复后的高架滤波器 - 使用标准Audio EQ Cookbook公式
            beta = 2 * sqrt(A) * alpha;
            coeff->b0 = A * ((A + 1) + (A - 1) * cos_omega + beta);
            coeff->b1 = -2 * A * ((A - 1) + (A + 1) * cos_omega);
            coeff->b2 = A * ((A + 1) + (A - 1) * cos_omega - beta);
            coeff->a0 = (A + 1) - (A - 1) * cos_omega + beta;
            coeff->a1 = 2 * ((A - 1) - (A + 1) * cos_omega);
            coeff->a2 = (A + 1) - (A - 1) * cos_omega - beta;
            break;
    }
}

// EQ类型映射函数（修复后的逻辑）
int mapEQTypeToFilterType(EQType eqType) {
    switch (eqType) {
        case EQ_TYPE_PEAK:
            return PEQ_PEAKING_FILTER;
        case EQ_TYPE_LS1:
        case EQ_TYPE_LS2:
            return PEQ_LOW_SHELF_FILTER;
        case EQ_TYPE_HS1:
        case EQ_TYPE_HS2:
            return PEQ_HIGH_SHELF_FILTER;
        default:
            return PEQ_PEAKING_FILTER;
    }
}

// 计算频率响应幅度
double calculateMagnitudeResponse(const BiquadCoeffs& coeffs, double frequency, double sampleRate) {
    double omega = 2 * PI * frequency / sampleRate;
    
    // 计算复数响应 H(e^jω)
    double cos_omega = cos(omega);
    double sin_omega = sin(omega);
    double cos_2omega = cos(2 * omega);
    double sin_2omega = sin(2 * omega);
    
    // 分子 = b0 + b1*e^(-jω) + b2*e^(-j2ω)
    double num_real = coeffs.b0 + coeffs.b1 * cos_omega + coeffs.b2 * cos_2omega;
    double num_imag = -coeffs.b1 * sin_omega - coeffs.b2 * sin_2omega;
    
    // 分母 = a0 + a1*e^(-jω) + a2*e^(-j2ω)
    double den_real = coeffs.a0 + coeffs.a1 * cos_omega + coeffs.a2 * cos_2omega;
    double den_imag = -coeffs.a1 * sin_omega - coeffs.a2 * sin_2omega;
    
    // |H(e^jω)| = |分子| / |分母|
    double num_mag = sqrt(num_real * num_real + num_imag * num_imag);
    double den_mag = sqrt(den_real * den_real + den_imag * den_imag);
    
    return num_mag / den_mag;
}

int main() {
    std::cout << "测试Shelving Filter修复\n";
    std::cout << "======================\n\n";
    
    double sampleRate = 48000.0;
    double frequency = 1000.0;  // 1kHz
    double gain = 6.0;          // +6dB
    double qValue = 0.707;      // Q = 0.707
    
    std::vector<std::pair<EQType, std::string>> testCases = {
        {EQ_TYPE_PEAK, "Peak"},
        {EQ_TYPE_LS1, "Low Shelf 1st"},
        {EQ_TYPE_LS2, "Low Shelf 2nd"},
        {EQ_TYPE_HS1, "High Shelf 1st"},
        {EQ_TYPE_HS2, "High Shelf 2nd"}
    };
    
    for (const auto& testCase : testCases) {
        EQType eqType = testCase.first;
        std::string typeName = testCase.second;
        
        int filterType = mapEQTypeToFilterType(eqType);
        BiquadCoeffs coeffs;
        calc_iir_filter_coeff_test(sampleRate, filterType, frequency, gain, qValue, &coeffs);
        
        std::cout << "EQ类型: " << typeName << " (0x" << std::hex << eqType << std::dec << ")\n";
        std::cout << "映射为滤波器类型: " << filterType << "\n";
        std::cout << "系数: b0=" << std::fixed << std::setprecision(6) << coeffs.b0 
                  << " b1=" << coeffs.b1 << " b2=" << coeffs.b2 << "\n";
        std::cout << "      a0=" << coeffs.a0 << " a1=" << coeffs.a1 << " a2=" << coeffs.a2 << "\n";
        
        // 测试几个频率点的响应
        std::vector<double> testFreqs = {100, 500, 1000, 2000, 5000, 10000};
        std::cout << "频率响应 (dB):\n";
        for (double freq : testFreqs) {
            double magnitude = calculateMagnitudeResponse(coeffs, freq, sampleRate);
            double magnitudeDB = 20 * log10(magnitude);
            std::cout << "  " << freq << "Hz: " << std::setprecision(2) << magnitudeDB << "dB\n";
        }
        std::cout << "\n";
    }
    
    return 0;
}
