1. 每条曲线中有脱离曲线的31个可选点，显示点的编号，默认y值为0，x按照初始值分布
2. 选中点本身选中后显示红色背景圈，在选中点的下方显示F、Q、G信息，信息之间用“;”隔开，F与值之间用“:”隔开
3. 31个点支持各个方向的拖拽，每个点的位置变化会发送信号，信号包括坐标
4. 31个点支持选中，鼠标按下或点击都会触发选中，31个点之间选中状态互斥，选中状态变化时会对外发送选中焦点变化的信号
5. 高低通滤波的点各有一个，高通显示“H”， 低通显示“L”，不同的线有不同的默认高低通频率值（x值），y值默认显示在-10，纵向不可调整
6. 高低通滤波点也是选中后显示红色圈，高通的频率不能高于低通的频率，低通的频率也不能低于高通的频率
7. 高低通频率变化时需要主动发送信号通知并打印日志
8. 当前选中的点为某条线关联，当前选中的曲线变化时，应该没有点被选中

根据以上需求，自动调整代码，自动设计数据结构、接口和信号
