import QtQuick
import QtQuick.Controls.Basic

Button {
    id: control
    implicitHeight: 24
    font.family: "Segoe UI"
    font.pixelSize: 12
    opacity: enabled ? 1 : 0.3

    contentItem: Text {
        color: "#E8E8E8"
        font: control.font
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        text: control.text
    }

    background: Rectangle {
        color: control.hovered ? "#6C7078" : control.pressed ? "#545860" : "#5C6068"
        border.color: control.hovered ? "#7C8088" : "#747880"
        border.width: 1
    }
}
