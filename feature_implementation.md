# 曲线编辑功能实现文档

## 需求概述
根据feature.md文件的需求，需要实现31个可选点和高低通滤波点的功能。这些点需要与曲线分离，并且支持拖拽和选中操作。

## 实现方案

### 1. 31个可选点的实现
- 修改了`DataModel::initAdjustablePoints`方法，使31个点初始位置的y坐标统一为0
- 点编号从1开始到31，直接显示在点的位置上
- 选中点时使用红色填充，未选中时只显示白色加粗数字

### 2. 可视化增强
- 重构了`CanvasView::paintEvent`方法，明确定义绘制顺序：
  1. 先绘制曲线
  2. 然后绘制曲线上的点
  3. 再绘制高低通滤波点
  4. 最后绘制31个独立可选点

- 优化了`drawAdjustablePoints`方法：
  - 未选中的点只显示白色加粗数字，带黑色阴影提高可读性
  - 选中点显示红色填充圆形，白色数字居中显示
  - 移除了额外的连线和背景区域

- 优化了`drawFilterPoints`方法：
  - 移除未选中时的背景色和圈
  - 使用白色"H"和"L"标记
  - 频率值显示添加背景，避免被截断
  - 频率值精度设置为小数点后一位

### 3. 交互功能
- 实现了点选中状态的互斥性
- 选中点时显示红色背景圈和F、Q、G信息
- 点支持拖拽，并限制在合理范围内
- 高低通滤波点支持拖拽，并相互限制频率范围
- 曲线选中变化时自动清除点选中状态
- 移除了原有的红点功能，简化交互逻辑

### 4. 调试和日志
- 添加了详细的调试日志，确保各组件正确初始化和绘制
- 在构造函数中添加初始化后的强制刷新，确保首次显示正确

## 关键修改文件
1. `page/framework/curve/datamodel.cpp`
   - 修改了点的初始化，统一y坐标为0
   - 添加了点选中状态管理
   - 增强了高低通滤波点的功能

2. `page/framework/curve/canvasview.cpp`
   - 重构了绘制流程
   - 简化了点的可视化效果
   - 优化了交互处理逻辑
   - 移除了红点相关功能

## 实现效果
- 31个可选点显示为白色数字，编号从1到31
- 选中点显示红色填充和白色数字
- 高低通滤波点显示为白色"H"和"L"标记，选中后显示红色填充
- 频率值显示带背景，精度为小数点后一位
- 所有点支持拖拽和选中，选中状态互斥
- 曲线选中变化时正确清除点选中状态