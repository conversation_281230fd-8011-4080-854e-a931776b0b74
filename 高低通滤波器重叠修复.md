# 高低通滤波器重叠修复

## 问题描述

在CanvasView中，高通和低通滤波器之间存在碰撞检测机制，阻止它们重叠或交叉。这种限制在某些音频处理场景中可能不必要，用户希望能够自由设置高通和低通滤波器的位置，即使它们重叠。

## 问题分析

### 原始限制机制

在`page/framework/curve/canvasview.cpp`的`mouseMoveEvent`函数中，存在以下碰撞检测代码：

#### 高通滤波器限制（第582行）
```cpp
// 获取低通点的x值
double lowpassX = m_model->getLowpassPoint(m_draggingFilterCurve).x();

// 确保高通点不超过低通点
newX = qMin(newX, lowpassX - 1.0);  // 至少保持1个单位的距离
```

#### 低通滤波器限制（第613行）
```cpp
// 获取高通点的x值
double highpassX = m_model->getHighpassPoint(m_draggingFilterCurve).x();

// 确保低通点不低于高通点
newX = qMax(newX, highpassX + 1.0);  // 至少保持1个单位的距离
```

### 限制的影响

1. **高通滤波器**：无法拖拽到低通滤波器右侧
2. **低通滤波器**：无法拖拽到高通滤波器左侧
3. **强制间距**：两个滤波器之间必须保持至少1个单位的距离

## 修复方案

### 修复内容

移除了高通和低通滤波器之间的碰撞检测限制，允许它们自由重叠。

#### 修复1：高通滤波器拖拽
**文件**：`page/framework/curve/canvasview.cpp` (第572-583行)

**修复前**：
```cpp
// 获取低通点的x值
double lowpassX = m_model->getLowpassPoint(m_draggingFilterCurve).x();

// 确保高通点不超过低通点
newX = qMin(newX, lowpassX - 1.0);  // 至少保持1个单位的距离
```

**修复后**：
```cpp
// 移除碰撞检测：允许高通和低通滤波器重叠
// 原来的限制：newX = qMin(newX, lowpassX - 1.0);
// 现在允许高通点超过低通点
```

#### 修复2：低通滤波器拖拽
**文件**：`page/framework/curve/canvasview.cpp` (第601-612行)

**修复前**：
```cpp
// 获取高通点的x值
double highpassX = m_model->getHighpassPoint(m_draggingFilterCurve).x();

// 确保低通点不低于高通点
newX = qMax(newX, highpassX + 1.0);  // 至少保持1个单位的距离
```

**修复后**：
```cpp
// 移除碰撞检测：允许高通和低通滤波器重叠
// 原来的限制：newX = qMax(newX, highpassX + 1.0);
// 现在允许低通点低于高通点
```

## 修复效果

### 修复前的行为
- 高通滤波器无法拖拽到低通滤波器右侧
- 低通滤波器无法拖拽到高通滤波器左侧
- 两个滤波器之间必须保持距离

### 修复后的行为
- 高通滤波器可以自由拖拽到任何位置（0-30范围内）
- 低通滤波器可以自由拖拽到任何位置（0-30范围内）
- 两个滤波器可以完全重叠或交叉

## 使用场景

### 允许重叠的好处
1. **特殊滤波效果**：可以创建带通或带阻滤波器效果
2. **灵活性增强**：用户可以根据需要自由配置滤波器
3. **实验性调整**：允许用户尝试各种滤波器组合

### 可能的配置
- **高通在右，低通在左**：创建带阻滤波器效果
- **完全重叠**：两个滤波器在同一频率点
- **部分重叠**：在某个频率范围内同时应用高通和低通

## 保留的限制

修复后仍然保留以下合理的限制：

1. **频率范围限制**：滤波器仍然限制在0-30的x坐标范围内
2. **垂直拖拽限制**：滤波器只能水平拖动，不能垂直移动
3. **可见性控制**：滤波器的显示/隐藏功能保持不变

## 兼容性保证

- ✅ **现有功能不受影响**：其他滤波器功能继续正常工作
- ✅ **UI交互保持一致**：拖拽操作方式不变
- ✅ **数据模型兼容**：底层数据结构和接口不变
- ✅ **向后兼容**：现有的滤波器设置继续有效

## 注意事项

### 用户使用建议
1. **理解滤波器效果**：重叠的高低通滤波器可能产生复杂的频率响应
2. **监听效果**：建议在调整时实时监听音频效果
3. **合理配置**：虽然允许重叠，但仍需根据实际需求合理配置

### 技术考虑
1. **算法层面**：底层滤波算法需要正确处理重叠情况
2. **性能影响**：重叠滤波器可能增加计算复杂度
3. **用户界面**：可能需要添加视觉提示来显示重叠状态

## 总结

这个修复移除了高通和低通滤波器之间不必要的碰撞限制，为用户提供了更大的灵活性。用户现在可以：

1. **自由配置滤波器位置**：不再受到相互位置的限制
2. **创建复杂滤波效果**：通过重叠或交叉配置实现特殊效果
3. **提高工作效率**：减少因位置限制导致的操作困扰

修复保持了系统的稳定性和兼容性，同时为高级用户提供了更多的创作可能性。
