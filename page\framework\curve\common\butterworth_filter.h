#ifndef BUTTERWORTH_FILTER_H
#define BUTTERWORTH_FILTER_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 计算巴特沃斯（Butterworth）滤波器在各频率点的复数响应
 * @param type 滤波器类型（5=低通，6=高通）
 * @param fc 截止频率
 * @param slope_rate 斜率（dB/oct），支持6/12/24/48等
 * @param freq_list 频率点数组
 * @param resp 输出复数响应（累乘）
 *
 * 采用标准多阶二阶节级联，每节极点不同，幅频/相位与专业音频软件一致。
 */
void butterworth_filter_optimized_response(int type, double fc, int slope_rate,
                                           const DoubleArray* freq_list,
                                           double_complex* resp);

/**
 * @brief 计算巴特沃斯高阶滤波器每个二阶节的系数（双线性变换法）
 * @param order 滤波器阶数
 * @param section 二阶节编号，-1表示计算一阶节系数（用于奇数阶滤波器）
 * @param fs 采样率
 * @param fc 截止频率
 * @param coeffs 输出系数结构体
 * @param type 滤波器类型（低通/高通）
 */
void butterworth_section_coeffs(int order, int section, double fs, double fc,
                                BiquadCoeffs* coeffs, int type);

#ifdef __cplusplus
}
#endif

#endif // BUTTERWORTH_FILTER_H