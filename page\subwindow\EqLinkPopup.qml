import QtQuick
import QtQuick.Controls
import "../framework"

SubWindow {
    id: root
    implicitWidth: windowWidth
    implicitHeight: windowHeight
    contentWidth: 400
    contentHeight: 540
    titleStr: "EQ Link"

    property int channel: dataMap["uiDataMap"]["eqLinkPopupChannel"]
    property int linkChannel: 0

    Connections {
        target: commonCtrl
        function onEqLinkPopupShown() {
            var linkstatus = commonCtrl.getEqLinkStatus(channel)
            linkChannel = (-2 === linkstatus) ? -1 : linkstatus
        }
    }

    contentItem: Rectangle {
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"

        Column {
            anchors.left: parent.left
            anchors.leftMargin: 24
            anchors.top: parent.top
            anchors.topMargin: 24
            spacing: 16

            Text {
                width: 336
                height: (lineCount - 1) * lineHeight + 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                wrapMode: Text.WordWrap
                horizontalAlignment: Text.AlignLeft
                verticalAlignment: Text.AlignVCenter
                lineHeight: 21
                lineHeightMode: Text.FixedHeight
                text: qsTr("-EQ Link will replace the current settings with the settings of the linked CH")
            }

            Text {
                width: 336
                height: (lineCount - 1) * lineHeight + 8
                color: "#E8E8E8"
                font.family: (1 === dataMap["uiDataMap"]["language"]) ? "Meiryo UI" : "Segoe UI"
                font.pixelSize: 12
                wrapMode: Text.WordWrap
                horizontalAlignment: Text.AlignLeft
                verticalAlignment: Text.AlignVCenter
                lineHeight: 21
                lineHeightMode: Text.FixedHeight
                text: qsTr("-To remove the EQ link, select “No Assign”.")
            }

            MyRadioButton {
                id: noAssignBtn
                width: 80
                text: "No Assign"
                checked: (-1 === linkChannel)

                onClicked: linkChannel = -1
            }

            Repeater {
                model: 10

                MyRadioButton {
                    id: channelBtn
                    width: 80
                    text: "Ch" + (modelData + 1)
                    enabled: ((channel !== modelData) && (1 !== dataMap["channel" + modelData + "DataMap"]["linkType"]) && (1 !== dataMap["channel" + channel + "DataMap"]["linkType"]))
                    checked: (modelData === linkChannel)
                    visible: (modelData > 5) ? ((1 === dataMap["uiDataMap"]["deviceLevel"]) ? false : true) : true

                    onClicked: linkChannel = modelData
                }
            }
        }
    }

    onClickConfirm: {
        commonCtrl.setEqLink(channel, linkChannel)
        clickClose()
    }

    onClickCancel: clickClose()
}
