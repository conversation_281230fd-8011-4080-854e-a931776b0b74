@echo off
chcp 65001>nul
echo ===================================
echo 清理编译文件
echo ===================================

REM 检查Qt环境
set QT_DIR=C:\Qt\6.8.3
set QT_TOOLS=C:\Qt\Tools
set PATH=%QT_DIR%\mingw_64\bin;%QT_TOOLS%\mingw1120_64\bin;%PATH%

where mingw32-make >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 警告: mingw32-make未找到，将直接删除build目录
) else (
    echo 执行make clean...
    mingw32-make clean
)

echo 删除编译生成的目录...

REM 删除旧的build-qmake目录（如果存在）
if exist build-qmake (
    echo 删除build-qmake目录...
    rd /s /q build-qmake
)

REM 删除新的build目录
if exist build (
    echo 删除build目录...
    rd /s /q build
)

REM 删除其他临时文件
if exist Makefile (
    echo 删除Makefile...
    del Makefile
)
if exist Makefile.* (
    echo 删除Makefile.*...
    del Makefile.*
)
if exist .qmake.stash (
    echo 删除.qmake.stash...
    del .qmake.stash
)

echo ===================================
echo 清理完成
echo ===================================
pause