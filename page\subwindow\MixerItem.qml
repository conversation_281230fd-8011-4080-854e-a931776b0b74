import QtQuick
import QtQuick.Controls
import "../framework"

Item {
    width: 114
    height: 60

    property bool itemVisible: true
    property int sourceType: 0 // 0:"High" 1:"RCA" 2:"DSP"
    property int inputChannel: 0
    property int outputChannel: 0

    property int uiGain: 0
    property alias uiMixEnable: enableComboBox.currentIndex

    property int gain: itemVisible ? dataMap["mix" + ((1 === sourceType) ? "RCA" : ((2 === sourceType) ? "DSP" : "High")) + "In" + inputChannel + "Out" + outputChannel]["gain"] : 0
    // {
    //     switch(sourceType)
    //     {
    //     case 0:
    //         return dataMap["mixHighIn" + inputChannel + "Out" + outputChannel]["gain"]
    //     case 1:
    //         return dataMap["mixRCAIn" + inputChannel + "Out" + outputChannel]["gain"]
    //     case 2:
    //         return dataMap["mixDSPIn" + inputChannel + "Out" + outputChannel]["gain"]
    //     }
    // }

    property int mixEnable: itemVisible ? dataMap["mix" + ((1 === sourceType) ? "RCA" : ((2 === sourceType) ? "DSP" : "High")) + "In" + inputChannel + "Out" + outputChannel]["enable"] : 0
    // {
    //     switch(sourceType)
    //     {
    //     case 0:
    //         return dataMap["mixHighIn" + inputChannel + "Out" + outputChannel]["enable"]
    //     case 1:
    //         return dataMap["mixRCAIn" + inputChannel + "Out" + outputChannel]["enable"]
    //     case 2:
    //         return dataMap["mixDSPIn" + inputChannel + "Out" + outputChannel]["enable"]
    //     }
    // }

    onUiGainChanged: {
        gainInput.value = uiGain
        gainSlider.value = uiGain

        if(uiGain !== gain)
        {
            commonCtrl.setMix(sourceType, inputChannel, outputChannel, uiMixEnable, uiGain)
        }
    }

    onUiMixEnableChanged: {
        if(uiMixEnable !== mixEnable)
        {
            commonCtrl.setMix(sourceType, inputChannel, outputChannel, uiMixEnable, uiGain)
        }
    }

    onGainChanged: {
        uiGain = gain
    }

    onMixEnableChanged: {
        uiMixEnable = mixEnable
    }

    Rectangle {
        anchors.fill: parent
        color: "#3C4048"
        border.width: 1
        border.color: "#5C6068"
        radius: 4
        visible: itemVisible

        ComboBoxA {
            id: enableComboBox
            anchors.left: parent.left
            anchors.leftMargin: 9
            anchors.top: parent.top
            anchors.topMargin: 7
            width: 52
            height: 24
            model: ["OFF", "ON"]
        }

        SpinBoxA {
            id: gainInput
            anchors.left: enableComboBox.right
            anchors.leftMargin: 4
            anchors.top: enableComboBox.top
            width: 40
            height: 24
            inputMethodHints: Qt.ImhDigitsOnly
            enabled: (1 === enableComboBox.currentIndex)

            from: 0
            to: 100
            value: uiGain

            validator: RegularExpressionValidator { regularExpression: /(\d{0,3})?/ }

            textFromValue: function(value, locale) {
                return value.toString()
            }

            valueFromText: function(text, locale) {
                return Number(text)
            }

            onValueChanged: {
                uiGain = value
            }
        }

        SliderA {
            id: gainSlider
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: gainInput.bottom
            width: 80
            height: 30
            enabled: (1 === enableComboBox.currentIndex)

            from: 0
            to: 100
            value: uiGain
            stepSize: 1

            onValueChanged: {
                uiGain = Math.round(value)
            }
        }
    }
}
