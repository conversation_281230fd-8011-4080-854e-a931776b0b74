#include "preset_manager.h"
#include <QDebug>
#include <cmath>

// 静态常量定义
const double PresetManager::PRESET_FREQUENCIES[5] = {100.0, 315.0, 1250.0, 3150.0, 8000.0};
const double PresetManager::INVISIBLE_POINT_FREQUENCIES[5] = {100.0, 315.0, 1250.0, 3150.0, 8000.0};
const double PresetManager::DEFAULT_Q_VALUE = 4.32;
const DataModel::EQType PresetManager::DEFAULT_EQ_TYPE = DataModel::EQ_TYPE_PEAK;

/**
 * @brief 构造函数
 */
PresetManager::PresetManager(QObject* parent)
    : QObject(parent)
{
    qDebug() << "[PresetManager] 初始化预设管理器";
    initPresetData();
}

/**
 * @brief 初始化预设数据
 * 根据preset.txt文件的数据初始化所有预设
 */
void PresetManager::initPresetData()
{
    qDebug() << "[PresetManager] 开始初始化预设数据";

    m_presets["Flat_Level5"] = PresetData(0, 0, 0, 0, 0);
    m_presets["Flat_Level4"] = PresetData(0, 0, 0, 0, 0);
    m_presets["Flat_Level3"] = PresetData(0, 0, 0, 0, 0);
    m_presets["Flat_Level2"] = PresetData(0, 0, 0, 0, 0);
    m_presets["Flat_Level1"] = PresetData(0, 0, 0, 0, 0);


    // SuperBass 预设数据
    m_presets["SuperBass_Level5"] = PresetData(15, -5, 0, 0, 5);
    m_presets["SuperBass_Level4"] = PresetData(12, -4, 0, 0, 4);
    m_presets["SuperBass_Level3"] = PresetData(9, -3, 0, 0, 3);
    m_presets["SuperBass_Level2"] = PresetData(6, -2, 0, 0, 2);
    m_presets["SuperBass_Level1"] = PresetData(3, -1, 0, 0, 1);

    // Powerful 预设数据
    m_presets["Powerful_Level5"] = PresetData(10, -3, 3, -3, 10);
    m_presets["Powerful_Level4"] = PresetData(8, -2, 2, -2, 8);
    m_presets["Powerful_Level3"] = PresetData(6, -2, 2, -2, 6);
    m_presets["Powerful_Level2"] = PresetData(4, -1, 1, -1, 4);
    m_presets["Powerful_Level1"] = PresetData(2, -1, 1, -1, 2);

    // Vocal 预设数据
    m_presets["Vocal_Level5"] = PresetData(0, 8, 3, 0, 8);
    m_presets["Vocal_Level4"] = PresetData(0, 6, 2, 0, 6);
    m_presets["Vocal_Level3"] = PresetData(0, 5, 2, 0, 5);
    m_presets["Vocal_Level2"] = PresetData(0, 3, 1, 0, 3);
    m_presets["Vocal_Level1"] = PresetData(0, 2, 1, 0, 2);

    // Natural 预设数据
    m_presets["Natural_Level5"] = PresetData(5, 3, 3, 0, 3);
    m_presets["Natural_Level4"] = PresetData(4, 2, 2, 0, 2);
    m_presets["Natural_Level3"] = PresetData(3, 2, 2, 0, 2);
    m_presets["Natural_Level2"] = PresetData(2, 1, 1, 0, 1);
    m_presets["Natural_Level1"] = PresetData(1, 1, 1, 0, 1);

    qDebug() << "[PresetManager] 预设数据初始化完成，共" << m_presets.size() << "个预设";

    // 打印所有预设数据
    for (auto it = m_presets.begin(); it != m_presets.end(); ++it) {
        const QString& key = it.key();
        const PresetData& data = it.value();
        qDebug() << "[PresetManager]" << key << ":"
                 << "100Hz=" << data.gain100Hz << "dB"
                 << "315Hz=" << data.gain315Hz << "dB"
                 << "1250Hz=" << data.gain1250Hz << "dB"
                 << "3150Hz=" << data.gain3150Hz << "dB"
                 << "8000Hz=" << data.gain8000Hz << "dB";
    }
}

/**
 * @brief 获取预设数据
 */
PresetManager::PresetData PresetManager::getPresetData(PresetType type, PresetLevel level) const
{
    QString key = generatePresetKey(type, level);
    if (m_presets.contains(key)) {
        return m_presets[key];
    } else {
        qDebug() << "[PresetManager] 警告：未找到预设" << key << "，返回默认值";
        return PresetData(); // 返回默认值（全0）
    }
}

/**
 * @brief 将预设数据转换为不可见点数据
 */
QVector<DataModel::AdjustablePointData> PresetManager::convertToInvisiblePoints(const PresetData& presetData) const
{
    QVector<DataModel::AdjustablePointData> invisiblePoints;

    // 增益数组
    const double gains[5] = {
        presetData.gain100Hz, presetData.gain315Hz, presetData.gain1250Hz,
        presetData.gain3150Hz, presetData.gain8000Hz
    };

    // 为每个频率点创建不可见点数据
    for (int i = 0; i < 5; ++i) {
        DataModel::AdjustablePointData pointData;

        // 设置频率
        pointData.frequency = INVISIBLE_POINT_FREQUENCIES[i];

        // 设置增益
        pointData.gain = gains[i];

        // 设置默认Q值和类型
        pointData.qValue = DEFAULT_Q_VALUE;
        pointData.type = DEFAULT_EQ_TYPE;

        // 计算位置坐标
        // 将频率映射到x坐标
        double x = 0.0;
        if (pointData.frequency >= 20.0 && pointData.frequency <= 40000.0) {
            x = 30.0 * log10(pointData.frequency / 20.0) / log10(40000.0 / 20.0);
        }

        // 将增益直接作为y坐标
        double y = pointData.gain;

        // 限制坐标范围
        x = qBound(0.0, x, 30.0);
        y = qBound(-18.0, y, 18.0);

        pointData.position = QPointF(x, y);

        invisiblePoints.append(pointData);

        qDebug() << "[PresetManager] 创建不可见点" << (i + 32)
                 << ": 频率=" << pointData.frequency << "Hz"
                 << ", 增益=" << pointData.gain << "dB"
                 << ", 位置=(" << x << "," << y << ")";
    }

    return invisiblePoints;
}

/**
 * @brief 应用预设到指定通道
 */
bool PresetManager::applyPreset(DataModel* dataModel, int curveIndex, PresetType type, PresetLevel level)
{
    if (!dataModel) {
        qDebug() << "[PresetManager] 错误：dataModel为空";
        return false;
    }

    // 获取预设数据
    PresetData presetData = getPresetData(type, level);

    // 转换为不可见点数据
    QVector<DataModel::AdjustablePointData> invisiblePoints = convertToInvisiblePoints(presetData);

    if (invisiblePoints.size() != DataModel::INVISIBLE_POINTS_COUNT) {
        qDebug() << "[PresetManager] 错误：不可见点数量不匹配，期望" << DataModel::INVISIBLE_POINTS_COUNT
                 << "个，实际" << invisiblePoints.size() << "个";
        return false;
    }

    // 应用到数据模型
    dataModel->setInvisiblePointsForCurve(curveIndex, invisiblePoints);

    qDebug() << "[PresetManager] 成功应用预设" << getPresetTypeName(type)
             << getPresetLevelName(level) << "到通道" << curveIndex;

    // 发送信号
    emit presetApplied(curveIndex, type, level);

    return true;
}

/**
 * @brief 获取预设类型名称
 */
QString PresetManager::getPresetTypeName(PresetType type)
{
    switch (type) {
    case Flat:      return "Flat";
    case SuperBass: return "SuperBass";
    case Powerful:  return "Powerful";
    case Vocal:     return "Vocal";
    case Natural:   return "Natural";
    default:        return "Unknown";
    }
}

/**
 * @brief 获取预设级别名称
 */
QString PresetManager::getPresetLevelName(PresetLevel level)
{
    switch (level) {
    case Level1: return "Level1";
    case Level2: return "Level2";
    case Level3: return "Level3";
    case Level4: return "Level4";
    case Level5: return "Level5";
    default:     return "Unknown";
    }
}

/**
 * @brief 从字符串获取预设类型
 */
PresetManager::PresetType PresetManager::getPresetTypeFromString(const QString& typeName)
{
    if (typeName.compare("Flat", Qt::CaseInsensitive) == 0) return Flat;
    if (typeName.compare("SuperBass", Qt::CaseInsensitive) == 0) return SuperBass;
    if (typeName.compare("Powerful", Qt::CaseInsensitive) == 0) return Powerful;
    if (typeName.compare("Vocal", Qt::CaseInsensitive) == 0) return Vocal;
    if (typeName.compare("Natural", Qt::CaseInsensitive) == 0) return Natural;

    qDebug() << "[PresetManager] 警告：未知的预设类型" << typeName << "，使用默认值Flat";
    return Flat;
}

/**
 * @brief 从字符串获取预设级别
 */
PresetManager::PresetLevel PresetManager::getPresetLevelFromString(const QString& levelName)
{
    if (levelName.compare("Level1", Qt::CaseInsensitive) == 0) return Level1;
    if (levelName.compare("Level2", Qt::CaseInsensitive) == 0) return Level2;
    if (levelName.compare("Level3", Qt::CaseInsensitive) == 0) return Level3;
    if (levelName.compare("Level4", Qt::CaseInsensitive) == 0) return Level4;
    if (levelName.compare("Level5", Qt::CaseInsensitive) == 0) return Level5;

    qDebug() << "[PresetManager] 警告：未知的预设级别" << levelName << "，使用默认值Level1";
    return Level1;
}

/**
 * @brief 获取所有可用的预设类型
 */
QVector<PresetManager::PresetType> PresetManager::getAllPresetTypes()
{
    return {Flat, SuperBass, Powerful, Vocal, Natural};
}

/**
 * @brief 获取所有可用的预设级别
 */
QVector<PresetManager::PresetLevel> PresetManager::getAllPresetLevels()
{
    return {Level1, Level2, Level3, Level4, Level5};
}

/**
 * @brief 生成预设键值
 */
QString PresetManager::generatePresetKey(PresetType type, PresetLevel level) const
{
    return QString("%1_%2").arg(getPresetTypeName(type)).arg(getPresetLevelName(level));
}