import QtQuick
import QtQuick.Controls.Basic

SpinBox {
    id: control
    implicitHeight: 24
    editable: true
    // wheelEnabled: true
    leftPadding: 6
    rightPadding: 6
    inputMethodHints: Qt.ImhFormattedNumbersOnly
    font.family: "Segoe UI"
    font.pixelSize: 12
    font.weight: 700
    opacity: enabled ? 1 : 0.3

    onValueChanged: {
        contentInput.text = textFromValue(value, locale)
    }

    property string unit: ""

    contentItem: Item {
        property alias text: contentInput.text

        TextInput {
            id: contentInput
            anchors.fill: parent
            color: "#E8E8E8"
            font: control.font
            horizontalAlignment: Qt.AlignHCenter
            verticalAlignment: Qt.AlignVCenter
            readOnly: !control.editable
            validator: control.validator
            inputMethodHints: control.inputMethodHints
            rightPadding: unitTxt.width

            text: control.textFromValue(control.value, control.locale)

            onEditingFinished: {
                // onTextEdited: {
                var modifyText = (("-" === contentInput.text) || ("" === contentInput.text)) ?
                            control.textFromValue(control.value, control.locale) : contentInput.text
                var val = control.valueFromText(modifyText, control.locale)
                if (val < Math.min(control.from, control.to)) {
                    contentInput.text = control.textFromValue(Math.min(control.from, control.to), control.locale)
                }
                else if(val > Math.max(control.from, control.to)) {
                    contentInput.text = control.textFromValue(Math.max(control.from, control.to), control.locale)
                }
                else {
                    contentInput.text = control.textFromValue(val, control.locale)
                }
            }
        }

        Text {
            id: unitTxt
            anchors.left: contentInput.horizontalCenter
            anchors.leftMargin: (contentInput.contentWidth - unitTxt.width) / 2
            anchors.verticalCenter: parent.verticalCenter
            color: "#E8E8E8"
            font: control.font
            verticalAlignment: Qt.AlignVCenter
            text: unit
        }
    }

    background: Rectangle {
        color: "#282828"
        border.width: 1
        border.color: control.hovered ? "#7C8088" : control.pressed ? "#C4C8D0" : "#646870"
    }

    up.indicator: Item {}
    down.indicator: Item {}
}
